# CRM Web System - Cursor Memory Bank Rules

## Project Context
You are working on a CRM Web System built with Next.js 15, TypeScript, Tailwind CSS, Prisma, and PostgreSQL. This project uses the Cursor Memory Bank system for structured development workflows.

## Memory Bank Modes Available

### 🚀 PLAN MODE
Use when you need to create detailed implementation plans for features or fixes.
- Load: `.cursor/custom_modes/plan_instructions.md`
- Purpose: Break down complex tasks, identify components, create implementation strategies
- Output: Detailed plans in `implementation-plan.md` and updated `tasks.md`

### 🎨 CREATIVE MODE  
Use when you need to explore design options or architectural decisions.
- Load: `.cursor/custom_modes/creative_instructions.md`
- Purpose: Generate multiple design options, analyze pros/cons, make architectural decisions
- Output: Design decisions with implementation guidelines

### 🔨 IMPLEMENT MODE
Use when you're ready to build planned features systematically.
- Load: `.cursor/custom_modes/implement_instructions.md`
- Purpose: Execute implementation plans, build features, document progress
- Output: Working code, test results, updated documentation

### 🔍 REFLECT MODE
Use for code review, optimization, and quality assessment.
- Load: `.cursor/custom_modes/reflect_archive_instructions.md`
- Purpose: Review code quality, identify improvements, optimize performance
- Output: Code review reports, optimization recommendations

### 🧠 VAN MODE
Use for general analysis and problem-solving.
- Load: `.cursor/custom_modes/van_instructions.md`
- Purpose: Analyze complex problems, provide insights, general assistance
- Output: Analysis reports, recommendations, insights

## Project-Specific Guidelines

### Code Standards
- Use TypeScript strict mode
- Follow Next.js 15 best practices
- Implement responsive design with Tailwind CSS
- Use Prisma for database operations
- Follow component-based architecture

### File Structure
```
src/
├── app/                 # Next.js app router
├── components/          # Reusable UI components
├── lib/                # Utility functions and configurations
├── types/              # TypeScript type definitions
└── styles/             # Global styles and Tailwind config
```

### Current Priority Tasks
1. **High Priority**: TypeScript error resolution (13 errors)
2. **Medium Priority**: Feature completions (task details, search)
3. **Low Priority**: Advanced features (real-time, integrations)

### Technology Stack Details
- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **UI Components**: Custom components with Framer Motion
- **Styling**: Tailwind CSS with custom design system

### Design System
- **Color Scheme**: Hackathon.dev inspired (darker greys, accent colors)
- **Typography**: Geist Sans font family
- **Components**: Consistent with shadcn/ui patterns
- **Animations**: Framer Motion for smooth interactions
- **Responsive**: Mobile-first approach

## Mode Selection Guidelines

### When to Use PLAN MODE
- Starting work on a new feature
- Breaking down complex requirements
- Need to identify affected components
- Planning multi-phase implementations
- Architectural decision planning

### When to Use CREATIVE MODE
- Designing new UI components
- Exploring algorithm options
- Making architectural decisions
- Solving complex design problems
- Need multiple solution approaches

### When to Use IMPLEMENT MODE
- Have a clear implementation plan
- Ready to write code systematically
- Following established patterns
- Building planned features
- Executing defined tasks

### When to Use REFLECT MODE
- Code review and optimization
- Performance analysis
- Quality assessment
- Refactoring planning
- Post-implementation review

### When to Use VAN MODE
- General problem analysis
- Understanding complex issues
- Exploring possibilities
- Research and investigation
- Brainstorming sessions

## Workflow Integration

### Standard Development Flow
1. **PLAN** → Analyze requirements and create implementation plan
2. **CREATIVE** → Explore design options for complex components
3. **IMPLEMENT** → Build the planned features systematically
4. **REFLECT** → Review, optimize, and document

### Quick Fix Flow
1. **VAN** → Analyze the issue
2. **IMPLEMENT** → Apply targeted fix
3. **REFLECT** → Verify and document

### Feature Development Flow
1. **PLAN** → Break down feature requirements
2. **CREATIVE** → Design UI/UX and architecture
3. **IMPLEMENT** → Build in phases
4. **REFLECT** → Review and optimize

## Project Status Tracking

### Current Status
- **Phase**: Enhancement & Optimization
- **Complexity**: Level 3 (Feature Development)
- **Active Issues**: TypeScript errors, performance optimization
- **Next Milestone**: Error-free codebase with enhanced features

### Key Files to Monitor
- `tasks.md` - Current task status and priorities
- `implementation-plan.md` - Detailed implementation strategies
- `package.json` - Dependencies and scripts
- `prisma/schema.prisma` - Database schema
- `src/types/` - TypeScript definitions

## Memory Bank Activation

To activate a specific mode, reference the appropriate instruction file:
- Plan: `.cursor/custom_modes/plan_instructions.md`
- Creative: `.cursor/custom_modes/creative_instructions.md`
- Implement: `.cursor/custom_modes/implement_instructions.md`
- Reflect: `.cursor/custom_modes/reflect_archive_instructions.md`
- Van: `.cursor/custom_modes/van_instructions.md`

Always read the current `tasks.md` and `implementation-plan.md` before starting any mode to understand the current project state and priorities.
