'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface DashboardStats {
  activeProjects: number;
  completedProjects: number;
  totalTasks: number;
  completedTasks: number;
  recentProjects: {
    id: string;
    name: string;
    status: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
    _count: {
      tasks: number;
    };
  }[];
}

export default function DashboardPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) {
          throw new Error('Fout bij het ophalen van dashboard statistieken');
        }
        const data = await response.json();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Er is een fout opgetreden');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {error || 'Kon de dashboard statistieken niet laden'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight gradient-text">
          Welkom terug, {session?.user?.name}
        </h1>
        <p className="text-sm text-muted-foreground">
          Hier is een overzicht van je projecten en taken
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="glow-accent border-primary/20 hover:border-primary/40 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Actieve Projecten
            </CardTitle>
            <Badge variant="default" className="bg-primary/20 text-primary border-primary/30">{stats.activeProjects}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold gradient-text">{stats.activeProjects}</div>
            <p className="text-xs text-muted-foreground">
              Projecten in uitvoering
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 hover:border-primary/40 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Afgeronde Projecten
            </CardTitle>
            <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground border-secondary/30">{stats.completedProjects}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold gradient-text">{stats.completedProjects}</div>
            <p className="text-xs text-muted-foreground">
              Succesvol afgerond
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 hover:border-primary/40 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Totaal Taken
            </CardTitle>
            <Badge variant="outline" className="border-primary/30 text-primary">{stats.totalTasks}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold gradient-text">{stats.totalTasks}</div>
            <p className="text-xs text-muted-foreground">
              Alle taken
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 hover:border-primary/40 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Afgeronde Taken
            </CardTitle>
            <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground border-secondary/30">{stats.completedTasks}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold gradient-text">{stats.completedTasks}</div>
            <p className="text-xs text-muted-foreground">
              Succesvol afgerond
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recente Projecten</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.recentProjects.length === 0 ? (
              <p className="text-sm text-muted-foreground">
                Geen recente projecten gevonden
              </p>
            ) : (
              <div className="space-y-4">
                {stats.recentProjects.map((project) => (
                  <div
                    key={project.id}
                    className="flex items-center justify-between"
                  >
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {project.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {project._count.tasks} {project._count.tasks === 1 ? 'taak' : 'taken'}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                    >
                      Bekijken
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Snelle Acties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              className="w-full justify-start glow-accent hover:shadow-lg transition-all duration-300"
              onClick={() => router.push('/dashboard/projects/new')}
            >
              Nieuw Project
            </Button>
            <Button
              className="w-full justify-start border-primary/30 hover:border-primary/50 transition-all duration-300"
              variant="outline"
              onClick={() => router.push('/dashboard/projects')}
            >
              Alle Projecten
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 