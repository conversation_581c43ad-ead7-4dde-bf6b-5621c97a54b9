'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { AnimatePresenceWrapper, FadeIn } from '@/components/ui/animate';
import { motion } from 'framer-motion';
import { useSession } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function SettingsPage() {
  const { data: session } = useSession();
  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    bio: '',
    notifications: {
      email: true,
      push: false,
      marketing: false,
    },
  });

  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        name: session.user.name || '',
        email: session.user.email || '',
      }));
    }
  }, [session]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Hier zou je de API call maken om de instellingen op te slaan
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulatie
      toast.success('Instellingen succesvol opgeslagen!');
    } catch (error) {
      toast.error('Er is iets misgegaan bij het opslaan van de instellingen.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatePresenceWrapper>
      <FadeIn>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold gradient-text">Instellingen</h1>
            <p className="text-muted-foreground">
              Beheer je account instellingen en voorkeuren.
            </p>
          </div>

          <Separator />

          <div className="grid gap-6">
            {/* Profiel Instellingen */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Profiel</CardTitle>
                  <CardDescription>
                    Update je profiel informatie en persoonlijke gegevens.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name">Naam</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Je volledige naam"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">E-mail</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      value={formData.bio}
                      onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                      placeholder="Vertel iets over jezelf..."
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Thema Instellingen */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Uiterlijk</CardTitle>
                  <CardDescription>
                    Pas het thema en uiterlijk van de applicatie aan.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Donker thema</Label>
                      <p className="text-sm text-muted-foreground">
                        Schakel tussen licht en donker thema
                      </p>
                    </div>
                    <Switch
                      checked={theme === 'dark'}
                      onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Notificatie Instellingen */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Notificaties</CardTitle>
                  <CardDescription>
                    Configureer hoe en wanneer je notificaties wilt ontvangen.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>E-mail notificaties</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang updates via e-mail
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.email}
                      onCheckedChange={(checked) => 
                        setFormData(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, email: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Push notificaties</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang push notificaties in je browser
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.push}
                      onCheckedChange={(checked) => 
                        setFormData(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, push: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Marketing e-mails</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang nieuws en updates over nieuwe functies
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.marketing}
                      onCheckedChange={(checked) => 
                        setFormData(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, marketing: checked }
                        }))
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Gevaarlijke Acties */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="border-destructive/50">
                <CardHeader>
                  <CardTitle className="text-destructive">Gevaarlijke Zone</CardTitle>
                  <CardDescription>
                    Permanente acties die niet ongedaan gemaakt kunnen worden.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert variant="destructive">
                    <AlertDescription>
                      Het verwijderen van je account is permanent en kan niet ongedaan worden gemaakt.
                      Alle je projecten en taken zullen verloren gaan.
                    </AlertDescription>
                  </Alert>
                  <div className="mt-4">
                    <Button variant="destructive" size="sm">
                      Account Verwijderen
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Opslaan Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="flex justify-end"
            >
              <Button 
                onClick={handleSave} 
                disabled={isLoading}
                className="glow-accent hover:shadow-lg transition-all duration-300"
              >
                {isLoading ? 'Opslaan...' : 'Instellingen Opslaan'}
              </Button>
            </motion.div>
          </div>
        </div>
      </FadeIn>
    </AnimatePresenceWrapper>
  );
}
