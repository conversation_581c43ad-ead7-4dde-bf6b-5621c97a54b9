'use client';

import { MainNav } from '@/components/dashboard/main-nav';
import { MobileNav } from '@/components/dashboard/mobile-nav';
import { SearchBar } from '@/components/dashboard/search';
import { UserNav } from '@/components/dashboard/user-nav';
import { ThemeToggle } from '@/components/theme-toggle';
import { AnimatePresenceWrapper, FadeIn } from '@/components/ui/animate';
import {
  FolderKanban,
  LayoutDashboard,
  Settings
} from 'lucide-react';
import { Toaster } from 'sonner';

const items = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Projecten',
    href: '/dashboard/projects',
    icon: FolderKanban,
  },
  {
    title: 'Instellingen',
    href: '/dashboard/settings',
    icon: Settings,
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b border-primary/20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <MobileNav items={items} />
          <MainNav />
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <SearchBar />
            </div>
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <UserNav />
            </div>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <div className="container py-6">
          <AnimatePresenceWrapper>
            <FadeIn>
              {children}
            </FadeIn>
          </AnimatePresenceWrapper>
        </div>
      </main>
      <Toaster richColors position="top-right" />
    </div>
  );
} 