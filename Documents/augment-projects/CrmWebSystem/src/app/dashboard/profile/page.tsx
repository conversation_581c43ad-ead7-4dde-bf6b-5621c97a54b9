'use client';

import { motion } from 'framer-motion';
import {
  Lock,
  Mail,
  Save,
  User
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { toast } from 'sonner';

import { AnimatePresenceWrapper, FadeIn } from '@/components/ui/animate';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ProfilePage() {
  const { data: session, update } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  if (!session?.user) {
    return null;
  }

  const initials = session.user.name
    ?.split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Hier zou je de API call maken om de gebruikersgegevens bij te werken
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulatie API call
      toast.success('Profiel succesvol bijgewerkt');
    } catch (error) {
      toast.error('Er is iets misgegaan bij het bijwerken van je profiel');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatePresenceWrapper>
      <FadeIn>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">Profiel</h1>
            <p className="text-muted-foreground">
              Beheer je account instellingen en voorkeuren
            </p>
          </div>

          <Tabs defaultValue="account" className="space-y-4">
            <TabsList>
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="notifications">Notificaties</TabsTrigger>
              <TabsTrigger value="appearance">Weergave</TabsTrigger>
            </TabsList>

            <TabsContent value="account" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Profiel Informatie</CardTitle>
                  <CardDescription>
                    Update je persoonlijke informatie
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-20 w-20">
                        <AvatarImage src={session.user.image || ''} alt={session.user.name || ''} />
                        <AvatarFallback className="text-lg">{initials}</AvatarFallback>
                      </Avatar>
                      <Button variant="outline" size="sm">
                        Verander foto
                      </Button>
                    </div>

                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="name">Naam</Label>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted">
                            <User className="h-4 w-4" />
                          </span>
                          <Input
                            id="name"
                            defaultValue={session.user.name || ''}
                            className="rounded-l-none"
                          />
                        </div>
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted">
                            <Mail className="h-4 w-4" />
                          </span>
                          <Input
                            id="email"
                            type="email"
                            defaultValue={session.user.email || ''}
                            className="rounded-l-none"
                          />
                        </div>
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="password">Wachtwoord</Label>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted">
                            <Lock className="h-4 w-4" />
                          </span>
                          <Input
                            id="password"
                            type="password"
                            placeholder="••••••••"
                            className="rounded-l-none"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button type="submit" disabled={isLoading}>
                        <Save className="mr-2 h-4 w-4" />
                        {isLoading ? 'Bezig met opslaan...' : 'Opslaan'}
                      </Button>
                    </motion.div>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Notificatie Instellingen</CardTitle>
                  <CardDescription>
                    Configureer hoe je notificaties wilt ontvangen
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Email Notificaties</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang updates over je projecten
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Push Notificaties</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang meldingen in je browser
                      </p>
                    </div>
                    <Switch />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Weergave Instellingen</CardTitle>
                  <CardDescription>
                    Pas de weergave van de applicatie aan
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Donker Modus</Label>
                      <p className="text-sm text-muted-foreground">
                        Schakel tussen licht en donker thema
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Taal</Label>
                      <p className="text-sm text-muted-foreground">
                        Kies je voorkeurstaal
                      </p>
                    </div>
                    <select className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm">
                      <option value="nl">Nederlands</option>
                      <option value="en">English</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </FadeIn>
    </AnimatePresenceWrapper>
  );
} 