'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

interface TaskFormData {
  title: string;
  description: string;
  status: 'TODO' | 'IN_PROGRESS' | 'DONE';
  dueDate: string;
}

export default function NewTaskPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    status: 'TODO',
    dueDate: '',
  });

  // State en ref voor spraakherkenning
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  const startListening = () => {
    if (!('webkitSpeechRecognition' in window)) {
      toast.error(
        'Spraakherkenning wordt niet ondersteund in deze browser.'
      );
      return;
    }

    const SpeechRecognition = (window as any).webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.continuous = false; // Luister één keer
    recognition.interimResults = false; // Geef alleen definitieve resultaten
    recognition.lang = 'nl-NL'; // Taal instellen op Nederlands

    recognition.onstart = () => {
      setIsListening(true);
      toast.info('Luisteren gestart...');
    };

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      const transcript = event.results[0][0].transcript;
      setFormData((prev) => ({
        ...prev,
        title: transcript, // Zet de getranscribeerde tekst als titel
      }));
      toast.success(`Spraak herkend: "${transcript}"`);
    };

    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      setIsListening(false);
      setError(`Spraakherkenning fout: ${event.error}`);
      toast.error(`Spraakherkenning fout: ${event.error}`);
    };

    recognition.onend = () => {
      setIsListening(false);
      toast.info('Luisteren gestopt.');
    };

    recognitionRef.current = recognition;
    recognition.start();
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/projects/${params.id}/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        // Probeer de foutmelding van de server te lezen
        const errorData = await response.json();
        const errorMessage = errorData.error || 'Fout bij het aanmaken van de taak';
        throw new Error(errorMessage);
      }

      router.push(`/dashboard/projects/${params.id}`);
      toast.success('Taak succesvol aangemaakt!');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Er is een fout opgetreden');
      toast.error(err instanceof Error ? err.message : 'Er is een fout opgetreden');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">
          Nieuwe Taak
        </h1>
        <Button
          variant="outline"
          onClick={() => router.push(`/dashboard/projects/${params.id}`)}
        >
          Terug
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Taak Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <label
                htmlFor="title"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Titel
              </label>
              <div className="flex space-x-2">
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  required
                />
                <button
                  type="button"
                  style={{ padding: '8px 12px', border: '1px solid grey', borderRadius: '4px' }}
                  onClick={isListening ? stopListening : startListening}
                  disabled={isSubmitting}
                >
                  {isListening ? (
                    'Stop Opnemen'
                  ) : (
                    'Microfoon'
                  )}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <label
                htmlFor="description"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Beschrijving
              </label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <label
                htmlFor="status"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Status
              </label>
              <Select
                value={formData.status}
                onValueChange={(value: 'TODO' | 'IN_PROGRESS' | 'DONE') =>
                  setFormData({ ...formData, status: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecteer status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TODO">Te Doen</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Behandeling</SelectItem>
                  <SelectItem value="DONE">Afgerond</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label
                htmlFor="dueDate"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Deadline
              </label>
              <Input
                id="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={(e) =>
                  setFormData({ ...formData, dueDate: e.target.value })
                }
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/dashboard/projects/${params.id}`)}
              >
                Annuleren
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Aanmaken...' : 'Aanmaken'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 