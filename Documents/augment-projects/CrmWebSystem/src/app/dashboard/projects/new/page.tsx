'use client';

import { EnhancedProjectForm } from '@/components/forms/EnhancedProjectForm';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function NewProjectPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Terug
        </Button>
        <h1 className="text-3xl font-bold tracking-tight gradient-text">
          Nieuw Project Aanmaken
        </h1>
      </div>

      <EnhancedProjectForm />
    </div>
  );
}