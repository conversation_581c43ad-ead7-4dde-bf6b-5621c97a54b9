'use client';

import { motion } from 'framer-motion';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { AnimatePresenceWrapper, ScaleIn } from '@/components/ui/animate';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'ACTIVE' | 'COMPLETED' | 'ON_HOLD';
  _count: {
    tasks: number;
  };
}

export default function ProjectsPage() {
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProjects() {
      try {
        const response = await fetch('/api/projects');
        if (!response.ok) {
          throw new Error('Er is iets misgegaan bij het ophalen van de projecten.');
        }
        const data = await response.json();
        setProjects(data);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Er is iets misgegaan.');
      } finally {
        setIsLoading(false);
      }
    }

    fetchProjects();
  }, []);

  if (isLoading) {
    return (
      <div className="flex h-[50vh] items-center justify-center">
        <Spinner className="h-8 w-8" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Projecten</h1>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button onClick={() => router.push('/dashboard/projects/new')}>
            <Plus className="mr-2 h-4 w-4" />
            Nieuw Project
          </Button>
        </motion.div>
      </div>

      <AnimatePresenceWrapper>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {projects.map((project, index) => (
            <ScaleIn key={project.id} delay={index * 0.1}>
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle>{project.name}</CardTitle>
                    <CardDescription>{project.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <Badge variant={
                        project.status === 'ACTIVE' ? 'default' :
                          project.status === 'COMPLETED' ? 'secondary' :
                            'outline'
                      }>
                        {project.status === 'ACTIVE' ? 'Actief' :
                          project.status === 'COMPLETED' ? 'Afgerond' :
                            'On Hold'}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {project._count.tasks} {project._count.tasks === 1 ? 'taak' : 'taken'}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full"
                    >
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                      >
                        Bekijk Details
                      </Button>
                    </motion.div>
                  </CardFooter>
                </Card>
              </motion.div>
            </ScaleIn>
          ))}
        </div>
      </AnimatePresenceWrapper>
    </div>
  );
} 