import { ReactNode } from 'react'
import { myLocalFont } from '../lib/fonts'
import './globals.css'
import { Providers } from './providers'

interface RootLayoutProps {
  children: ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" className={myLocalFont.variable}>
      <body className={myLocalFont.className}>
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
