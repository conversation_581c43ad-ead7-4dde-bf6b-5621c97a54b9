@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Hackathon.dev inspired dark theme */
  --background: oklch(0.08 0.01 240);
  --foreground: oklch(0.95 0.01 60);
  --card: oklch(0.12 0.02 240);
  --card-foreground: oklch(0.95 0.01 60);
  --popover: oklch(0.12 0.02 240);
  --popover-foreground: oklch(0.95 0.01 60);
  --primary: oklch(0.75 0.15 45);
  --primary-foreground: oklch(0.08 0.01 240);
  --secondary: oklch(0.18 0.02 240);
  --secondary-foreground: oklch(0.95 0.01 60);
  --muted: oklch(0.15 0.02 240);
  --muted-foreground: oklch(0.65 0.05 60);
  --accent: oklch(0.75 0.15 45);
  --accent-foreground: oklch(0.08 0.01 240);
  --destructive: oklch(0.65 0.2 25);
  --border: oklch(0.25 0.03 240);
  --input: oklch(0.18 0.02 240);
  --ring: oklch(0.75 0.15 45);
  --chart-1: oklch(0.75 0.15 45);
  --chart-2: oklch(0.65 0.12 180);
  --chart-3: oklch(0.55 0.1 280);
  --chart-4: oklch(0.7 0.18 120);
  --chart-5: oklch(0.8 0.2 30);
  --sidebar: oklch(0.1 0.015 240);
  --sidebar-foreground: oklch(0.95 0.01 60);
  --sidebar-primary: oklch(0.75 0.15 45);
  --sidebar-primary-foreground: oklch(0.08 0.01 240);
  --sidebar-accent: oklch(0.18 0.02 240);
  --sidebar-accent-foreground: oklch(0.95 0.01 60);
  --sidebar-border: oklch(0.25 0.03 240);
  --sidebar-ring: oklch(0.75 0.15 45);
}

.dark {
  /* Enhanced dark mode with hackathon.dev vibes */
  --background: oklch(0.05 0.01 240);
  --foreground: oklch(0.98 0.005 60);
  --card: oklch(0.08 0.015 240);
  --card-foreground: oklch(0.98 0.005 60);
  --popover: oklch(0.08 0.015 240);
  --popover-foreground: oklch(0.98 0.005 60);
  --primary: oklch(0.8 0.18 45);
  --primary-foreground: oklch(0.05 0.01 240);
  --secondary: oklch(0.15 0.02 240);
  --secondary-foreground: oklch(0.98 0.005 60);
  --muted: oklch(0.12 0.02 240);
  --muted-foreground: oklch(0.7 0.05 60);
  --accent: oklch(0.8 0.18 45);
  --accent-foreground: oklch(0.05 0.01 240);
  --destructive: oklch(0.7 0.22 25);
  --border: oklch(0.2 0.03 240);
  --input: oklch(0.15 0.02 240);
  --ring: oklch(0.8 0.18 45);
  --chart-1: oklch(0.8 0.18 45);
  --chart-2: oklch(0.7 0.15 180);
  --chart-3: oklch(0.6 0.12 280);
  --chart-4: oklch(0.75 0.2 120);
  --chart-5: oklch(0.85 0.22 30);
  --sidebar: oklch(0.07 0.012 240);
  --sidebar-foreground: oklch(0.98 0.005 60);
  --sidebar-primary: oklch(0.8 0.18 45);
  --sidebar-primary-foreground: oklch(0.05 0.01 240);
  --sidebar-accent: oklch(0.15 0.02 240);
  --sidebar-accent-foreground: oklch(0.98 0.005 60);
  --sidebar-border: oklch(0.2 0.03 240);
  --sidebar-ring: oklch(0.8 0.18 45);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* Hackathon.dev inspired gradient background */
    background: radial-gradient(ellipse at top, oklch(0.12 0.05 45) 0%, oklch(0.08 0.01 240) 50%);
    min-height: 100vh;
  }

  /* Glowing accent effects */
  .glow-accent {
    box-shadow: 0 0 20px oklch(0.75 0.15 45 / 0.3);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, oklch(0.8 0.18 45), oklch(0.9 0.15 60));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animated gradient background for cards */
  .card-gradient {
    background: linear-gradient(135deg, oklch(0.12 0.02 240), oklch(0.08 0.015 240));
    position: relative;
    overflow: hidden;
  }

  .card-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, oklch(0.75 0.15 45 / 0.1), transparent);
    transition: left 0.5s;
  }

  .card-gradient:hover::before {
    left: 100%;
  }

  /* Pulsing animation for active elements */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from {
      box-shadow: 0 0 10px oklch(0.75 0.15 45 / 0.2);
    }
    to {
      box-shadow: 0 0 20px oklch(0.75 0.15 45 / 0.4), 0 0 30px oklch(0.75 0.15 45 / 0.2);
    }
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
