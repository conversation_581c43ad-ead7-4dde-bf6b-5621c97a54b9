import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Niet geautoriseerd' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Haal alle statistieken op in parallel
    const [
      activeProjects,
      completedProjects,
      totalTasks,
      completedTasks,
      recentProjects,
    ] = await Promise.all([
      // Actieve projecten
      prisma.project.count({
        where: {
          userId,
          status: 'ACTIVE',
        },
      }),
      
      // Afgeronde projecten
      prisma.project.count({
        where: {
          userId,
          status: 'COMPLETED',
        },
      }),
      
      // Totaal aantal taken
      prisma.task.count({
        where: {
          userId,
        },
      }),
      
      // Afgeronde taken
      prisma.task.count({
        where: {
          userId,
          status: 'DONE',
        },
      }),
      
      // Recente projecten (laatste 5)
      prisma.project.findMany({
        where: {
          userId,
        },
        include: {
          _count: {
            select: {
              tasks: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        take: 5,
      }),
    ]);

    const stats = {
      activeProjects,
      completedProjects,
      totalTasks,
      completedTasks,
      recentProjects,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Fout bij het ophalen van dashboard statistieken' },
      { status: 500 }
    );
  }
}
