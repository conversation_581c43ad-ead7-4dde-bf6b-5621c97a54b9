import { sendEmail } from '@/lib/email';
import { prisma } from '@/lib/prisma';
import { hash } from 'bcryptjs';
import { randomBytes } from 'crypto';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const registerSchema = z.object({
  name: z.string().min(2, 'Naam moet minimaal 2 karakters bevatten'),
  email: z.string().email('Ongeldig email adres'),
  password: z.string().min(6, 'Wachtwoord moet minimaal 6 karakters bevatten'),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { name, email, password } = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: '<PERSON><PERSON><PERSON><PERSON><PERSON> met dit email adres bestaat al' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hash(password, 12);

    // Generate verification token
    const verificationToken = randomBytes(32).toString('hex');
    const verificationTokenExpiry = new Date(Date.now() + 24 * 3600000); // 24 hours

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        verificationToken,
        verificationTokenExpiry,
      },
    });

    // Send verification email
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}`;
    await sendEmail({
      to: email,
      subject: 'Verifieer je email adres',
      text: `Klik op de volgende link om je email adres te verifiëren: ${verificationUrl}`,
      html: `
        <p>Welkom bij CRM Web System!</p>
        <p>Klik op de volgende link om je email adres te verifiëren:</p>
        <p><a href="${verificationUrl}">${verificationUrl}</a></p>
        <p>Deze link is 24 uur geldig.</p>
      `,
    });

    return NextResponse.json(
      {
        message: 'Registratie succesvol. Controleer je email voor verificatie.',
        user: {
          name: user.name,
          email: user.email,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Er is iets misgegaan bij het registreren' },
      { status: 500 }
    );
  }
} 