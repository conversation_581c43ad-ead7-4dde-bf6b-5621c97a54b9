import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';
import { generateText } from 'services/ai/jan-ai';
import { z } from 'zod';

// Schema voor inkomende conversationele aanvraag
const converseRequestSchema = z.object({
  text: z.string().min(1, 'Tekst is verplicht'),
  projectId: z.string().min(1, 'Project ID is verplicht'),
  currentTaskData: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    status: z.enum(['TODO', 'IN_PROGRESS', 'DONE']).optional(),
    dueDate: z.string().optional(),
  }).optional(),
});

// Schema voor het verwachte antwoord van de AI
const aiResponseSchema = z.object({
  action: z.enum(['create_task', 'ask_question', 'unclear_intent']),
  task: z.object({
    title: z.string().min(1, 'Titel is verplicht').optional(),
    description: z.string().optional(),
    status: z.enum(['TODO', 'IN_PROGRESS', 'DONE']).optional(),
    dueDate: z.string().optional(),
  }).optional(),
  question: z.string().optional(), // Indien actie 'ask_question' is
  error: z.string().optional(), // Indien er iets misging bij de AI-verwerking
});

export async function POST(
  req: Request,
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const json = await req.json();
    const { text, projectId, currentTaskData } = converseRequestSchema.parse(json);

    // Controleer of het project bestaat en van de gebruiker is
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return new NextResponse('Project niet gevonden', { status: 404 });
    }

    // Bouw prompt voor de AI
    let prompt = `De gebruiker zei: "${text}".`;
    if (currentTaskData) {
      prompt += ` De huidige (gedeeltelijke) taakgegevens zijn: ${JSON.stringify(currentTaskData)}.`;
    }
    prompt += ` Jouw taak is om de intentie van de gebruiker te bepalen. Als de gebruiker een taak wil aanmaken, extraheer dan de 'title', 'description', 'status' (moet 'TODO', 'IN_PROGRESS' of 'DONE' zijn) en 'dueDate' (formaat YYYY-MM-DD). Als niet alle informatie voor een taak compleet is, stel dan een vraag om de ontbrekende informatie te verkrijgen. Als de intentie onduidelijk is, geef dit dan aan. \nReageer in JSON-formaat.\nVoorbeeld reactie als alle info voor een taak aanwezig is:\n{"action": "create_task", "task": {"title": "Titel van de taak", "description": "Beschrijving", "status": "TODO", "dueDate": "2024-12-31"}}\nVoorbeeld reactie als info ontbreekt:\n{"action": "ask_question", "question": "Wat is de titel van de taak?", "task": {"title": "Deel van titel"}}\nVoorbeeld reactie als intentie onduidelijk is:\n{"action": "unclear_intent", "question": "Ik begrijp je intentie niet. Kun je duidelijker zijn?"}`; // Aangepast voorbeeld voor 'task' veld bij 'ask_question'

    // Roep Jan.ai aan
    const aiResponse = await generateText({
      prompt,
      model: 'llama-3-8b-instruct', // Gebruik het model dat je eerder hebt geconfigureerd
      maxTokens: 300, // Verhoog maxTokens voor complexere responses
      temperature: 0.5,
    });

    if (!aiResponse.success) {
      return new NextResponse(JSON.stringify({ error: aiResponse.error || 'AI-generatie mislukt' }), { status: 500 });
    }

    let parsedAiResponse;
    try {
      parsedAiResponse = aiResponseSchema.parse(JSON.parse(aiResponse.text));
    } catch (parseError) {
      console.error('Fout bij parsen AI-antwoord:', parseError, 'Raw AI text:', aiResponse.text);
      return new NextResponse(JSON.stringify({ error: 'AI antwoord kon niet worden geparsed.' }), { status: 500 });
    }

    if (parsedAiResponse.action === 'ask_question' || parsedAiResponse.action === 'unclear_intent') {
      return NextResponse.json({
        action: parsedAiResponse.action,
        question: parsedAiResponse.question,
        task: parsedAiResponse.task, // Retourneer de (gedeeltelijke) taakdata
      });
    }

    if (parsedAiResponse.action === 'create_task' && parsedAiResponse.task) {
      const { title, description, status, dueDate } = parsedAiResponse.task;

      // Combineer met bestaande partial data
      const finalTaskData = {
        ...currentTaskData,
        title: title || currentTaskData?.title,
        description: description || currentTaskData?.description,
        status: status || currentTaskData?.status,
        dueDate: dueDate || currentTaskData?.dueDate,
      };

      // Controleer of alle vereiste velden aanwezig zijn voor creatie
      if (!finalTaskData.title || !finalTaskData.status) {
        return NextResponse.json({
          action: 'ask_question',
          question: 'De taak is nog niet compleet. Zorg voor een titel en status.',
          task: finalTaskData,
        }, { status: 400 });
      }

      const createdTask = await prisma.task.create({
        data: {
          title: finalTaskData.title,
          description: finalTaskData.description,
          status: finalTaskData.status,
          dueDate: finalTaskData.dueDate && !isNaN(new Date(finalTaskData.dueDate).getTime()) ? new Date(finalTaskData.dueDate) : null,
          projectId: projectId,
          userId: session.user.id,
        },
      });
      return NextResponse.json({ action: 'task_created', task: createdTask });
    }

    return new NextResponse(JSON.stringify({ error: 'Onverwachte AI respons' }), { status: 500 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return new NextResponse(JSON.stringify(error.issues), { status: 422 });
    }
    console.error('Fout in AI conversatie route:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Er is een fout opgetreden bij AI conversatie' }),
      { status: 500 }
    );
  }
} 