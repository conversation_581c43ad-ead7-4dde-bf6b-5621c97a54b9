import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';
import { z } from 'zod';

// Schema voor taak validatie
const createTaskSchema = z.object({
  title: z.string().min(1, 'Titel is verplicht').max(100),
  description: z.string().max(500).optional(),
  status: z.enum(['TODO', 'IN_PROGRESS', 'DONE']),
  dueDate: z.string().optional(),
});

export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Controleer of het project bestaat en van <PERSON> geb<PERSON>ike<PERSON> is
    const project = await prisma.project.findUnique({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!project) {
      return new NextResponse('Project niet gevonden', { status: 404 });
    }

    const json = await req.json();
    const body = createTaskSchema.parse(json);

    const task = await prisma.task.create({
      data: {
        title: body.title,
        description: body.description,
        status: body.status,
        dueDate: body.dueDate && !isNaN(new Date(body.dueDate).getTime()) ? new Date(body.dueDate) : null,
        projectId: params.id,
        userId: session.user.id,
      },
    });

    return NextResponse.json(task);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return new NextResponse(JSON.stringify(error.issues), { status: 422 });
    }

    return new NextResponse(
      JSON.stringify({ error: 'Er is een fout opgetreden' }),
      { status: 500 }
    );
  }
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Controleer of het project bestaat en van de gebruiker is
    const project = await prisma.project.findUnique({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!project) {
      return new NextResponse('Project niet gevonden', { status: 404 });
    }

    const tasks = await prisma.task.findMany({
      where: {
        projectId: params.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(tasks);
  } catch (error) {
    return new NextResponse(
      JSON.stringify({ error: 'Er is een fout opgetreden' }),
      { status: 500 }
    );
  }
} 