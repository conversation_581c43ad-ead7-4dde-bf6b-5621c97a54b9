'use client';

import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import * as React from 'react';

interface HoverCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  scale?: number;
  duration?: number;
}

export function HoverCard({
  children,
  className,
  scale = 1.02,
  duration = 0.2,
  ...props
}: HoverCardProps) {
  return (
    <motion.div
      whileHover={{ scale }}
      transition={{ duration }}
      className={cn(
        'rounded-lg border bg-card text-card-foreground shadow-sm transition-colors hover:bg-accent/50',
        className
      )}
      {...props}
    >
      {children}
    </motion.div>
  );
}

interface HoverButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  scale?: number;
  duration?: number;
}

export function HoverButton({
  children,
  className,
  scale = 1.05,
  duration = 0.2,
  ...props
}: HoverButtonProps) {
  return (
    <motion.button
      whileHover={{ scale }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration }}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        className
      )}
      {...props}
    >
      {children}
    </motion.button>
  );
} 