'use client';

import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import {
  navigationMenuTriggerStyle
} from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';

interface BreadcrumbProps extends React.ComponentPropsWithoutRef<'nav'> {
  separator?: React.ReactNode;
  truncationLength?: number;
}

export function Breadcrumb({
  className,
  separator = <ChevronRight className="h-4 w-4" />,
  truncationLength = 0,
  ...props
}: BreadcrumbProps) {
  const pathname = usePathname();
  const segments = pathname.split('/').filter(Boolean);

  const items = segments.map((segment, index) => {
    const href = `/${segments.slice(0, index + 1).join('/')}`;
    const isLast = index === segments.length - 1;

    let label = segment.charAt(0).toUpperCase() + segment.slice(1);
    if (truncationLength > 0 && label.length > truncationLength) {
      label = `${label.slice(0, truncationLength)}...`;
    }

    return {
      href,
      label,
      isLast,
    };
  });

  return (
    <nav
      aria-label="breadcrumb"
      className={cn(
        'flex items-center space-x-1 text-sm text-muted-foreground',
        className
      )}
      {...props}
    >
      <ol className="flex items-center space-x-1">
        {items.map((item, index) => (
          <li key={item.href} className="flex items-center">
            {index > 0 && <span className="mx-1">{separator}</span>}
            {item.isLast ? (
              <span className="font-medium text-foreground">{item.label}</span>
            ) : (
              <Link
                href={item.href}
                className={cn(
                  'hover:text-foreground transition-colors',
                  navigationMenuTriggerStyle()
                )}
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
} 