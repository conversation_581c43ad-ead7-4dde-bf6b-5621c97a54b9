'use client';

import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  FolderKanban,
  LayoutDashboard,
  LogOut,
  Settings
} from 'lucide-react';
import { signOut } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const items: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Projecten',
    href: '/dashboard/projects',
    icon: FolderKanban,
  },
  {
    title: 'Instellingen',
    href: '/dashboard/settings',
    icon: Settings,
  },
];

export function MainNav() {
  const pathname = usePathname();

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      {items.map((item) => (
        <Button
          key={item.href}
          variant={pathname === item.href ? 'default' : 'ghost'}
          className={`flex items-center gap-2 transition-all duration-300 ${pathname === item.href
            ? 'glow-accent bg-primary/20 text-primary border-primary/30'
            : 'hover:bg-primary/10 hover:text-primary'
            }`}
          asChild
        >
          <Link href={item.href}>
            <item.icon className="h-4 w-4" />
            <span className="hidden md:inline-block">{item.title}</span>
          </Link>
        </Button>
      ))}
      <Separator orientation="vertical" className="h-6" />
      <Button
        variant="ghost"
        className="flex items-center gap-2 hover:bg-destructive/10 hover:text-destructive transition-all duration-300"
        onClick={() => signOut()}
      >
        <LogOut className="h-4 w-4" />
        <span className="hidden md:inline-block">Uitloggen</span>
      </Button>
    </nav>
  );
} 