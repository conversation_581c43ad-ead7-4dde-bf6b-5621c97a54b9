{"name": "sonner", "version": "2.0.5", "description": "An opinionated toast component for React.", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.js"}, "./dist/styles.css": "./dist/styles.css"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "bunchee --watch", "build": "bunchee && cp src/styles.css dist/styles.css", "type-check": "tsc --noEmit", "dev:website": "turbo run dev --filter=website...", "dev:test": "turbo run dev --filter=test...", "format": "prettier --write .", "test": "playwright test"}, "keywords": ["react", "notifications", "toast", "snackbar", "message"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://sonner.emilkowal.ski/", "repository": {"type": "git", "url": "git+https://github.com/emilkowalski/sonner.git"}, "bugs": {"url": "https://github.com/emilkowalski/sonner/issues"}, "devDependencies": {"@playwright/test": "^1.49.1", "@types/node": "^18.11.13", "@types/react": "^18.0.26", "bunchee": "6.3.3", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "turbo": "1.6", "typescript": "^4.8.4"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "packageManager": "pnpm@8.12.1"}