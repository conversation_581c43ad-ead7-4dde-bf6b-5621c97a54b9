LICENSE
MANIFEST.in
README.md
pyproject.toml
setup.py
examples/__init__.py
nemo/__init__.py
nemo/constants.py
nemo/package_info.py
nemo/automodel/__init__.py
nemo/automodel/dist_utils.py
nemo/automodel/misc_utils.py
nemo/automodel/loss/__init__.py
nemo/automodel/loss/chunked_ce.py
nemo/automodel/loss/linear_ce.py
nemo/automodel/loss/masked_ce.py
nemo/collections/__init__.py
nemo/collections/asr/__init__.py
nemo/collections/asr/data/__init__.py
nemo/collections/asr/data/audio_to_ctm_dataset.py
nemo/collections/asr/data/audio_to_diar_label.py
nemo/collections/asr/data/audio_to_diar_label_lhotse.py
nemo/collections/asr/data/audio_to_label.py
nemo/collections/asr/data/audio_to_label_dataset.py
nemo/collections/asr/data/audio_to_text.py
nemo/collections/asr/data/audio_to_text_dali.py
nemo/collections/asr/data/audio_to_text_dataset.py
nemo/collections/asr/data/audio_to_text_lhotse.py
nemo/collections/asr/data/audio_to_text_lhotse_prompted.py
nemo/collections/asr/data/data_simulation.py
nemo/collections/asr/data/feature_to_label.py
nemo/collections/asr/data/feature_to_label_dataset.py
nemo/collections/asr/data/feature_to_text.py
nemo/collections/asr/data/feature_to_text_dataset.py
nemo/collections/asr/data/ssl_dataset.py
nemo/collections/asr/data/text_to_text.py
nemo/collections/asr/data/huggingface/__init__.py
nemo/collections/asr/data/huggingface/hf_audio_to_text.py
nemo/collections/asr/data/huggingface/hf_audio_to_text_dataset.py
nemo/collections/asr/losses/__init__.py
nemo/collections/asr/losses/angularloss.py
nemo/collections/asr/losses/bce_loss.py
nemo/collections/asr/losses/ctc.py
nemo/collections/asr/losses/lattice_losses.py
nemo/collections/asr/losses/rnnt.py
nemo/collections/asr/losses/rnnt_pytorch.py
nemo/collections/asr/losses/ssl_losses/__init__.py
nemo/collections/asr/losses/ssl_losses/contrastive.py
nemo/collections/asr/losses/ssl_losses/ctc.py
nemo/collections/asr/losses/ssl_losses/mlm.py
nemo/collections/asr/losses/ssl_losses/rnnt.py
nemo/collections/asr/metrics/__init__.py
nemo/collections/asr/metrics/bleu.py
nemo/collections/asr/metrics/der.py
nemo/collections/asr/metrics/multi_binary_acc.py
nemo/collections/asr/metrics/wer.py
nemo/collections/asr/models/__init__.py
nemo/collections/asr/models/aed_multitask_models.py
nemo/collections/asr/models/asr_model.py
nemo/collections/asr/models/classification_models.py
nemo/collections/asr/models/clustering_diarizer.py
nemo/collections/asr/models/confidence_ensemble.py
nemo/collections/asr/models/ctc_bpe_models.py
nemo/collections/asr/models/ctc_models.py
nemo/collections/asr/models/hybrid_asr_tts_models.py
nemo/collections/asr/models/hybrid_rnnt_ctc_bpe_models.py
nemo/collections/asr/models/hybrid_rnnt_ctc_models.py
nemo/collections/asr/models/k2_aligner_model.py
nemo/collections/asr/models/k2_sequence_models.py
nemo/collections/asr/models/label_models.py
nemo/collections/asr/models/msdd_models.py
nemo/collections/asr/models/online_diarizer.py
nemo/collections/asr/models/rnnt_bpe_models.py
nemo/collections/asr/models/rnnt_models.py
nemo/collections/asr/models/slu_models.py
nemo/collections/asr/models/sortformer_diar_models.py
nemo/collections/asr/models/ssl_models.py
nemo/collections/asr/models/transformer_bpe_models.py
nemo/collections/asr/models/configs/__init__.py
nemo/collections/asr/models/configs/aligner_config.py
nemo/collections/asr/models/configs/asr_models_config.py
nemo/collections/asr/models/configs/classification_models_config.py
nemo/collections/asr/models/configs/diarizer_config.py
nemo/collections/asr/models/configs/k2_sequence_models_config.py
nemo/collections/asr/models/configs/matchboxnet_config.py
nemo/collections/asr/models/configs/quartznet_config.py
nemo/collections/asr/modules/__init__.py
nemo/collections/asr/modules/audio_preprocessing.py
nemo/collections/asr/modules/beam_search_decoder.py
nemo/collections/asr/modules/conformer_encoder.py
nemo/collections/asr/modules/conv_asr.py
nemo/collections/asr/modules/flashlight_decoder.py
nemo/collections/asr/modules/graph_decoder.py
nemo/collections/asr/modules/hybrid_autoregressive_transducer.py
nemo/collections/asr/modules/lstm_decoder.py
nemo/collections/asr/modules/msdd_diarizer.py
nemo/collections/asr/modules/rnn_encoder.py
nemo/collections/asr/modules/rnnt.py
nemo/collections/asr/modules/rnnt_abstract.py
nemo/collections/asr/modules/sortformer_modules.py
nemo/collections/asr/modules/squeezeformer_encoder.py
nemo/collections/asr/modules/wav2vec_modules.py
nemo/collections/asr/modules/ssl_modules/__init__.py
nemo/collections/asr/modules/ssl_modules/augmentation.py
nemo/collections/asr/modules/ssl_modules/masking.py
nemo/collections/asr/modules/ssl_modules/multi_layer_feat.py
nemo/collections/asr/modules/ssl_modules/multi_softmax_decoder.py
nemo/collections/asr/modules/ssl_modules/quantizers.py
nemo/collections/asr/modules/transformer/__init__.py
nemo/collections/asr/modules/transformer/bridge_encoders.py
nemo/collections/asr/modules/transformer/decoder_module.py
nemo/collections/asr/modules/transformer/encoder_module.py
nemo/collections/asr/modules/transformer/perceiver_encoders.py
nemo/collections/asr/modules/transformer/reduction_encoders.py
nemo/collections/asr/modules/transformer/text_generation.py
nemo/collections/asr/modules/transformer/transformer.py
nemo/collections/asr/modules/transformer/transformer_bottleneck.py
nemo/collections/asr/modules/transformer/transformer_decoders.py
nemo/collections/asr/modules/transformer/transformer_encoders.py
nemo/collections/asr/modules/transformer/transformer_generators.py
nemo/collections/asr/modules/transformer/transformer_modules.py
nemo/collections/asr/modules/transformer/transformer_utils.py
nemo/collections/asr/parts/__init__.py
nemo/collections/asr/parts/features.py
nemo/collections/asr/parts/context_biasing/__init__.py
nemo/collections/asr/parts/context_biasing/context_biasing_utils.py
nemo/collections/asr/parts/context_biasing/context_graph_ctc.py
nemo/collections/asr/parts/context_biasing/ctc_based_word_spotter.py
nemo/collections/asr/parts/k2/__init__.py
nemo/collections/asr/parts/k2/classes.py
nemo/collections/asr/parts/k2/grad_utils.py
nemo/collections/asr/parts/k2/graph_compilers.py
nemo/collections/asr/parts/k2/graph_decoders.py
nemo/collections/asr/parts/k2/graph_transducer.py
nemo/collections/asr/parts/k2/loss_mixins.py
nemo/collections/asr/parts/k2/map_loss.py
nemo/collections/asr/parts/k2/ml_loss.py
nemo/collections/asr/parts/k2/rnnt_logprobs.py
nemo/collections/asr/parts/k2/rnnt_logprobs_triton.py
nemo/collections/asr/parts/k2/topologies.py
nemo/collections/asr/parts/k2/utils.py
nemo/collections/asr/parts/k2/w_transducer.py
nemo/collections/asr/parts/mixins/__init__.py
nemo/collections/asr/parts/mixins/asr_adapter_mixins.py
nemo/collections/asr/parts/mixins/diarization.py
nemo/collections/asr/parts/mixins/interctc_mixin.py
nemo/collections/asr/parts/mixins/mixins.py
nemo/collections/asr/parts/mixins/streaming.py
nemo/collections/asr/parts/mixins/transcription.py
nemo/collections/asr/parts/numba/__init__.py
nemo/collections/asr/parts/numba/rnnt_loss/__init__.py
nemo/collections/asr/parts/numba/rnnt_loss/rnnt.py
nemo/collections/asr/parts/numba/rnnt_loss/rnnt_numpy.py
nemo/collections/asr/parts/numba/rnnt_loss/rnnt_pytorch.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/__init__.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/global_constants.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/rnnt_helper.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cpu_utils/__init__.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cpu_utils/cpu_rnnt.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cuda_utils/__init__.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cuda_utils/gpu_rnnt.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cuda_utils/gpu_rnnt_kernel.py
nemo/collections/asr/parts/numba/rnnt_loss/utils/cuda_utils/reduce.py
nemo/collections/asr/parts/numba/spec_augment/__init__.py
nemo/collections/asr/parts/numba/spec_augment/spec_aug_numba.py
nemo/collections/asr/parts/preprocessing/__init__.py
nemo/collections/asr/parts/preprocessing/feature_loader.py
nemo/collections/asr/parts/preprocessing/features.py
nemo/collections/asr/parts/preprocessing/perturb.py
nemo/collections/asr/parts/preprocessing/segment.py
nemo/collections/asr/parts/submodules/__init__.py
nemo/collections/asr/parts/submodules/batchnorm.py
nemo/collections/asr/parts/submodules/causal_convs.py
nemo/collections/asr/parts/submodules/classifier.py
nemo/collections/asr/parts/submodules/conformer_modules.py
nemo/collections/asr/parts/submodules/ctc_batched_beam_decoding.py
nemo/collections/asr/parts/submodules/ctc_beam_decoding.py
nemo/collections/asr/parts/submodules/ctc_decoding.py
nemo/collections/asr/parts/submodules/ctc_greedy_decoding.py
nemo/collections/asr/parts/submodules/cuda_graph_rnnt_greedy_decoding.py
nemo/collections/asr/parts/submodules/jasper.py
nemo/collections/asr/parts/submodules/multi_head_attention.py
nemo/collections/asr/parts/submodules/multitask_beam_decoding.py
nemo/collections/asr/parts/submodules/multitask_decoding.py
nemo/collections/asr/parts/submodules/multitask_greedy_decoding.py
nemo/collections/asr/parts/submodules/rnnt_beam_decoding.py
nemo/collections/asr/parts/submodules/rnnt_decoding.py
nemo/collections/asr/parts/submodules/rnnt_greedy_decoding.py
nemo/collections/asr/parts/submodules/rnnt_maes_batched_computer.py
nemo/collections/asr/parts/submodules/rnnt_malsd_batched_computer.py
nemo/collections/asr/parts/submodules/spectr_augment.py
nemo/collections/asr/parts/submodules/squeezeformer_modules.py
nemo/collections/asr/parts/submodules/ssl_quantizers.py
nemo/collections/asr/parts/submodules/stateless_net.py
nemo/collections/asr/parts/submodules/subsampling.py
nemo/collections/asr/parts/submodules/tdnn_attention.py
nemo/collections/asr/parts/submodules/tdt_beam_decoding.py
nemo/collections/asr/parts/submodules/tdt_malsd_batched_computer.py
nemo/collections/asr/parts/submodules/token_classifier.py
nemo/collections/asr/parts/submodules/wfst_decoder.py
nemo/collections/asr/parts/submodules/adapters/__init__.py
nemo/collections/asr/parts/submodules/adapters/attention_adapter_mixin.py
nemo/collections/asr/parts/submodules/adapters/multi_head_attention_adapter_module.py
nemo/collections/asr/parts/submodules/adapters/transformer_multi_head_attention_adapter_module.py
nemo/collections/asr/parts/submodules/ngram_lm/__init__.py
nemo/collections/asr/parts/submodules/ngram_lm/constants.py
nemo/collections/asr/parts/submodules/ngram_lm/kenlm_utils.py
nemo/collections/asr/parts/submodules/ngram_lm/ngram_lm_batched.py
nemo/collections/asr/parts/submodules/ngram_lm/ngram_lm_triton.py
nemo/collections/asr/parts/submodules/transducer_decoding/__init__.py
nemo/collections/asr/parts/submodules/transducer_decoding/label_looping_base.py
nemo/collections/asr/parts/submodules/transducer_decoding/rnnt_label_looping.py
nemo/collections/asr/parts/submodules/transducer_decoding/tdt_label_looping.py
nemo/collections/asr/parts/utils/__init__.py
nemo/collections/asr/parts/utils/activations.py
nemo/collections/asr/parts/utils/adapter_utils.py
nemo/collections/asr/parts/utils/asr_batching.py
nemo/collections/asr/parts/utils/asr_confidence_benchmarking_utils.py
nemo/collections/asr/parts/utils/asr_confidence_utils.py
nemo/collections/asr/parts/utils/asr_module_utils.py
nemo/collections/asr/parts/utils/asr_multispeaker_utils.py
nemo/collections/asr/parts/utils/batched_beam_decoding_utils.py
nemo/collections/asr/parts/utils/confidence_metrics.py
nemo/collections/asr/parts/utils/data_simulation_utils.py
nemo/collections/asr/parts/utils/decoder_timestamps_utils.py
nemo/collections/asr/parts/utils/diarization_utils.py
nemo/collections/asr/parts/utils/eval_utils.py
nemo/collections/asr/parts/utils/longform_clustering.py
nemo/collections/asr/parts/utils/manifest_utils.py
nemo/collections/asr/parts/utils/numba_utils.py
nemo/collections/asr/parts/utils/offline_clustering.py
nemo/collections/asr/parts/utils/online_clustering.py
nemo/collections/asr/parts/utils/optimization_utils.py
nemo/collections/asr/parts/utils/regularization_utils.py
nemo/collections/asr/parts/utils/rnnt_utils.py
nemo/collections/asr/parts/utils/slu_utils.py
nemo/collections/asr/parts/utils/speaker_utils.py
nemo/collections/asr/parts/utils/streaming_utils.py
nemo/collections/asr/parts/utils/timestamp_utils.py
nemo/collections/asr/parts/utils/transcribe_utils.py
nemo/collections/asr/parts/utils/vad_utils.py
nemo/collections/asr/parts/utils/wfst_utils.py
nemo/collections/audio/__init__.py
nemo/collections/audio/data/__init__.py
nemo/collections/audio/data/audio_to_audio.py
nemo/collections/audio/data/audio_to_audio_dataset.py
nemo/collections/audio/data/audio_to_audio_lhotse.py
nemo/collections/audio/data/data_simulation.py
nemo/collections/audio/losses/__init__.py
nemo/collections/audio/losses/audio.py
nemo/collections/audio/losses/maxine/__init__.py
nemo/collections/audio/losses/maxine/losses_combined.py
nemo/collections/audio/losses/maxine/sisnr_loss.py
nemo/collections/audio/metrics/__init__.py
nemo/collections/audio/metrics/audio.py
nemo/collections/audio/metrics/squim.py
nemo/collections/audio/models/__init__.py
nemo/collections/audio/models/audio_to_audio.py
nemo/collections/audio/models/enhancement.py
nemo/collections/audio/models/maxine/__init__.py
nemo/collections/audio/models/maxine/bnr.py
nemo/collections/audio/modules/__init__.py
nemo/collections/audio/modules/features.py
nemo/collections/audio/modules/masking.py
nemo/collections/audio/modules/projections.py
nemo/collections/audio/modules/ssl_pretrain_masking.py
nemo/collections/audio/modules/transforms.py
nemo/collections/audio/parts/__init__.py
nemo/collections/audio/parts/submodules/__init__.py
nemo/collections/audio/parts/submodules/conformer.py
nemo/collections/audio/parts/submodules/diffusion.py
nemo/collections/audio/parts/submodules/flow.py
nemo/collections/audio/parts/submodules/multichannel.py
nemo/collections/audio/parts/submodules/ncsnpp.py
nemo/collections/audio/parts/submodules/schroedinger_bridge.py
nemo/collections/audio/parts/submodules/transformerunet.py
nemo/collections/audio/parts/utils/__init__.py
nemo/collections/audio/parts/utils/audio.py
nemo/collections/audio/parts/utils/callbacks.py
nemo/collections/audio/parts/utils/maxine.py
nemo/collections/audio/parts/utils/resampling.py
nemo/collections/avlm/__init__.py
nemo/collections/avlm/data/__init__.py
nemo/collections/avlm/data/mock.py
nemo/collections/avlm/data/energon/__init__.py
nemo/collections/avlm/data/energon/avlm_data_module.py
nemo/collections/avlm/data/energon/avlm_sample_config.py
nemo/collections/avlm/data/energon/avlm_task_encoder.py
nemo/collections/avlm/data/energon/calculate_media_seq_length.py
nemo/collections/avlm/model/__init__.py
nemo/collections/avlm/model/avlm.py
nemo/collections/avlm/model/base.py
nemo/collections/avlm/recipes/__init__.py
nemo/collections/avlm/recipes/avlm_8b.py
nemo/collections/common/__init__.py
nemo/collections/common/callbacks/__init__.py
nemo/collections/common/callbacks/callbacks.py
nemo/collections/common/callbacks/ema.py
nemo/collections/common/data/__init__.py
nemo/collections/common/data/dataset.py
nemo/collections/common/data/prompt_fn.py
nemo/collections/common/data/utils.py
nemo/collections/common/data/lhotse/__init__.py
nemo/collections/common/data/lhotse/cutset.py
nemo/collections/common/data/lhotse/dataloader.py
nemo/collections/common/data/lhotse/nemo_adapters.py
nemo/collections/common/data/lhotse/sampling.py
nemo/collections/common/data/lhotse/text_adapters.py
nemo/collections/common/losses/__init__.py
nemo/collections/common/losses/aggregator.py
nemo/collections/common/losses/bce_logits_loss.py
nemo/collections/common/losses/cross_entropy.py
nemo/collections/common/losses/mse_loss.py
nemo/collections/common/losses/multi_similarity_loss.py
nemo/collections/common/losses/smoothed_cross_entropy.py
nemo/collections/common/losses/spanning_loss.py
nemo/collections/common/metrics/__init__.py
nemo/collections/common/metrics/classification_accuracy.py
nemo/collections/common/metrics/global_average_loss_metric.py
nemo/collections/common/metrics/metric_string_to_torchmetric.py
nemo/collections/common/metrics/perf_metrics.py
nemo/collections/common/metrics/perplexity.py
nemo/collections/common/metrics/punct_er.py
nemo/collections/common/parts/__init__.py
nemo/collections/common/parts/adapter_modules.py
nemo/collections/common/parts/mlm_scorer.py
nemo/collections/common/parts/multi_layer_perceptron.py
nemo/collections/common/parts/nemo_run_utils.py
nemo/collections/common/parts/optional_cuda_graphs.py
nemo/collections/common/parts/patch_utils.py
nemo/collections/common/parts/perf_metrics_utils.py
nemo/collections/common/parts/ptl_overrides.py
nemo/collections/common/parts/rnn.py
nemo/collections/common/parts/skills_utils.py
nemo/collections/common/parts/transformer_utils.py
nemo/collections/common/parts/utils.py
nemo/collections/common/parts/preprocessing/__init__.py
nemo/collections/common/parts/preprocessing/cleaners.py
nemo/collections/common/parts/preprocessing/collections.py
nemo/collections/common/parts/preprocessing/manifest.py
nemo/collections/common/parts/preprocessing/parsers.py
nemo/collections/common/prompts/__init__.py
nemo/collections/common/prompts/canary.py
nemo/collections/common/prompts/canary2.py
nemo/collections/common/prompts/example.py
nemo/collections/common/prompts/formatter.py
nemo/collections/common/prompts/gemma.py
nemo/collections/common/prompts/llama.py
nemo/collections/common/prompts/mistral.py
nemo/collections/common/prompts/phi2.py
nemo/collections/common/prompts/plain.py
nemo/collections/common/prompts/t5nmt.py
nemo/collections/common/tokenizers/__init__.py
nemo/collections/common/tokenizers/aggregate_tokenizer.py
nemo/collections/common/tokenizers/bytelevel_tokenizers.py
nemo/collections/common/tokenizers/canary_tokenizer.py
nemo/collections/common/tokenizers/char_tokenizer.py
nemo/collections/common/tokenizers/chat_template_mixin.py
nemo/collections/common/tokenizers/chinese_tokenizers.py
nemo/collections/common/tokenizers/column_coder.py
nemo/collections/common/tokenizers/en_ja_tokenizers.py
nemo/collections/common/tokenizers/fairseq_tokenizer.py
nemo/collections/common/tokenizers/indic_tokenizers.py
nemo/collections/common/tokenizers/moses_tokenizers.py
nemo/collections/common/tokenizers/null_tokenizer.py
nemo/collections/common/tokenizers/regex_tokenizer.py
nemo/collections/common/tokenizers/sentencepiece_tokenizer.py
nemo/collections/common/tokenizers/tabular_tokenizer.py
nemo/collections/common/tokenizers/tiktoken_tokenizer.py
nemo/collections/common/tokenizers/tokenizer_spec.py
nemo/collections/common/tokenizers/word_tokenizer.py
nemo/collections/common/tokenizers/youtokentome_tokenizer.py
nemo/collections/common/tokenizers/huggingface/__init__.py
nemo/collections/common/tokenizers/huggingface/auto_tokenizer.py
nemo/collections/common/tokenizers/text_to_speech/__init__.py
nemo/collections/common/tokenizers/text_to_speech/ipa_lexicon.py
nemo/collections/common/tokenizers/text_to_speech/tokenizer_utils.py
nemo/collections/common/tokenizers/text_to_speech/tokenizer_wrapper.py
nemo/collections/common/tokenizers/text_to_speech/tts_tokenizers.py
nemo/collections/common/video_tokenizers/__init__.py
nemo/collections/common/video_tokenizers/cosmos_tokenizer.py
nemo/collections/common/video_tokenizers/cosmos_trt_run.py
nemo/collections/common/video_tokenizers/utils.py
nemo/collections/common/video_tokenizers/modules/__init__.py
nemo/collections/common/video_tokenizers/modules/distributions.py
nemo/collections/common/video_tokenizers/modules/layers2d.py
nemo/collections/common/video_tokenizers/modules/layers3d.py
nemo/collections/common/video_tokenizers/modules/patching.py
nemo/collections/common/video_tokenizers/modules/quantizers.py
nemo/collections/common/video_tokenizers/modules/utils.py
nemo/collections/common/video_tokenizers/networks/__init__.py
nemo/collections/common/video_tokenizers/networks/configs.py
nemo/collections/common/video_tokenizers/networks/continuous_image.py
nemo/collections/common/video_tokenizers/networks/continuous_video.py
nemo/collections/common/video_tokenizers/networks/discrete_image.py
nemo/collections/common/video_tokenizers/networks/discrete_video.py
nemo/collections/diffusion/__init__.py
nemo/collections/diffusion/data/__init__.py
nemo/collections/diffusion/data/diffusion_energon_datamodule.py
nemo/collections/diffusion/data/diffusion_fake_datamodule.py
nemo/collections/diffusion/data/diffusion_mock_datamodule.py
nemo/collections/diffusion/data/diffusion_taskencoder.py
nemo/collections/diffusion/data/prepare_energon_dataset.py
nemo/collections/diffusion/encoders/__init__.py
nemo/collections/diffusion/encoders/conditioner.py
nemo/collections/diffusion/models/__init__.py
nemo/collections/diffusion/models/model.py
nemo/collections/diffusion/models/dit/__init__.py
nemo/collections/diffusion/models/dit/dit_attention.py
nemo/collections/diffusion/models/dit/dit_embeddings.py
nemo/collections/diffusion/models/dit/dit_layer_spec.py
nemo/collections/diffusion/models/dit/dit_model.py
nemo/collections/diffusion/models/dit_llama/__init__.py
nemo/collections/diffusion/models/dit_llama/dit_llama_layer_spec.py
nemo/collections/diffusion/models/dit_llama/dit_llama_model.py
nemo/collections/diffusion/models/flux/__init__.py
nemo/collections/diffusion/models/flux/layers.py
nemo/collections/diffusion/models/flux/model.py
nemo/collections/diffusion/models/flux/pipeline.py
nemo/collections/diffusion/models/flux_controlnet/__init__.py
nemo/collections/diffusion/models/flux_controlnet/layers.py
nemo/collections/diffusion/models/flux_controlnet/model.py
nemo/collections/diffusion/recipes/__init__.py
nemo/collections/diffusion/recipes/flux_12b.py
nemo/collections/diffusion/recipes/flux_535m.py
nemo/collections/diffusion/sampler/__init__.py
nemo/collections/diffusion/sampler/batch_ops.py
nemo/collections/diffusion/sampler/context_parallel.py
nemo/collections/diffusion/sampler/edm/__init__.py
nemo/collections/diffusion/sampler/edm/edm.py
nemo/collections/diffusion/sampler/edm/edm_pipeline.py
nemo/collections/diffusion/sampler/flow_matching/__init__.py
nemo/collections/diffusion/sampler/flow_matching/flow_match_euler_discrete.py
nemo/collections/diffusion/utils/__init__.py
nemo/collections/diffusion/utils/flux_ckpt_converter.py
nemo/collections/diffusion/utils/flux_pipeline_utils.py
nemo/collections/diffusion/utils/mcore_parallel_utils.py
nemo/collections/diffusion/vae/__init__.py
nemo/collections/diffusion/vae/autoencoder.py
nemo/collections/diffusion/vae/autovae.py
nemo/collections/diffusion/vae/blocks.py
nemo/collections/diffusion/vae/contperceptual_loss.py
nemo/collections/diffusion/vae/diffusers_vae.py
nemo/collections/diffusion/vae/test_autovae.py
nemo/collections/diffusion/vae/train_vae.py
nemo/collections/diffusion/vae/validate_vae.py
nemo/collections/llm/__init__.py
nemo/collections/llm/api.py
nemo/collections/llm/utils.py
nemo/collections/llm/bert/__init__.py
nemo/collections/llm/bert/loss.py
nemo/collections/llm/bert/data/__init__.py
nemo/collections/llm/bert/data/core.py
nemo/collections/llm/bert/data/fine_tuning.py
nemo/collections/llm/bert/data/mock.py
nemo/collections/llm/bert/data/pre_training.py
nemo/collections/llm/bert/data/specter.py
nemo/collections/llm/bert/model/__init__.py
nemo/collections/llm/bert/model/base.py
nemo/collections/llm/bert/model/bert.py
nemo/collections/llm/bert/model/bert_spec.py
nemo/collections/llm/bert/model/embedding.py
nemo/collections/llm/deploy/__init__.py
nemo/collections/llm/deploy/base.py
nemo/collections/llm/evaluation/__init__.py
nemo/collections/llm/evaluation/api.py
nemo/collections/llm/evaluation/base.py
nemo/collections/llm/evaluation/adapters/__init__.py
nemo/collections/llm/evaluation/adapters/server.py
nemo/collections/llm/evaluation/adapters/utils.py
nemo/collections/llm/evaluation/adapters/interceptors/__init__.py
nemo/collections/llm/evaluation/adapters/interceptors/endpoint_interceptor.py
nemo/collections/llm/evaluation/adapters/interceptors/logging_interceptor.py
nemo/collections/llm/evaluation/adapters/interceptors/reasoning_interceptor.py
nemo/collections/llm/evaluation/adapters/interceptors/system_message_interceptor.py
nemo/collections/llm/evaluation/adapters/interceptors/types.py
nemo/collections/llm/fn/__init__.py
nemo/collections/llm/fn/activation.py
nemo/collections/llm/fn/base.py
nemo/collections/llm/fn/mixin.py
nemo/collections/llm/gpt/__init__.py
nemo/collections/llm/gpt/data/__init__.py
nemo/collections/llm/gpt/data/alpaca.py
nemo/collections/llm/gpt/data/api.py
nemo/collections/llm/gpt/data/chat.py
nemo/collections/llm/gpt/data/core.py
nemo/collections/llm/gpt/data/dolly.py
nemo/collections/llm/gpt/data/fine_tuning.py
nemo/collections/llm/gpt/data/hf_dataset.py
nemo/collections/llm/gpt/data/hf_dataset_packed_sequence.py
nemo/collections/llm/gpt/data/mlperf_govreport.py
nemo/collections/llm/gpt/data/mock.py
nemo/collections/llm/gpt/data/packed_sequence.py
nemo/collections/llm/gpt/data/pre_training.py
nemo/collections/llm/gpt/data/reranker.py
nemo/collections/llm/gpt/data/retrieval.py
nemo/collections/llm/gpt/data/squad.py
nemo/collections/llm/gpt/data/utils.py
nemo/collections/llm/gpt/data/megatron/__init__.py
nemo/collections/llm/gpt/data/megatron/hyena/__init__.py
nemo/collections/llm/gpt/data/megatron/hyena/config.py
nemo/collections/llm/gpt/data/megatron/hyena/evo2_dataset.py
nemo/collections/llm/gpt/model/__init__.py
nemo/collections/llm/gpt/model/baichuan.py
nemo/collections/llm/gpt/model/base.py
nemo/collections/llm/gpt/model/chatglm.py
nemo/collections/llm/gpt/model/deepseek.py
nemo/collections/llm/gpt/model/gemma.py
nemo/collections/llm/gpt/model/gemma2.py
nemo/collections/llm/gpt/model/gemma3.py
nemo/collections/llm/gpt/model/hf_auto_model_for_causal_lm.py
nemo/collections/llm/gpt/model/hf_llama_embedding.py
nemo/collections/llm/gpt/model/hyena.py
nemo/collections/llm/gpt/model/llama.py
nemo/collections/llm/gpt/model/llama4_utils.py
nemo/collections/llm/gpt/model/llama_embedding.py
nemo/collections/llm/gpt/model/llama_nemotron.py
nemo/collections/llm/gpt/model/llama_nemotron_config.py
nemo/collections/llm/gpt/model/mistral.py
nemo/collections/llm/gpt/model/mixtral.py
nemo/collections/llm/gpt/model/nemotron.py
nemo/collections/llm/gpt/model/phi3mini.py
nemo/collections/llm/gpt/model/qwen2.py
nemo/collections/llm/gpt/model/qwen3.py
nemo/collections/llm/gpt/model/reranker.py
nemo/collections/llm/gpt/model/ssm.py
nemo/collections/llm/gpt/model/starcoder.py
nemo/collections/llm/gpt/model/starcoder2.py
nemo/collections/llm/gpt/model/megatron/__init__.py
nemo/collections/llm/gpt/model/megatron/hyena/__init__.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_block.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_config.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_hybrid_layer_allocation.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_layer.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_layer_specs.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_mixer.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_model.py
nemo/collections/llm/gpt/model/megatron/hyena/hyena_utils.py
nemo/collections/llm/inference/__init__.py
nemo/collections/llm/inference/base.py
nemo/collections/llm/modelopt/__init__.py
nemo/collections/llm/modelopt/model_utils.py
nemo/collections/llm/modelopt/distill/__init__.py
nemo/collections/llm/modelopt/distill/loss.py
nemo/collections/llm/modelopt/distill/model.py
nemo/collections/llm/modelopt/distill/utils.py
nemo/collections/llm/modelopt/prune/__init__.py
nemo/collections/llm/modelopt/prune/pruner.py
nemo/collections/llm/modelopt/quantization/__init__.py
nemo/collections/llm/modelopt/quantization/quant_cfg_choices.py
nemo/collections/llm/modelopt/quantization/quantizer.py
nemo/collections/llm/modelopt/quantization/utils.py
nemo/collections/llm/modelopt/recipes/__init__.py
nemo/collections/llm/modelopt/recipes/distillation_recipe.py
nemo/collections/llm/modelopt/recipes/prune_recipe.py
nemo/collections/llm/modelopt/speculative/__init__.py
nemo/collections/llm/modelopt/speculative/model_transform.py
nemo/collections/llm/peft/__init__.py
nemo/collections/llm/peft/api.py
nemo/collections/llm/peft/canonical_lora.py
nemo/collections/llm/peft/dora.py
nemo/collections/llm/peft/lora.py
nemo/collections/llm/peft/module_matcher.py
nemo/collections/llm/peft/utils.py
nemo/collections/llm/recipes/__init__.py
nemo/collections/llm/recipes/baichuan2_7b.py
nemo/collections/llm/recipes/bert.py
nemo/collections/llm/recipes/bert_110m.py
nemo/collections/llm/recipes/bert_340m.py
nemo/collections/llm/recipes/bert_embedding.py
nemo/collections/llm/recipes/chatglm3_6b.py
nemo/collections/llm/recipes/deepseek.py
nemo/collections/llm/recipes/deepseek_v2.py
nemo/collections/llm/recipes/deepseek_v2_lite.py
nemo/collections/llm/recipes/deepseek_v3.py
nemo/collections/llm/recipes/e5_340m.py
nemo/collections/llm/recipes/finetune_default.py
nemo/collections/llm/recipes/gemma2.py
nemo/collections/llm/recipes/gemma2_27b.py
nemo/collections/llm/recipes/gemma2_2b.py
nemo/collections/llm/recipes/gemma2_9b.py
nemo/collections/llm/recipes/gemma3_1b.py
nemo/collections/llm/recipes/gemma_2b.py
nemo/collections/llm/recipes/gemma_7b.py
nemo/collections/llm/recipes/gpt3_175b.py
nemo/collections/llm/recipes/hf_auto_model_for_causal_lm.py
nemo/collections/llm/recipes/hyena_1b.py
nemo/collections/llm/recipes/hyena_40b.py
nemo/collections/llm/recipes/hyena_7b.py
nemo/collections/llm/recipes/hyena_base.py
nemo/collections/llm/recipes/llama2_7b.py
nemo/collections/llm/recipes/llama31_405b.py
nemo/collections/llm/recipes/llama31_70b.py
nemo/collections/llm/recipes/llama31_8b.py
nemo/collections/llm/recipes/llama31_nemotron_70b.py
nemo/collections/llm/recipes/llama31_nemotron_nano_8b.py
nemo/collections/llm/recipes/llama31_nemotron_ultra_253b.py
nemo/collections/llm/recipes/llama32_1b.py
nemo/collections/llm/recipes/llama32_3b.py
nemo/collections/llm/recipes/llama33_nemotron_super_49b.py
nemo/collections/llm/recipes/llama3_70b.py
nemo/collections/llm/recipes/llama3_70b_16k.py
nemo/collections/llm/recipes/llama3_70b_64k.py
nemo/collections/llm/recipes/llama3_8b.py
nemo/collections/llm/recipes/llama3_8b_128k.py
nemo/collections/llm/recipes/llama3_8b_16k.py
nemo/collections/llm/recipes/llama3_8b_64k.py
nemo/collections/llm/recipes/llama4_e128.py
nemo/collections/llm/recipes/llama4_e16.py
nemo/collections/llm/recipes/llama_embedding_1b.py
nemo/collections/llm/recipes/llama_embedding_3b.py
nemo/collections/llm/recipes/llama_reranker_1b.py
nemo/collections/llm/recipes/mamba2_130m.py
nemo/collections/llm/recipes/mamba2_1_3b.py
nemo/collections/llm/recipes/mamba2_2_7b.py
nemo/collections/llm/recipes/mamba2_370m.py
nemo/collections/llm/recipes/mamba2_780m.py
nemo/collections/llm/recipes/mamba2_8b.py
nemo/collections/llm/recipes/mamba2_hybrid_8b.py
nemo/collections/llm/recipes/mistral_7b.py
nemo/collections/llm/recipes/mistral_nemo_12b.py
nemo/collections/llm/recipes/mixtral_8x22b.py
nemo/collections/llm/recipes/mixtral_8x22b_64k.py
nemo/collections/llm/recipes/mixtral_8x7b.py
nemo/collections/llm/recipes/mixtral_8x7b_16k.py
nemo/collections/llm/recipes/mixtral_8x7b_64k.py
nemo/collections/llm/recipes/nemotron.py
nemo/collections/llm/recipes/nemotron3_22b.py
nemo/collections/llm/recipes/nemotron3_22b_16k.py
nemo/collections/llm/recipes/nemotron3_22b_64k.py
nemo/collections/llm/recipes/nemotron3_4b.py
nemo/collections/llm/recipes/nemotron3_8b.py
nemo/collections/llm/recipes/nemotron4_15b.py
nemo/collections/llm/recipes/nemotron4_15b_16k.py
nemo/collections/llm/recipes/nemotron4_15b_64k.py
nemo/collections/llm/recipes/nemotron4_340b.py
nemo/collections/llm/recipes/nemotronh_47b.py
nemo/collections/llm/recipes/nemotronh_4b.py
nemo/collections/llm/recipes/nemotronh_56b.py
nemo/collections/llm/recipes/nemotronh_8b.py
nemo/collections/llm/recipes/phi3_mini_4k_instruct.py
nemo/collections/llm/recipes/qwen2.py
nemo/collections/llm/recipes/qwen25_14b.py
nemo/collections/llm/recipes/qwen25_1p5b.py
nemo/collections/llm/recipes/qwen25_32b.py
nemo/collections/llm/recipes/qwen25_500m.py
nemo/collections/llm/recipes/qwen25_72b.py
nemo/collections/llm/recipes/qwen25_7b.py
nemo/collections/llm/recipes/qwen2_1p5b.py
nemo/collections/llm/recipes/qwen2_500m.py
nemo/collections/llm/recipes/qwen2_72b.py
nemo/collections/llm/recipes/qwen2_7b.py
nemo/collections/llm/recipes/qwen3.py
nemo/collections/llm/recipes/qwen3_14b.py
nemo/collections/llm/recipes/qwen3_1p7b.py
nemo/collections/llm/recipes/qwen3_235b_a22b.py
nemo/collections/llm/recipes/qwen3_30b_a3b.py
nemo/collections/llm/recipes/qwen3_32b.py
nemo/collections/llm/recipes/qwen3_4b.py
nemo/collections/llm/recipes/qwen3_600m.py
nemo/collections/llm/recipes/qwen3_8b.py
nemo/collections/llm/recipes/starcoder2.py
nemo/collections/llm/recipes/starcoder2_15b.py
nemo/collections/llm/recipes/starcoder2_3b.py
nemo/collections/llm/recipes/starcoder2_7b.py
nemo/collections/llm/recipes/starcoder_15b.py
nemo/collections/llm/recipes/t5_11b.py
nemo/collections/llm/recipes/t5_220m.py
nemo/collections/llm/recipes/t5_3b.py
nemo/collections/llm/recipes/callbacks/__init__.py
nemo/collections/llm/recipes/callbacks/common.py
nemo/collections/llm/recipes/log/__init__.py
nemo/collections/llm/recipes/log/default.py
nemo/collections/llm/recipes/optim/__init__.py
nemo/collections/llm/recipes/optim/adam.py
nemo/collections/llm/recipes/optim/sgd.py
nemo/collections/llm/recipes/precision/__init__.py
nemo/collections/llm/recipes/precision/mixed_precision.py
nemo/collections/llm/recipes/run/__init__.py
nemo/collections/llm/recipes/run/executor.py
nemo/collections/llm/recipes/tp_overlap_configs/__init__.py
nemo/collections/llm/recipes/tp_overlap_configs/userbuffers.py
nemo/collections/llm/t5/__init__.py
nemo/collections/llm/t5/data/__init__.py
nemo/collections/llm/t5/data/core.py
nemo/collections/llm/t5/data/fine_tuning.py
nemo/collections/llm/t5/data/mock.py
nemo/collections/llm/t5/data/pre_training.py
nemo/collections/llm/t5/data/squad.py
nemo/collections/llm/t5/model/__init__.py
nemo/collections/llm/t5/model/t5.py
nemo/collections/llm/tools/__init__.py
nemo/collections/llm/tools/auto_configurator/__init__.py
nemo/collections/llm/tools/auto_configurator/runner.py
nemo/collections/llm/tools/auto_configurator/core/__init__.py
nemo/collections/llm/tools/auto_configurator/core/base_config.py
nemo/collections/llm/tools/auto_configurator/core/calculate_performance.py
nemo/collections/llm/tools/auto_configurator/core/training_config.py
nemo/collections/llm/tools/auto_configurator/core/utils.py
nemo/collections/multimodal/__init__.py
nemo/collections/multimodal/data/__init__.py
nemo/collections/multimodal/data/clip/__init__.py
nemo/collections/multimodal/data/clip/clip_dataset.py
nemo/collections/multimodal/data/clip/imagenet_zeroshot_data.py
nemo/collections/multimodal/data/clip/augmentations/__init__.py
nemo/collections/multimodal/data/clip/augmentations/augmentations.py
nemo/collections/multimodal/data/common/__init__.py
nemo/collections/multimodal/data/common/data_samplers.py
nemo/collections/multimodal/data/common/utils.py
nemo/collections/multimodal/data/common/webdataset.py
nemo/collections/multimodal/data/common/webdataset_s3.py
nemo/collections/multimodal/data/controlnet/__init__.py
nemo/collections/multimodal/data/controlnet/controlnet_dataset.py
nemo/collections/multimodal/data/dreambooth/__init__.py
nemo/collections/multimodal/data/dreambooth/dreambooth_dataset.py
nemo/collections/multimodal/data/energon/__init__.py
nemo/collections/multimodal/data/energon/base.py
nemo/collections/multimodal/data/energon/config.py
nemo/collections/multimodal/data/energon/conversation.py
nemo/collections/multimodal/data/energon/sample_encoder.py
nemo/collections/multimodal/data/energon/task_encoder.py
nemo/collections/multimodal/data/imagen/__init__.py
nemo/collections/multimodal/data/imagen/imagen_dataset.py
nemo/collections/multimodal/data/imagen/augmentations/__init__.py
nemo/collections/multimodal/data/imagen/augmentations/augmentations.py
nemo/collections/multimodal/data/imagen/augmentations/corruption.py
nemo/collections/multimodal/data/instruct_pix2pix/__init__.py
nemo/collections/multimodal/data/instruct_pix2pix/edit_dataset.py
nemo/collections/multimodal/data/nerf/__init__.py
nemo/collections/multimodal/data/nerf/cameras.py
nemo/collections/multimodal/data/nerf/circle_poses.py
nemo/collections/multimodal/data/nerf/random_poses.py
nemo/collections/multimodal/data/nerf/utils.py
nemo/collections/multimodal/data/neva/__init__.py
nemo/collections/multimodal/data/neva/conversation.py
nemo/collections/multimodal/data/neva/neva_dataset.py
nemo/collections/multimodal/data/neva/neva_energon_dataset.py
nemo/collections/multimodal/data/nsfw/__init__.py
nemo/collections/multimodal/data/nsfw/nsfw_dataset.py
nemo/collections/multimodal/data/stable_diffusion/__init__.py
nemo/collections/multimodal/data/stable_diffusion/stable_diffusion_dataset.py
nemo/collections/multimodal/data/stable_diffusion/augmentation/__init__.py
nemo/collections/multimodal/data/stable_diffusion/augmentation/augmentations.py
nemo/collections/multimodal/losses/__init__.py
nemo/collections/multimodal/losses/clip_loss.py
nemo/collections/multimodal/losses/siglip_loss.py
nemo/collections/multimodal/models/__init__.py
nemo/collections/multimodal/models/multimodal_llm/__init__.py
nemo/collections/multimodal/models/multimodal_llm/neva/__init__.py
nemo/collections/multimodal/models/multimodal_llm/neva/neva_model.py
nemo/collections/multimodal/models/nerf/__init__.py
nemo/collections/multimodal/models/nerf/base.py
nemo/collections/multimodal/models/nerf/dreamfusion.py
nemo/collections/multimodal/models/nerf/txt2nerf_base.py
nemo/collections/multimodal/models/text_to_image/__init__.py
nemo/collections/multimodal/models/text_to_image/controlnet/__init__.py
nemo/collections/multimodal/models/text_to_image/controlnet/controlnet.py
nemo/collections/multimodal/models/text_to_image/controlnet/util.py
nemo/collections/multimodal/models/text_to_image/dreambooth/__init__.py
nemo/collections/multimodal/models/text_to_image/dreambooth/dreambooth.py
nemo/collections/multimodal/models/text_to_image/dreambooth/util.py
nemo/collections/multimodal/models/text_to_image/imagen/__init__.py
nemo/collections/multimodal/models/text_to_image/imagen/imagen.py
nemo/collections/multimodal/models/text_to_image/imagen/imagen_pipeline.py
nemo/collections/multimodal/models/text_to_image/imagen/precond.py
nemo/collections/multimodal/models/text_to_image/instruct_pix2pix/__init__.py
nemo/collections/multimodal/models/text_to_image/instruct_pix2pix/ldm/__init__.py
nemo/collections/multimodal/models/text_to_image/instruct_pix2pix/ldm/ddpm_edit.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/__init__.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/diffusion_engine.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/diffusion_model.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/ldm_config.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/ldm/__init__.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/ldm/autoencoder.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/ldm/ddpm.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/__init__.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/base_sampler.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/ddim.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/dpmsolver.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/k_diffusion.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/para_ddim.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/plms.py
nemo/collections/multimodal/models/text_to_image/stable_diffusion/samplers/sampler_dpm.py
nemo/collections/multimodal/models/vision_language_foundation/__init__.py
nemo/collections/multimodal/models/vision_language_foundation/megatron_nsfw_clip_models.py
nemo/collections/multimodal/models/vision_language_foundation/clip/__init__.py
nemo/collections/multimodal/models/vision_language_foundation/clip/megatron_clip_models.py
nemo/collections/multimodal/modules/__init__.py
nemo/collections/multimodal/modules/imagen/__init__.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/__init__.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/attention.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/attention_alt.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/blocks.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/embs.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/layers.py
nemo/collections/multimodal/modules/imagen/diffusionmodules/nets.py
nemo/collections/multimodal/modules/imagen/encoder/__init__.py
nemo/collections/multimodal/modules/imagen/encoder/t5encoder.py
nemo/collections/multimodal/modules/imagen/sampler/__init__.py
nemo/collections/multimodal/modules/imagen/sampler/batch_ops.py
nemo/collections/multimodal/modules/imagen/sampler/continuous_ddpm.py
nemo/collections/multimodal/modules/imagen/sampler/sampler.py
nemo/collections/multimodal/modules/nerf/__init__.py
nemo/collections/multimodal/modules/nerf/background/__init__.py
nemo/collections/multimodal/modules/nerf/background/nerf_background_base.py
nemo/collections/multimodal/modules/nerf/background/random_background.py
nemo/collections/multimodal/modules/nerf/background/static_background.py
nemo/collections/multimodal/modules/nerf/background/tcnn_background.py
nemo/collections/multimodal/modules/nerf/background/torchngp_background.py
nemo/collections/multimodal/modules/nerf/geometry/__init__.py
nemo/collections/multimodal/modules/nerf/geometry/dmtet.py
nemo/collections/multimodal/modules/nerf/geometry/layers.py
nemo/collections/multimodal/modules/nerf/geometry/nerf_base.py
nemo/collections/multimodal/modules/nerf/geometry/tcnn_nerf.py
nemo/collections/multimodal/modules/nerf/geometry/torchngp_nerf.py
nemo/collections/multimodal/modules/nerf/guidance/__init__.py
nemo/collections/multimodal/modules/nerf/guidance/stablediffusion_huggingface_pipeline.py
nemo/collections/multimodal/modules/nerf/guidance/stablediffusion_nemo_pipeline.py
nemo/collections/multimodal/modules/nerf/guidance/stablediffusion_trt_pipeline.py
nemo/collections/multimodal/modules/nerf/guidance/txt2img_guidance_base.py
nemo/collections/multimodal/modules/nerf/loss/__init__.py
nemo/collections/multimodal/modules/nerf/loss/laplacian_smooth_loss.py
nemo/collections/multimodal/modules/nerf/loss/normal_consistency_loss.py
nemo/collections/multimodal/modules/nerf/materials/__init__.py
nemo/collections/multimodal/modules/nerf/materials/basic_shading.py
nemo/collections/multimodal/modules/nerf/materials/materials_base.py
nemo/collections/multimodal/modules/nerf/renderers/__init__.py
nemo/collections/multimodal/modules/nerf/renderers/base_renderer.py
nemo/collections/multimodal/modules/nerf/renderers/base_sdf_renderer.py
nemo/collections/multimodal/modules/nerf/renderers/base_volume_renderer.py
nemo/collections/multimodal/modules/nerf/renderers/nerfacc_volume_renderer.py
nemo/collections/multimodal/modules/nerf/renderers/nvdiffrast_renderer.py
nemo/collections/multimodal/modules/nerf/renderers/torchngp_volume_renderer.py
nemo/collections/multimodal/modules/nerf/utils/__init__.py
nemo/collections/multimodal/modules/nerf/utils/activation.py
nemo/collections/multimodal/modules/nerf/utils/trt_engine.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/__init__.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/encoding.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/freqencoder.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/gridencoder.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/raymarching.py
nemo/collections/multimodal/modules/nerf/utils/torch_ngp/shencoder.py
nemo/collections/multimodal/modules/stable_diffusion/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/attention.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/denoiser.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/denoiser_scaling.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/denoiser_weighting.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/discretizer.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/guiders.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/loss.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/model.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/openaimodel.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/sampling.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/sampling_utils.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/sigma_sampling.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/util.py
nemo/collections/multimodal/modules/stable_diffusion/diffusionmodules/wrappers.py
nemo/collections/multimodal/modules/stable_diffusion/distributions/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/distributions/distributions.py
nemo/collections/multimodal/modules/stable_diffusion/encoders/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/encoders/modules.py
nemo/collections/multimodal/modules/stable_diffusion/encoders/x_transformer.py
nemo/collections/multimodal/modules/stable_diffusion/fast_geglu/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/fast_geglu/geglu.cpp
nemo/collections/multimodal/modules/stable_diffusion/quantization_utils/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/quantization_utils/calib_prompts.txt
nemo/collections/multimodal/modules/stable_diffusion/quantization_utils/plugin_calib.py
nemo/collections/multimodal/modules/stable_diffusion/quantization_utils/trt_engine.py
nemo/collections/multimodal/modules/stable_diffusion/quantization_utils/utils.py
nemo/collections/multimodal/modules/stable_diffusion/schedulers/__init__.py
nemo/collections/multimodal/modules/stable_diffusion/schedulers/ddim_scheduler.py
nemo/collections/multimodal/parts/__init__.py
nemo/collections/multimodal/parts/utils.py
nemo/collections/multimodal/parts/imagen/__init__.py
nemo/collections/multimodal/parts/imagen/utils.py
nemo/collections/multimodal/parts/stable_diffusion/__init__.py
nemo/collections/multimodal/parts/stable_diffusion/lr_scheduler.py
nemo/collections/multimodal/parts/stable_diffusion/pipeline.py
nemo/collections/multimodal/parts/stable_diffusion/sdxl_helpers.py
nemo/collections/multimodal/parts/stable_diffusion/sdxl_pipeline.py
nemo/collections/multimodal/parts/stable_diffusion/utils.py
nemo/collections/multimodal/speech_cv/__init__.py
nemo/collections/multimodal/speech_cv/data/__init__.py
nemo/collections/multimodal/speech_cv/data/video_to_text.py
nemo/collections/multimodal/speech_cv/data/video_to_text_dataset.py
nemo/collections/multimodal/speech_cv/models/__init__.py
nemo/collections/multimodal/speech_cv/models/visual_ctc_bpe_models.py
nemo/collections/multimodal/speech_cv/models/visual_ctc_models.py
nemo/collections/multimodal/speech_cv/models/visual_hybrid_rnnt_ctc_bpe_models.py
nemo/collections/multimodal/speech_cv/models/visual_hybrid_rnnt_ctc_models.py
nemo/collections/multimodal/speech_cv/models/visual_rnnt_bpe_models.py
nemo/collections/multimodal/speech_cv/models/visual_rnnt_models.py
nemo/collections/multimodal/speech_cv/modules/__init__.py
nemo/collections/multimodal/speech_cv/modules/linear_projection_video_front_end.py
nemo/collections/multimodal/speech_cv/modules/resnet_video_front_end.py
nemo/collections/multimodal/speech_cv/modules/video_augment.py
nemo/collections/multimodal/speech_cv/modules/video_preprocessing.py
nemo/collections/multimodal/speech_cv/parts/__init__.py
nemo/collections/multimodal/speech_cv/parts/preprocessing/__init__.py
nemo/collections/multimodal/speech_cv/parts/preprocessing/features.py
nemo/collections/multimodal/speech_cv/parts/submodules/__init__.py
nemo/collections/multimodal/speech_cv/parts/submodules/conv2d.py
nemo/collections/multimodal/speech_cv/parts/submodules/global_avg_pool2d.py
nemo/collections/multimodal/speech_cv/parts/submodules/permute.py
nemo/collections/multimodal/speech_cv/parts/submodules/resnet.py
nemo/collections/multimodal/speech_cv/parts/submodules/resnet_block.py
nemo/collections/multimodal/speech_cv/parts/submodules/resnet_bottleneck_block.py
nemo/collections/multimodal/speech_llm/__init__.py
nemo/collections/multimodal/speech_llm/data/__init__.py
nemo/collections/multimodal/speech_llm/data/audio_text_dataset.py
nemo/collections/multimodal/speech_llm/data/build_dataset.py
nemo/collections/multimodal/speech_llm/data/lhotse_dataset.py
nemo/collections/multimodal/speech_llm/models/__init__.py
nemo/collections/multimodal/speech_llm/models/modular_models.py
nemo/collections/multimodal/speech_llm/models/modular_t5_models.py
nemo/collections/multimodal/speech_llm/modules/__init__.py
nemo/collections/multimodal/speech_llm/modules/modality_adapters.py
nemo/collections/multimodal/speech_llm/modules/perception_modules.py
nemo/collections/multimodal/speech_llm/modules/common/__init__.py
nemo/collections/multimodal/speech_llm/modules/common/audio_text_generation_strategy.py
nemo/collections/multimodal/speech_llm/modules/common/audio_text_generation_utils.py
nemo/collections/multimodal/speech_llm/parts/__init__.py
nemo/collections/multimodal/speech_llm/parts/mixins/__init__.py
nemo/collections/multimodal/speech_llm/parts/mixins/adapter_mixin.py
nemo/collections/multimodal/speech_llm/parts/utils/__init__.py
nemo/collections/multimodal/speech_llm/parts/utils/data_utils.py
nemo/collections/multimodal_autoregressive/__init__.py
nemo/collections/multimodal_autoregressive/data/__init__.py
nemo/collections/multimodal_autoregressive/data/preprocess_coyo_emu3_tokenizer.py
nemo/collections/multimodal_autoregressive/data/preprocess_pokemon_blip_cosmos_tokenizer.py
nemo/collections/multimodal_autoregressive/tokenizer/__init__.py
nemo/collections/multimodal_autoregressive/tokenizer/cosmos_multimodal_tokenizer.py
nemo/collections/multimodal_autoregressive/tokenizer/cosmos_vision_tokens.txt
nemo/collections/nlp/__init__.py
nemo/collections/nlp/data/__init__.py
nemo/collections/nlp/data/common/__init__.py
nemo/collections/nlp/data/common/sequence_to_sequence_dataset.py
nemo/collections/nlp/data/data_utils/__init__.py
nemo/collections/nlp/data/data_utils/data_preprocessing.py
nemo/collections/nlp/data/dialogue/__init__.py
nemo/collections/nlp/data/dialogue/data_processor/__init__.py
nemo/collections/nlp/data/dialogue/data_processor/assistant_data_processor.py
nemo/collections/nlp/data/dialogue/data_processor/data_processor.py
nemo/collections/nlp/data/dialogue/data_processor/design_data_processor.py
nemo/collections/nlp/data/dialogue/data_processor/mellon_qa_data_processor.py
nemo/collections/nlp/data/dialogue/data_processor/ms_marco_data_processor.py
nemo/collections/nlp/data/dialogue/data_processor/sgd_data_processor.py
nemo/collections/nlp/data/dialogue/dataset/__init__.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_bert_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_gpt_classification_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_gpt_generation_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_nearest_neighbour_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_s2s_generation_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_sgd_bert_dataset.py
nemo/collections/nlp/data/dialogue/dataset/dialogue_zero_shot_intent_dataset.py
nemo/collections/nlp/data/dialogue/input_example/__init__.py
nemo/collections/nlp/data/dialogue/input_example/assistant_input_example.py
nemo/collections/nlp/data/dialogue/input_example/design_input_example.py
nemo/collections/nlp/data/dialogue/input_example/input_example.py
nemo/collections/nlp/data/dialogue/input_example/mellon_qa_input_example.py
nemo/collections/nlp/data/dialogue/input_example/ms_marco_input_example.py
nemo/collections/nlp/data/dialogue/input_example/sgd_input_example.py
nemo/collections/nlp/data/dialogue/sgd/__init__.py
nemo/collections/nlp/data/dialogue/sgd/evaluate.py
nemo/collections/nlp/data/dialogue/sgd/prediction_utils.py
nemo/collections/nlp/data/dialogue/sgd/schema.py
nemo/collections/nlp/data/entity_linking/__init__.py
nemo/collections/nlp/data/entity_linking/entity_linking_dataset.py
nemo/collections/nlp/data/glue_benchmark/__init__.py
nemo/collections/nlp/data/glue_benchmark/data_processors.py
nemo/collections/nlp/data/glue_benchmark/glue_benchmark_dataset.py
nemo/collections/nlp/data/information_retrieval/__init__.py
nemo/collections/nlp/data/information_retrieval/bert_embedding_dataset.py
nemo/collections/nlp/data/information_retrieval/gpt_embedding_dataset.py
nemo/collections/nlp/data/information_retrieval/information_retrieval_dataset.py
nemo/collections/nlp/data/intent_slot_classification/__init__.py
nemo/collections/nlp/data/intent_slot_classification/intent_slot_classification_dataset.py
nemo/collections/nlp/data/intent_slot_classification/intent_slot_classification_descriptor.py
nemo/collections/nlp/data/intent_slot_classification/multi_label_intent_slot_classification_dataset.py
nemo/collections/nlp/data/intent_slot_classification/multi_label_intent_slot_classification_descriptor.py
nemo/collections/nlp/data/language_modeling/__init__.py
nemo/collections/nlp/data/language_modeling/l2r_lm_dataset.py
nemo/collections/nlp/data/language_modeling/lm_bert_dataset.py
nemo/collections/nlp/data/language_modeling/sentence_dataset.py
nemo/collections/nlp/data/language_modeling/t0_dataset.py
nemo/collections/nlp/data/language_modeling/text_memmap_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/Makefile
nemo/collections/nlp/data/language_modeling/megatron/__init__.py
nemo/collections/nlp/data/language_modeling/megatron/bart_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/base_dataset_utils.py
nemo/collections/nlp/data/language_modeling/megatron/base_prompt_learning_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/bert_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/blendable_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/data_samplers.py
nemo/collections/nlp/data/language_modeling/megatron/dataset_utils.py
nemo/collections/nlp/data/language_modeling/megatron/gpt_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/gpt_fim_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/gpt_prompt_learning_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/gpt_sft_chat_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/gpt_sft_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/helpers.cpp
nemo/collections/nlp/data/language_modeling/megatron/indexed_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/indexed_retrieval_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/length_distribution_type.py
nemo/collections/nlp/data/language_modeling/megatron/lm_adapted_t5_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/megatron_batch_samplers.py
nemo/collections/nlp/data/language_modeling/megatron/request_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/retro_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/retro_dataset_legacy.py
nemo/collections/nlp/data/language_modeling/megatron/retro_fine_tune_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/t5_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/t5_prompt_learning_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/t5_sft_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/ul2_dataset.py
nemo/collections/nlp/data/language_modeling/megatron/xlm_dataset.py
nemo/collections/nlp/data/machine_translation/__init__.py
nemo/collections/nlp/data/machine_translation/machine_translation_dataset.py
nemo/collections/nlp/data/machine_translation/preproc_mt_data.py
nemo/collections/nlp/data/question_answering/__init__.py
nemo/collections/nlp/data/question_answering/data_processor/__init__.py
nemo/collections/nlp/data/question_answering/data_processor/qa_processing.py
nemo/collections/nlp/data/question_answering/dataset/__init__.py
nemo/collections/nlp/data/question_answering/dataset/qa_bert_dataset.py
nemo/collections/nlp/data/question_answering/dataset/qa_dataset.py
nemo/collections/nlp/data/question_answering/dataset/qa_gpt_dataset.py
nemo/collections/nlp/data/question_answering/dataset/qa_s2s_dataset.py
nemo/collections/nlp/data/question_answering/input_example/__init__.py
nemo/collections/nlp/data/question_answering/input_example/qa_bert_input_example.py
nemo/collections/nlp/data/question_answering/input_example/qa_gpt_input_example.py
nemo/collections/nlp/data/question_answering/input_example/qa_input_example.py
nemo/collections/nlp/data/question_answering/input_example/qa_s2s_input_example.py
nemo/collections/nlp/data/question_answering_squad/__init__.py
nemo/collections/nlp/data/question_answering_squad/qa_dataset.py
nemo/collections/nlp/data/question_answering_squad/qa_squad_processing.py
nemo/collections/nlp/data/spellchecking_asr_customization/__init__.py
nemo/collections/nlp/data/spellchecking_asr_customization/bert_example.py
nemo/collections/nlp/data/spellchecking_asr_customization/dataset.py
nemo/collections/nlp/data/spellchecking_asr_customization/utils.py
nemo/collections/nlp/data/text2sparql/__init__.py
nemo/collections/nlp/data/text2sparql/text2sparql_dataset.py
nemo/collections/nlp/data/text_classification/__init__.py
nemo/collections/nlp/data/text_classification/ptune_text_classification_dataset.py
nemo/collections/nlp/data/text_classification/text_classification_dataset.py
nemo/collections/nlp/data/text_normalization/__init__.py
nemo/collections/nlp/data/text_normalization/constants.py
nemo/collections/nlp/data/text_normalization/decoder_dataset.py
nemo/collections/nlp/data/text_normalization/tagger_dataset.py
nemo/collections/nlp/data/text_normalization/test_dataset.py
nemo/collections/nlp/data/text_normalization/utils.py
nemo/collections/nlp/data/text_normalization_as_tagging/__init__.py
nemo/collections/nlp/data/text_normalization_as_tagging/bert_example.py
nemo/collections/nlp/data/text_normalization_as_tagging/tagging.py
nemo/collections/nlp/data/text_normalization_as_tagging/thutmose_tagger_dataset.py
nemo/collections/nlp/data/text_normalization_as_tagging/utils.py
nemo/collections/nlp/data/token_classification/__init__.py
nemo/collections/nlp/data/token_classification/punctuation_capitalization_dataset.py
nemo/collections/nlp/data/token_classification/punctuation_capitalization_infer_dataset.py
nemo/collections/nlp/data/token_classification/punctuation_capitalization_tarred_dataset.py
nemo/collections/nlp/data/token_classification/token_classification_dataset.py
nemo/collections/nlp/data/token_classification/token_classification_utils.py
nemo/collections/nlp/data/zero_shot_intent_recognition/__init__.py
nemo/collections/nlp/data/zero_shot_intent_recognition/zero_shot_intent_dataset.py
nemo/collections/nlp/losses/__init__.py
nemo/collections/nlp/losses/sgd_loss.py
nemo/collections/nlp/metrics/__init__.py
nemo/collections/nlp/metrics/classification_report.py
nemo/collections/nlp/metrics/dialogue_metrics.py
nemo/collections/nlp/metrics/prompt_learning_metrics.py
nemo/collections/nlp/metrics/qa_metrics.py
nemo/collections/nlp/metrics/sequence_perplexity.py
nemo/collections/nlp/metrics/sgd_metrics.py
nemo/collections/nlp/models/__init__.py
nemo/collections/nlp/models/enc_dec_nlp_model.py
nemo/collections/nlp/models/nlp_model.py
nemo/collections/nlp/models/dialogue/__init__.py
nemo/collections/nlp/models/dialogue/dialogue_gpt_classification_model.py
nemo/collections/nlp/models/dialogue/dialogue_gpt_generation_model.py
nemo/collections/nlp/models/dialogue/dialogue_nearest_neighbour_model.py
nemo/collections/nlp/models/dialogue/dialogue_s2s_generation_model.py
nemo/collections/nlp/models/dialogue/dialogue_zero_shot_intent_model.py
nemo/collections/nlp/models/dialogue/intent_slot_classification_model.py
nemo/collections/nlp/models/dialogue/sgdqa_model.py
nemo/collections/nlp/models/duplex_text_normalization/__init__.py
nemo/collections/nlp/models/duplex_text_normalization/duplex_decoder.py
nemo/collections/nlp/models/duplex_text_normalization/duplex_tagger.py
nemo/collections/nlp/models/duplex_text_normalization/duplex_tn.py
nemo/collections/nlp/models/duplex_text_normalization/utils.py
nemo/collections/nlp/models/entity_linking/__init__.py
nemo/collections/nlp/models/entity_linking/entity_linking_model.py
nemo/collections/nlp/models/glue_benchmark/__init__.py
nemo/collections/nlp/models/glue_benchmark/glue_benchmark_model.py
nemo/collections/nlp/models/glue_benchmark/metrics_for_glue.py
nemo/collections/nlp/models/information_retrieval/__init__.py
nemo/collections/nlp/models/information_retrieval/base_ir_model.py
nemo/collections/nlp/models/information_retrieval/bert_dpr_model.py
nemo/collections/nlp/models/information_retrieval/bert_embedding_model.py
nemo/collections/nlp/models/information_retrieval/bert_joint_ir_model.py
nemo/collections/nlp/models/information_retrieval/megatron_bert_embedding_model.py
nemo/collections/nlp/models/information_retrieval/megatron_gpt_embedding_model.py
nemo/collections/nlp/models/information_retrieval/megatron_gpt_reranker_model.py
nemo/collections/nlp/models/intent_slot_classification/__init__.py
nemo/collections/nlp/models/intent_slot_classification/intent_slot_classification_model.py
nemo/collections/nlp/models/intent_slot_classification/multi_label_intent_slot_classification_model.py
nemo/collections/nlp/models/language_modeling/__init__.py
nemo/collections/nlp/models/language_modeling/bert_lm_model.py
nemo/collections/nlp/models/language_modeling/megatron_bart_model.py
nemo/collections/nlp/models/language_modeling/megatron_base_model.py
nemo/collections/nlp/models/language_modeling/megatron_base_prompt_learning_model.py
nemo/collections/nlp/models/language_modeling/megatron_bert_model.py
nemo/collections/nlp/models/language_modeling/megatron_glue_model.py
nemo/collections/nlp/models/language_modeling/megatron_gpt_adapter_model.py
nemo/collections/nlp/models/language_modeling/megatron_gpt_model.py
nemo/collections/nlp/models/language_modeling/megatron_gpt_prompt_learning_model.py
nemo/collections/nlp/models/language_modeling/megatron_gpt_sft_model.py
nemo/collections/nlp/models/language_modeling/megatron_griffin_model.py
nemo/collections/nlp/models/language_modeling/megatron_griffin_sft_model.py
nemo/collections/nlp/models/language_modeling/megatron_lm_encoder_decoder_model.py
nemo/collections/nlp/models/language_modeling/megatron_mamba_model.py
nemo/collections/nlp/models/language_modeling/megatron_mamba_sft_model.py
nemo/collections/nlp/models/language_modeling/megatron_retrieval_model.py
nemo/collections/nlp/models/language_modeling/megatron_retro_fine_tune_model.py
nemo/collections/nlp/models/language_modeling/megatron_retro_model.py
nemo/collections/nlp/models/language_modeling/megatron_t0_model.py
nemo/collections/nlp/models/language_modeling/megatron_t5_adapter_model.py
nemo/collections/nlp/models/language_modeling/megatron_t5_model.py
nemo/collections/nlp/models/language_modeling/megatron_t5_prompt_learning_model.py
nemo/collections/nlp/models/language_modeling/megatron_t5_sft_model.py
nemo/collections/nlp/models/language_modeling/transformer_lm_model.py
nemo/collections/nlp/models/language_modeling/megatron/__init__.py
nemo/collections/nlp/models/language_modeling/megatron/gpt_full_te_layer_autocast_spec.py
nemo/collections/nlp/models/language_modeling/megatron/gpt_layer_modelopt_spec.py
nemo/collections/nlp/models/language_modeling/megatron/gpt_model.py
nemo/collections/nlp/models/language_modeling/megatron/bert/__init__.py
nemo/collections/nlp/models/language_modeling/megatron/bert/bert_model.py
nemo/collections/nlp/models/language_modeling/megatron/bert/bert_spec.py
nemo/collections/nlp/models/language_modeling/megatron/falcon/__init__.py
nemo/collections/nlp/models/language_modeling/megatron/falcon/falcon_decoder_layer.py
nemo/collections/nlp/models/language_modeling/megatron/falcon/falcon_spec.py
nemo/collections/nlp/models/language_modeling/megatron/gemma2/__init__.py
nemo/collections/nlp/models/language_modeling/megatron/gemma2/gemma2_modules.py
nemo/collections/nlp/models/language_modeling/megatron/gemma2/gemma2_spec.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/__init__.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/griffin_block.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/griffin_layer_spec.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/griffin_model.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/recurrent_layer.py
nemo/collections/nlp/models/language_modeling/megatron/griffin/recurrent_module.py
nemo/collections/nlp/models/machine_translation/__init__.py
nemo/collections/nlp/models/machine_translation/megatron_nmt_model.py
nemo/collections/nlp/models/machine_translation/mt_enc_dec_bottleneck_model.py
nemo/collections/nlp/models/machine_translation/mt_enc_dec_config.py
nemo/collections/nlp/models/machine_translation/mt_enc_dec_model.py
nemo/collections/nlp/models/question_answering/__init__.py
nemo/collections/nlp/models/question_answering/qa_base_model.py
nemo/collections/nlp/models/question_answering/qa_bert_model.py
nemo/collections/nlp/models/question_answering/qa_gpt_model.py
nemo/collections/nlp/models/question_answering/qa_model.py
nemo/collections/nlp/models/question_answering/qa_s2s_model.py
nemo/collections/nlp/models/rag/__init__.py
nemo/collections/nlp/models/rag/custom_bert_embedder.py
nemo/collections/nlp/models/rag/custom_gpt_llm.py
nemo/collections/nlp/models/spellchecking_asr_customization/__init__.py
nemo/collections/nlp/models/spellchecking_asr_customization/spellchecking_model.py
nemo/collections/nlp/models/text2sparql/__init__.py
nemo/collections/nlp/models/text2sparql/text2sparql_model.py
nemo/collections/nlp/models/text_classification/__init__.py
nemo/collections/nlp/models/text_classification/text_classification_model.py
nemo/collections/nlp/models/text_normalization_as_tagging/__init__.py
nemo/collections/nlp/models/text_normalization_as_tagging/thutmose_tagger.py
nemo/collections/nlp/models/token_classification/__init__.py
nemo/collections/nlp/models/token_classification/punctuation_capitalization_config.py
nemo/collections/nlp/models/token_classification/punctuation_capitalization_lexical_audio_model.py
nemo/collections/nlp/models/token_classification/punctuation_capitalization_model.py
nemo/collections/nlp/models/token_classification/token_classification_model.py
nemo/collections/nlp/models/zero_shot_intent_recognition/__init__.py
nemo/collections/nlp/models/zero_shot_intent_recognition/zero_shot_intent_model.py
nemo/collections/nlp/modules/__init__.py
nemo/collections/nlp/modules/common/__init__.py
nemo/collections/nlp/modules/common/bert_module.py
nemo/collections/nlp/modules/common/chat_css.py
nemo/collections/nlp/modules/common/chatbot_component.py
nemo/collections/nlp/modules/common/classifier.py
nemo/collections/nlp/modules/common/decoder_module.py
nemo/collections/nlp/modules/common/encoder_module.py
nemo/collections/nlp/modules/common/gpt_module.py
nemo/collections/nlp/modules/common/lm_utils.py
nemo/collections/nlp/modules/common/megatron_web_server.py
nemo/collections/nlp/modules/common/prompt_encoder.py
nemo/collections/nlp/modules/common/prompt_table.py
nemo/collections/nlp/modules/common/retro_inference_strategies.py
nemo/collections/nlp/modules/common/sequence_classifier.py
nemo/collections/nlp/modules/common/sequence_regression.py
nemo/collections/nlp/modules/common/sequence_token_classifier.py
nemo/collections/nlp/modules/common/text_generation_server.py
nemo/collections/nlp/modules/common/text_generation_strategy.py
nemo/collections/nlp/modules/common/text_generation_utils.py
nemo/collections/nlp/modules/common/token_classifier.py
nemo/collections/nlp/modules/common/tokenizer_utils.py
nemo/collections/nlp/modules/common/huggingface/__init__.py
nemo/collections/nlp/modules/common/huggingface/albert.py
nemo/collections/nlp/modules/common/huggingface/bert.py
nemo/collections/nlp/modules/common/huggingface/camembert.py
nemo/collections/nlp/modules/common/huggingface/distilbert.py
nemo/collections/nlp/modules/common/huggingface/gpt2.py
nemo/collections/nlp/modules/common/huggingface/huggingface_decoder.py
nemo/collections/nlp/modules/common/huggingface/huggingface_encoder.py
nemo/collections/nlp/modules/common/huggingface/huggingface_utils.py
nemo/collections/nlp/modules/common/huggingface/roberta.py
nemo/collections/nlp/modules/common/hyena/__init__.py
nemo/collections/nlp/modules/common/hyena/fftconv_wrapper.py
nemo/collections/nlp/modules/common/hyena/hyena.py
nemo/collections/nlp/modules/common/hyena/hyena_filter.py
nemo/collections/nlp/modules/common/hyena/hyena_spec.py
nemo/collections/nlp/modules/common/megatron/__init__.py
nemo/collections/nlp/modules/common/megatron/attention.py
nemo/collections/nlp/modules/common/megatron/build_model.py
nemo/collections/nlp/modules/common/megatron/clip_grads.py
nemo/collections/nlp/modules/common/megatron/fused_bias_dropout_add.py
nemo/collections/nlp/modules/common/megatron/fused_bias_geglu.py
nemo/collections/nlp/modules/common/megatron/fused_bias_gelu.py
nemo/collections/nlp/modules/common/megatron/fused_layer_norm.py
nemo/collections/nlp/modules/common/megatron/fused_softmax.py
nemo/collections/nlp/modules/common/megatron/kerple_relative_position_embedding.py
nemo/collections/nlp/modules/common/megatron/language_model.py
nemo/collections/nlp/modules/common/megatron/layer_norm_1p.py
nemo/collections/nlp/modules/common/megatron/layer_type.py
nemo/collections/nlp/modules/common/megatron/megatron_decoder_module.py
nemo/collections/nlp/modules/common/megatron/megatron_decoders.py
nemo/collections/nlp/modules/common/megatron/megatron_encoder_decoder.py
nemo/collections/nlp/modules/common/megatron/megatron_encoder_module.py
nemo/collections/nlp/modules/common/megatron/megatron_encoders.py
nemo/collections/nlp/modules/common/megatron/megatron_export.py
nemo/collections/nlp/modules/common/megatron/megatron_init.py
nemo/collections/nlp/modules/common/megatron/megatron_perceiver_encoders.py
nemo/collections/nlp/modules/common/megatron/megatron_tokens_head_module.py
nemo/collections/nlp/modules/common/megatron/megatron_transformer_decoder.py
nemo/collections/nlp/modules/common/megatron/megatron_transformer_encoder.py
nemo/collections/nlp/modules/common/megatron/megatron_utils.py
nemo/collections/nlp/modules/common/megatron/mlp.py
nemo/collections/nlp/modules/common/megatron/module.py
nemo/collections/nlp/modules/common/megatron/retrieval_token_level_encoder_decoder.py
nemo/collections/nlp/modules/common/megatron/retrieval_transformer.py
nemo/collections/nlp/modules/common/megatron/token_level_encoder_decoder.py
nemo/collections/nlp/modules/common/megatron/transformer.py
nemo/collections/nlp/modules/common/megatron/utils.py
nemo/collections/nlp/modules/common/megatron/vocab_parallel_cross_entropy.py
nemo/collections/nlp/modules/common/megatron/adapters/__init__.py
nemo/collections/nlp/modules/common/megatron/adapters/mcore_mixins.py
nemo/collections/nlp/modules/common/megatron/adapters/parallel_adapters.py
nemo/collections/nlp/modules/common/megatron/adapters/qlora.py
nemo/collections/nlp/modules/common/megatron/hiddens/__init__.py
nemo/collections/nlp/modules/common/megatron/hiddens/megatron_hidden_loss.py
nemo/collections/nlp/modules/common/megatron/hiddens/megatron_hidden_transform.py
nemo/collections/nlp/modules/common/megatron/hiddens/megatron_hiddens.py
nemo/collections/nlp/modules/common/megatron/mup/__init__.py
nemo/collections/nlp/modules/common/megatron/mup/infshape.py
nemo/collections/nlp/modules/common/megatron/mup/init.py
nemo/collections/nlp/modules/common/megatron/mup/layer.py
nemo/collections/nlp/modules/common/megatron/mup/optim.py
nemo/collections/nlp/modules/common/megatron/mup/shape.py
nemo/collections/nlp/modules/common/megatron/position_embedding/__init__.py
nemo/collections/nlp/modules/common/megatron/position_embedding/alibi_relative_position_embedding.py
nemo/collections/nlp/modules/common/megatron/position_embedding/kerple_relative_position_embedding.py
nemo/collections/nlp/modules/common/megatron/position_embedding/rotary_position_embedding.py
nemo/collections/nlp/modules/common/megatron/position_embedding/sandwich_relative_position_embedding.py
nemo/collections/nlp/modules/common/megatron/position_embedding/t5_relative_position_embedding.py
nemo/collections/nlp/modules/common/megatron/position_embedding/xpos_position_embedding.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/__init__.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/bert_service.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/combo_retrieval_server.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/dynamic_retrieval_server.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/retrieval_service.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/static_retrieval_server.py
nemo/collections/nlp/modules/common/megatron/retrieval_services/util.py
nemo/collections/nlp/modules/common/transformer/__init__.py
nemo/collections/nlp/modules/common/transformer/bridge_encoders.py
nemo/collections/nlp/modules/common/transformer/perceiver_encoders.py
nemo/collections/nlp/modules/common/transformer/reduction_encoders.py
nemo/collections/nlp/modules/common/transformer/text_generation.py
nemo/collections/nlp/modules/common/transformer/transformer.py
nemo/collections/nlp/modules/common/transformer/transformer_bottleneck.py
nemo/collections/nlp/modules/common/transformer/transformer_decoders.py
nemo/collections/nlp/modules/common/transformer/transformer_encoders.py
nemo/collections/nlp/modules/common/transformer/transformer_generators.py
nemo/collections/nlp/modules/common/transformer/transformer_modules.py
nemo/collections/nlp/modules/common/transformer/transformer_utils.py
nemo/collections/nlp/modules/dialogue_state_tracking/__init__.py
nemo/collections/nlp/modules/dialogue_state_tracking/sgd_decoder.py
nemo/collections/nlp/modules/dialogue_state_tracking/sgd_encoder.py
nemo/collections/nlp/parts/__init__.py
nemo/collections/nlp/parts/megatron_lr_schedulers.py
nemo/collections/nlp/parts/megatron_trainer_builder.py
nemo/collections/nlp/parts/nlp_overrides.py
nemo/collections/nlp/parts/peft_config.py
nemo/collections/nlp/parts/utils_funcs.py
nemo/collections/nlp/parts/mixins/__init__.py
nemo/collections/nlp/parts/mixins/multimodal_adapter_mixins.py
nemo/collections/nlp/parts/mixins/nlp_adapter_mixins.py
nemo/collections/speechlm/__init__.py
nemo/collections/speechlm/api.py
nemo/collections/speechlm/data/__init__.py
nemo/collections/speechlm/data/audio_to_text_module.py
nemo/collections/speechlm/data/data_sampler.py
nemo/collections/speechlm/data/text_processing.py
nemo/collections/speechlm/data/dataset/__init__.py
nemo/collections/speechlm/data/dataset/audio_text_dataset.py
nemo/collections/speechlm/data/dataset/audio_text_lhotse_dataset.py
nemo/collections/speechlm/data/dataset/data_utils.py
nemo/collections/speechlm/models/__init__.py
nemo/collections/speechlm/models/base.py
nemo/collections/speechlm/models/hf_auto_model_for_speech_seq2seq.py
nemo/collections/speechlm/models/speech_to_text_llm_model.py
nemo/collections/speechlm/modules/__init__.py
nemo/collections/speechlm/modules/asr_module.py
nemo/collections/speechlm/modules/modality_adapter.py
nemo/collections/speechlm/recipes/__init__.py
nemo/collections/speechlm/recipes/pipeline.py
nemo/collections/speechlm/recipes/optim/__init__.py
nemo/collections/speechlm/recipes/optim/adam.py
nemo/collections/speechlm/strategies/__init__.py
nemo/collections/speechlm/strategies/megatron_strategy.py
nemo/collections/speechlm/utils/__init__.py
nemo/collections/speechlm/utils/hydra_utils.py
nemo/collections/speechlm/utils/io.py
nemo/collections/speechlm/utils/model_transform.py
nemo/collections/speechlm/utils/resume.py
nemo/collections/speechlm/utils/text_generation/__init__.py
nemo/collections/speechlm/utils/text_generation/audio_text_generation_strategy.py
nemo/collections/speechlm/utils/text_generation/audio_text_generation_utils.py
nemo/collections/speechlm2/__init__.py
nemo/collections/speechlm2/data/__init__.py
nemo/collections/speechlm2/data/datamodule.py
nemo/collections/speechlm2/data/s2s_dataset.py
nemo/collections/speechlm2/data/salm_dataset.py
nemo/collections/speechlm2/data/utils.py
nemo/collections/speechlm2/models/__init__.py
nemo/collections/speechlm2/models/duplex_s2s_model.py
nemo/collections/speechlm2/models/duplex_s2s_speech_decoder_model.py
nemo/collections/speechlm2/models/salm.py
nemo/collections/speechlm2/modules/__init__.py
nemo/collections/speechlm2/modules/perception.py
nemo/collections/speechlm2/modules/speech_generation.py
nemo/collections/speechlm2/parts/__init__.py
nemo/collections/speechlm2/parts/hf_hub.py
nemo/collections/speechlm2/parts/lora.py
nemo/collections/speechlm2/parts/nsight.py
nemo/collections/speechlm2/parts/optim_setup.py
nemo/collections/speechlm2/parts/precision.py
nemo/collections/speechlm2/parts/pretrained.py
nemo/collections/speechlm2/parts/metrics/__init__.py
nemo/collections/speechlm2/parts/metrics/asr_bleu.py
nemo/collections/speechlm2/parts/metrics/bleu.py
nemo/collections/tts/__init__.py
nemo/collections/tts/data/__init__.py
nemo/collections/tts/data/dataset.py
nemo/collections/tts/data/text_to_speech_dataset.py
nemo/collections/tts/data/vocoder_dataset.py
nemo/collections/tts/data/speechllm/__init__.py
nemo/collections/tts/data/speechllm/t5_speechllm_dataset.py
nemo/collections/tts/data/speechllm/t5_speechllm_tarred_dataset.py
nemo/collections/tts/g2p/__init__.py
nemo/collections/tts/g2p/modules.py
nemo/collections/tts/g2p/utils.py
nemo/collections/tts/g2p/data/__init__.py
nemo/collections/tts/g2p/data/ctc.py
nemo/collections/tts/g2p/data/heteronym_classification.py
nemo/collections/tts/g2p/data/t5.py
nemo/collections/tts/g2p/models/__init__.py
nemo/collections/tts/g2p/models/base.py
nemo/collections/tts/g2p/models/ctc.py
nemo/collections/tts/g2p/models/en_us_arpabet.py
nemo/collections/tts/g2p/models/heteronym_classification.py
nemo/collections/tts/g2p/models/i18n_ipa.py
nemo/collections/tts/g2p/models/ja_jp_ipa.py
nemo/collections/tts/g2p/models/t5.py
nemo/collections/tts/g2p/models/zh_cn_pinyin.py
nemo/collections/tts/losses/__init__.py
nemo/collections/tts/losses/aligner_loss.py
nemo/collections/tts/losses/audio_codec_loss.py
nemo/collections/tts/losses/fastpitchloss.py
nemo/collections/tts/losses/hifigan_losses.py
nemo/collections/tts/losses/radttsloss.py
nemo/collections/tts/losses/spectrogram_enhancer_losses.py
nemo/collections/tts/losses/stftlosses.py
nemo/collections/tts/losses/tacotron2loss.py
nemo/collections/tts/losses/vits_losses.py
nemo/collections/tts/losses/waveglowloss.py
nemo/collections/tts/models/__init__.py
nemo/collections/tts/models/aligner.py
nemo/collections/tts/models/audio_codec.py
nemo/collections/tts/models/base.py
nemo/collections/tts/models/fastpitch.py
nemo/collections/tts/models/fastpitch_ssl.py
nemo/collections/tts/models/hifigan.py
nemo/collections/tts/models/magpietts.py
nemo/collections/tts/models/mixer_tts.py
nemo/collections/tts/models/radtts.py
nemo/collections/tts/models/spectrogram_enhancer.py
nemo/collections/tts/models/ssl_tts.py
nemo/collections/tts/models/tacotron2.py
nemo/collections/tts/models/two_stages.py
nemo/collections/tts/models/univnet.py
nemo/collections/tts/models/vits.py
nemo/collections/tts/models/waveglow.py
nemo/collections/tts/models/speechllm/__init__.py
nemo/collections/tts/models/speechllm/megatron_base_speechllm_prompt_model.py
nemo/collections/tts/models/speechllm/megatron_t5_speechllm_model.py
nemo/collections/tts/modules/__init__.py
nemo/collections/tts/modules/adapters.py
nemo/collections/tts/modules/aligner.py
nemo/collections/tts/modules/attribute_prediction_model.py
nemo/collections/tts/modules/audio_codec_modules.py
nemo/collections/tts/modules/common.py
nemo/collections/tts/modules/encodec_modules.py
nemo/collections/tts/modules/fastpitch.py
nemo/collections/tts/modules/hifigan_modules.py
nemo/collections/tts/modules/mixer_tts.py
nemo/collections/tts/modules/radtts.py
nemo/collections/tts/modules/spectrogram_enhancer.py
nemo/collections/tts/modules/ssl_tts.py
nemo/collections/tts/modules/submodules.py
nemo/collections/tts/modules/tacotron2.py
nemo/collections/tts/modules/transformer.py
nemo/collections/tts/modules/transformer_2501.py
nemo/collections/tts/modules/univnet_modules.py
nemo/collections/tts/modules/vits_modules.py
nemo/collections/tts/modules/waveglow.py
nemo/collections/tts/modules/monotonic_align/__init__.py
nemo/collections/tts/modules/monotonic_align/numba_core.py
nemo/collections/tts/parts/__init__.py
nemo/collections/tts/parts/mixins/__init__.py
nemo/collections/tts/parts/mixins/fastpitch_adapter_mixins.py
nemo/collections/tts/parts/preprocessing/__init__.py
nemo/collections/tts/parts/preprocessing/audio_trimming.py
nemo/collections/tts/parts/preprocessing/feature_processors.py
nemo/collections/tts/parts/preprocessing/features.py
nemo/collections/tts/parts/utils/__init__.py
nemo/collections/tts/parts/utils/callbacks.py
nemo/collections/tts/parts/utils/distributed.py
nemo/collections/tts/parts/utils/helpers.py
nemo/collections/tts/parts/utils/splines.py
nemo/collections/tts/parts/utils/tts_dataset_utils.py
nemo/collections/tts/torch/__init__.py
nemo/collections/tts/torch/g2ps.py
nemo/collections/tts/torch/tts_data_types.py
nemo/collections/tts/torch/tts_tokenizers.py
nemo/collections/vision/__init__.py
nemo/collections/vision/data/__init__.py
nemo/collections/vision/data/imagenet_classnames.py
nemo/collections/vision/data/megatron/__init__.py
nemo/collections/vision/data/megatron/autoaugment.py
nemo/collections/vision/data/megatron/data_samplers.py
nemo/collections/vision/data/megatron/image_folder.py
nemo/collections/vision/data/megatron/vit_dataset.py
nemo/collections/vision/losses/__init__.py
nemo/collections/vision/metrics/__init__.py
nemo/collections/vision/models/__init__.py
nemo/collections/vision/models/megatron_vit_classification_models.py
nemo/collections/vision/modules/__init__.py
nemo/collections/vision/modules/common/__init__.py
nemo/collections/vision/modules/common/megatron/__init__.py
nemo/collections/vision/modules/common/megatron/vision_transformer.py
nemo/collections/vision/modules/vit/__init__.py
nemo/collections/vision/modules/vit/vit_backbone.py
nemo/collections/vision/parts/__init__.py
nemo/collections/vlm/__init__.py
nemo/collections/vlm/layer_specs.py
nemo/collections/vlm/clip/__init__.py
nemo/collections/vlm/clip/data/__init__.py
nemo/collections/vlm/clip/data/clip_data_module.py
nemo/collections/vlm/clip/data/mock.py
nemo/collections/vlm/clip/loss/__init__.py
nemo/collections/vlm/clip/loss/clip_loss.py
nemo/collections/vlm/clip/model/__init__.py
nemo/collections/vlm/clip/model/base.py
nemo/collections/vlm/clip/model/clip.py
nemo/collections/vlm/data/__init__.py
nemo/collections/vlm/data/data_module.py
nemo/collections/vlm/data/task_encoder.py
nemo/collections/vlm/data/utils.py
nemo/collections/vlm/gemma3vl/__init__.py
nemo/collections/vlm/gemma3vl/data/__init__.py
nemo/collections/vlm/gemma3vl/data/mock.py
nemo/collections/vlm/gemma3vl/data/task_encoder.py
nemo/collections/vlm/gemma3vl/model/__init__.py
nemo/collections/vlm/gemma3vl/model/base.py
nemo/collections/vlm/gemma3vl/model/gemma3vl.py
nemo/collections/vlm/gemma3vl/model/vision.py
nemo/collections/vlm/hf/__init__.py
nemo/collections/vlm/hf/data/__init__.py
nemo/collections/vlm/hf/data/automodel_datasets.py
nemo/collections/vlm/hf/data/hf_dataset.py
nemo/collections/vlm/hf/model/__init__.py
nemo/collections/vlm/hf/model/hf_auto_model_for_image_text_to_text.py
nemo/collections/vlm/inference/__init__.py
nemo/collections/vlm/inference/base.py
nemo/collections/vlm/inference/llava_inference_wrapper.py
nemo/collections/vlm/inference/mllama_inference_wrapper.py
nemo/collections/vlm/inference/vlm_engine.py
nemo/collections/vlm/inference/vlm_inference_controller.py
nemo/collections/vlm/llama4/__init__.py
nemo/collections/vlm/llama4/data/__init__.py
nemo/collections/vlm/llama4/data/mock.py
nemo/collections/vlm/llama4/data/task_encoder.py
nemo/collections/vlm/llama4/model/__init__.py
nemo/collections/vlm/llama4/model/base.py
nemo/collections/vlm/llama4/model/llama4_omni.py
nemo/collections/vlm/llama4/model/vision.py
nemo/collections/vlm/llava_next/__init__.py
nemo/collections/vlm/llava_next/data/__init__.py
nemo/collections/vlm/llava_next/data/interleaved_sample_encoder.py
nemo/collections/vlm/llava_next/data/mock.py
nemo/collections/vlm/llava_next/data/sample.py
nemo/collections/vlm/llava_next/data/task_encoder.py
nemo/collections/vlm/llava_next/data/utils.py
nemo/collections/vlm/llava_next/data/vqa_sample_encoder.py
nemo/collections/vlm/llava_next/model/__init__.py
nemo/collections/vlm/llava_next/model/base.py
nemo/collections/vlm/llava_next/model/llava_next.py
nemo/collections/vlm/llava_next/model/utils.py
nemo/collections/vlm/mllama/__init__.py
nemo/collections/vlm/mllama/data/__init__.py
nemo/collections/vlm/mllama/data/mock.py
nemo/collections/vlm/mllama/data/preloaded.py
nemo/collections/vlm/mllama/data/sample_encoder.py
nemo/collections/vlm/mllama/data/task_encoder.py
nemo/collections/vlm/mllama/model/__init__.py
nemo/collections/vlm/mllama/model/base.py
nemo/collections/vlm/mllama/model/language.py
nemo/collections/vlm/mllama/model/mllama.py
nemo/collections/vlm/mllama/model/utils.py
nemo/collections/vlm/mllama/model/vision.py
nemo/collections/vlm/neva/__init__.py
nemo/collections/vlm/neva/data/__init__.py
nemo/collections/vlm/neva/data/config.py
nemo/collections/vlm/neva/data/conversation.py
nemo/collections/vlm/neva/data/mock.py
nemo/collections/vlm/neva/data/multimodal_tokens.py
nemo/collections/vlm/neva/data/preloaded.py
nemo/collections/vlm/neva/data/sequence_packing.py
nemo/collections/vlm/neva/model/__init__.py
nemo/collections/vlm/neva/model/base.py
nemo/collections/vlm/neva/model/llava.py
nemo/collections/vlm/peft/__init__.py
nemo/collections/vlm/peft/lora.py
nemo/collections/vlm/qwen2vl/__init__.py
nemo/collections/vlm/qwen2vl/data/__init__.py
nemo/collections/vlm/qwen2vl/data/config.py
nemo/collections/vlm/qwen2vl/data/conversation.py
nemo/collections/vlm/qwen2vl/data/mock.py
nemo/collections/vlm/qwen2vl/data/multimodal_tokens.py
nemo/collections/vlm/qwen2vl/data/preloaded.py
nemo/collections/vlm/qwen2vl/data/task_encoder.py
nemo/collections/vlm/qwen2vl/model/__init__.py
nemo/collections/vlm/qwen2vl/model/api.py
nemo/collections/vlm/qwen2vl/model/base.py
nemo/collections/vlm/qwen2vl/model/qwen2vl.py
nemo/collections/vlm/qwen2vl/model/vision.py
nemo/collections/vlm/recipes/__init__.py
nemo/collections/vlm/recipes/clip_b32.py
nemo/collections/vlm/recipes/gemma3vl_12b.py
nemo/collections/vlm/recipes/gemma3vl_27b.py
nemo/collections/vlm/recipes/gemma3vl_4b.py
nemo/collections/vlm/recipes/llama4_omni_e128.py
nemo/collections/vlm/recipes/llama4_omni_e16.py
nemo/collections/vlm/recipes/llava15_13b.py
nemo/collections/vlm/recipes/llava15_7b.py
nemo/collections/vlm/recipes/llava_next_7b.py
nemo/collections/vlm/recipes/mllama_11b.py
nemo/collections/vlm/recipes/mllama_90b.py
nemo/collections/vlm/recipes/neva_llama3_8b.py
nemo/collections/vlm/recipes/qwen2vl_2b.py
nemo/collections/vlm/vision/__init__.py
nemo/collections/vlm/vision/base.py
nemo/collections/vlm/vision/clip_vit.py
nemo/collections/vlm/vision/intern_vit.py
nemo/collections/vlm/vision/siglip_vit.py
nemo/core/__init__.py
nemo/core/classes/__init__.py
nemo/core/classes/common.py
nemo/core/classes/dataset.py
nemo/core/classes/exportable.py
nemo/core/classes/loss.py
nemo/core/classes/modelPT.py
nemo/core/classes/module.py
nemo/core/classes/mixins/__init__.py
nemo/core/classes/mixins/access_mixins.py
nemo/core/classes/mixins/adapter_mixin_strategies.py
nemo/core/classes/mixins/adapter_mixins.py
nemo/core/classes/mixins/hf_io_mixin.py
nemo/core/config/__init__.py
nemo/core/config/base_config.py
nemo/core/config/hydra_runner.py
nemo/core/config/modelPT.py
nemo/core/config/optimizers.py
nemo/core/config/pytorch.py
nemo/core/config/pytorch_lightning.py
nemo/core/config/schedulers.py
nemo/core/config/templates/__init__.py
nemo/core/config/templates/model_card.py
nemo/core/connectors/__init__.py
nemo/core/connectors/save_restore_connector.py
nemo/core/neural_types/__init__.py
nemo/core/neural_types/axes.py
nemo/core/neural_types/comparison.py
nemo/core/neural_types/elements.py
nemo/core/neural_types/neural_type.py
nemo/core/optim/__init__.py
nemo/core/optim/adafactor.py
nemo/core/optim/adan.py
nemo/core/optim/distributed_adam.py
nemo/core/optim/lr_scheduler.py
nemo/core/optim/mcore_optim.py
nemo/core/optim/megatron_fused_adam.py
nemo/core/optim/novograd.py
nemo/core/optim/optimizer_with_main_params.py
nemo/core/optim/optimizers.py
nemo/core/optim/radam.py
nemo/core/utils/__init__.py
nemo/core/utils/cuda_python_utils.py
nemo/core/utils/k2_guard.py
nemo/core/utils/k2_utils.py
nemo/core/utils/neural_type_utils.py
nemo/core/utils/numba_utils.py
nemo/core/utils/optional_libs.py
nemo/core/utils/process_launcher/__init__.py
nemo/core/utils/process_launcher/launcher.py
nemo/deploy/__init__.py
nemo/deploy/deploy_base.py
nemo/deploy/deploy_pytriton.py
nemo/deploy/triton_deployable.py
nemo/deploy/utils.py
nemo/deploy/multimodal/__init__.py
nemo/deploy/multimodal/query_multimodal.py
nemo/deploy/nlp/__init__.py
nemo/deploy/nlp/hf_deployable.py
nemo/deploy/nlp/megatronllm_deployable.py
nemo/deploy/nlp/query_llm.py
nemo/deploy/service/__init__.py
nemo/deploy/service/fastapi_interface_to_pytriton.py
nemo/deploy/service/rest_model_api.py
nemo/export/__init__.py
nemo/export/onnx_llm_exporter.py
nemo/export/sentencepiece_tokenizer.py
nemo/export/tarutils.py
nemo/export/tensorrt_lazy_compiler.py
nemo/export/tensorrt_llm.py
nemo/export/tensorrt_mm_exporter.py
nemo/export/tiktoken_tokenizer.py
nemo/export/vllm_exporter.py
nemo/export/vllm_hf_exporter.py
nemo/export/multimodal/__init__.py
nemo/export/multimodal/build.py
nemo/export/multimodal/converter.py
nemo/export/multimodal/run.py
nemo/export/quantize/__init__.py
nemo/export/quantize/quantizer.py
nemo/export/trt_llm/__init__.py
nemo/export/trt_llm/tensorrt_llm_build.py
nemo/export/trt_llm/tensorrt_llm_run.py
nemo/export/trt_llm/utils.py
nemo/export/trt_llm/converter/__init__.py
nemo/export/trt_llm/converter/model_converter.py
nemo/export/trt_llm/converter/model_to_trt_llm_ckpt.py
nemo/export/trt_llm/converter/utils.py
nemo/export/trt_llm/nemo_ckpt_loader/__init__.py
nemo/export/trt_llm/nemo_ckpt_loader/nemo_file.py
nemo/export/trt_llm/qnemo/__init__.py
nemo/export/trt_llm/qnemo/qnemo_to_tensorrt_llm.py
nemo/export/trt_llm/qnemo/tokenizer_utils.py
nemo/export/trt_llm/qnemo/utils.py
nemo/export/utils/__init__.py
nemo/export/utils/_mock_import.py
nemo/export/utils/constants.py
nemo/export/utils/lora_converter.py
nemo/export/utils/model_loader.py
nemo/export/utils/utils.py
nemo/export/vllm/__init__.py
nemo/export/vllm/model_config.py
nemo/export/vllm/model_converters.py
nemo/export/vllm/model_loader.py
nemo/lightning/__init__.py
nemo/lightning/_strategy_lib.py
nemo/lightning/base.py
nemo/lightning/ckpt_utils.py
nemo/lightning/data.py
nemo/lightning/megatron_init.py
nemo/lightning/megatron_parallel.py
nemo/lightning/nemo_logger.py
nemo/lightning/resume.py
nemo/lightning/fabric/__init__.py
nemo/lightning/fabric/conversion.py
nemo/lightning/fabric/fabric.py
nemo/lightning/fabric/plugins.py
nemo/lightning/fabric/strategies.py
nemo/lightning/io/__init__.py
nemo/lightning/io/api.py
nemo/lightning/io/capture.py
nemo/lightning/io/connector.py
nemo/lightning/io/fdl_torch.py
nemo/lightning/io/hf.py
nemo/lightning/io/mixin.py
nemo/lightning/io/pl.py
nemo/lightning/io/registry.py
nemo/lightning/io/state.py
nemo/lightning/io/to_config.py
nemo/lightning/io/artifact/__init__.py
nemo/lightning/io/artifact/base.py
nemo/lightning/io/artifact/file.py
nemo/lightning/io/artifact/hf_auto.py
nemo/lightning/io/artifact/pickle.py
nemo/lightning/pytorch/__init__.py
nemo/lightning/pytorch/local_ckpt.py
nemo/lightning/pytorch/trainer.py
nemo/lightning/pytorch/utils.py
nemo/lightning/pytorch/accelerate/__init__.py
nemo/lightning/pytorch/accelerate/transformer_engine.py
nemo/lightning/pytorch/callbacks/__init__.py
nemo/lightning/pytorch/callbacks/ddp_parity_checker.py
nemo/lightning/pytorch/callbacks/debugging.py
nemo/lightning/pytorch/callbacks/flops_callback.py
nemo/lightning/pytorch/callbacks/garbage_collection.py
nemo/lightning/pytorch/callbacks/jit_transform.py
nemo/lightning/pytorch/callbacks/megatron_comm_overlap.py
nemo/lightning/pytorch/callbacks/memory_callback.py
nemo/lightning/pytorch/callbacks/memory_profiler.py
nemo/lightning/pytorch/callbacks/model_callback.py
nemo/lightning/pytorch/callbacks/model_checkpoint.py
nemo/lightning/pytorch/callbacks/model_transform.py
nemo/lightning/pytorch/callbacks/moe_token_drop.py
nemo/lightning/pytorch/callbacks/nsys.py
nemo/lightning/pytorch/callbacks/optimizer_monitor.py
nemo/lightning/pytorch/callbacks/peft.py
nemo/lightning/pytorch/callbacks/preemption.py
nemo/lightning/pytorch/callbacks/progress_bar.py
nemo/lightning/pytorch/callbacks/progress_printer.py
nemo/lightning/pytorch/callbacks/pytorch_profiler.py
nemo/lightning/pytorch/callbacks/runtime_estimator.py
nemo/lightning/pytorch/callbacks/speed_monitor.py
nemo/lightning/pytorch/optim/__init__.py
nemo/lightning/pytorch/optim/base.py
nemo/lightning/pytorch/optim/lr_scheduler.py
nemo/lightning/pytorch/optim/megatron.py
nemo/lightning/pytorch/optim/pytorch.py
nemo/lightning/pytorch/plugins/__init__.py
nemo/lightning/pytorch/plugins/data_sampler.py
nemo/lightning/pytorch/plugins/mixed_precision.py
nemo/lightning/pytorch/strategies/__init__.py
nemo/lightning/pytorch/strategies/fsdp2_strategy.py
nemo/lightning/pytorch/strategies/fsdp_strategy.py
nemo/lightning/pytorch/strategies/megatron_strategy.py
nemo/lightning/pytorch/strategies/utils.py
nemo/lightning/run/__init__.py
nemo/lightning/run/plugins.py
nemo/utils/__init__.py
nemo/utils/app_state.py
nemo/utils/arguments.py
nemo/utils/cast_utils.py
nemo/utils/cloud.py
nemo/utils/config_utils.py
nemo/utils/data_utils.py
nemo/utils/debug_hook.py
nemo/utils/distributed.py
nemo/utils/dtype.py
nemo/utils/enum.py
nemo/utils/env_var_parsing.py
nemo/utils/exceptions.py
nemo/utils/exp_manager.py
nemo/utils/export_utils.py
nemo/utils/flops_formulas.py
nemo/utils/get_rank.py
nemo/utils/hyena_flops_formulas.py
nemo/utils/import_utils.py
nemo/utils/lightning_logger_patch.py
nemo/utils/mcore_logger.py
nemo/utils/metaclasses.py
nemo/utils/model_utils.py
nemo/utils/msc_utils.py
nemo/utils/nemo_logging.py
nemo/utils/notebook_utils.py
nemo/utils/nvtx.py
nemo/utils/s3_dirpath_utils.py
nemo/utils/s3_utils.py
nemo/utils/sequence_packing_utils.py
nemo/utils/te_utils.py
nemo/utils/timers.py
nemo/utils/trainer_utils.py
nemo/utils/trt_utils.py
nemo/utils/callbacks/__init__.py
nemo/utils/callbacks/checkpointing_context.py
nemo/utils/callbacks/cuda_graph.py
nemo/utils/callbacks/dist_ckpt_io.py
nemo/utils/callbacks/nemo_model_checkpoint.py
nemo/utils/callbacks/preemption.py
nemo/utils/callbacks/s3_checkpoint_io.py
nemo/utils/decorators/__init__.py
nemo/utils/decorators/deprecated.py
nemo/utils/decorators/experimental.py
nemo/utils/decorators/port_docs.py
nemo/utils/formatters/__init__.py
nemo/utils/formatters/base.py
nemo/utils/formatters/colors.py
nemo/utils/formatters/utils.py
nemo/utils/loggers/__init__.py
nemo/utils/loggers/clearml_logger.py
nemo/utils/loggers/dllogger.py
nemo/utils/loggers/mlflow_logger.py
nemo_toolkit.egg-info/PKG-INFO
nemo_toolkit.egg-info/SOURCES.txt
nemo_toolkit.egg-info/dependency_links.txt
nemo_toolkit.egg-info/entry_points.txt
nemo_toolkit.egg-info/not-zip-safe
nemo_toolkit.egg-info/requires.txt
nemo_toolkit.egg-info/top_level.txt
requirements/manifest.json
requirements/requirements.txt
requirements/requirements_asr.txt
requirements/requirements_audio.txt
requirements/requirements_automodel.txt
requirements/requirements_common.txt
requirements/requirements_deploy.txt
requirements/requirements_docs.txt
requirements/requirements_eval.txt
requirements/requirements_lightning.txt
requirements/requirements_multimodal.txt
requirements/requirements_nlp.txt
requirements/requirements_run.txt
requirements/requirements_slu.txt
requirements/requirements_test.txt
requirements/requirements_tts.txt
requirements/requirements_vllm.txt
scripts/__init__.py
scripts/construct_random_negatives.py
scripts/export.py
scripts/performance/__init__.py
scripts/performance/argument_parser.py
scripts/performance/executors.py
scripts/performance/helpers.py
scripts/performance/utils.py
scripts/performance/llm/__init__.py
scripts/performance/llm/finetune_deepseek_v3.py
scripts/performance/llm/finetune_llama31_405b.py
scripts/performance/llm/finetune_llama3_70b.py
scripts/performance/llm/finetune_llama3_8b.py
scripts/performance/llm/mlperf_lora_llama2_70b.py
scripts/performance/llm/pretrain_automodel_llama3_8b.py
scripts/performance/llm/pretrain_deepseek_v3.py
scripts/performance/llm/pretrain_gpt3_175b.py
scripts/performance/llm/pretrain_llama31_405b.py
scripts/performance/llm/pretrain_llama3_70b.py
scripts/performance/llm/pretrain_llama3_8b.py
scripts/performance/llm/pretrain_llama4_e128.py
scripts/performance/llm/pretrain_llama4_e16.py
scripts/performance/llm/pretrain_mixtral_8x22b.py
scripts/performance/llm/pretrain_mixtral_8x7b.py
scripts/performance/llm/pretrain_nemotron3_22b.py
scripts/performance/llm/pretrain_nemotron3_8b.py
scripts/performance/llm/pretrain_nemotron4_15b.py
scripts/performance/llm/pretrain_nemotron4_340b.py
scripts/performance/llm/pretrain_nemotronh_47b.py
scripts/performance/llm/pretrain_nemotronh_56b.py
scripts/performance/llm/pretrain_nemotronh_8b.py
tests/__init__.py
tests/check_copyright_header.py
tests/conftest.py
tests/manualtest_model_downloads.py
tests/py_cprheader.txt
tests/test_data_dir.py
tests/automodel/__init__.py
tests/automodel/_dist_utils.py
tests/automodel/_test_utils.py
tests/automodel/loss/__init__.py
tests/automodel/loss/test_chunked_ce.py
tests/automodel/loss/test_linear_ce.py
tests/automodel/loss/test_masked_ce.py
tests/collections/__init__.py
tests/collections/asr/__init__.py
tests/collections/asr/conftest.py
tests/collections/asr/test_asr_classification_model.py
tests/collections/asr/test_asr_context_biasing.py
tests/collections/asr/test_asr_ctc_encoder_model_bpe.py
tests/collections/asr/test_asr_ctcencdec_model.py
tests/collections/asr/test_asr_datasets.py
tests/collections/asr/test_asr_exportables.py
tests/collections/asr/test_asr_filterbankfeatures_seq_len.py
tests/collections/asr/test_asr_hybrid_rnnt_ctc_model_bpe.py
tests/collections/asr/test_asr_hybrid_rnnt_ctc_model_char.py
tests/collections/asr/test_asr_interctc_models.py
tests/collections/asr/test_asr_lhotse_dataset.py
tests/collections/asr/test_asr_local_attn.py
tests/collections/asr/test_asr_metrics.py
tests/collections/asr/test_asr_modules.py
tests/collections/asr/test_asr_multitask_model_bpe.py
tests/collections/asr/test_asr_parts_submodules_batchnorm.py
tests/collections/asr/test_asr_regression_model.py
tests/collections/asr/test_asr_rnnt_encdec_model.py
tests/collections/asr/test_asr_rnnt_encoder_model_bpe.py
tests/collections/asr/test_asr_samplers.py
tests/collections/asr/test_asr_subsampling.py
tests/collections/asr/test_confidence_ensembles.py
tests/collections/asr/test_conformer_encoder.py
tests/collections/asr/test_custom_tokenizer.py
tests/collections/asr/test_hybrid_asr_tts_models.py
tests/collections/asr/test_jasper_block.py
tests/collections/asr/test_label_datasets.py
tests/collections/asr/test_ngram_lm.py
tests/collections/asr/test_preprocessing_segment.py
tests/collections/asr/test_ssl_models.py
tests/collections/asr/test_text_to_text_dataset.py
tests/collections/asr/confidence/__init__.py
tests/collections/asr/confidence/test_asr_confidence.py
tests/collections/asr/confidence/test_asr_confidence_metrics.py
tests/collections/asr/confidence/test_asr_confidence_primitives.py
tests/collections/asr/decoding/__init__.py
tests/collections/asr/decoding/conftest.py
tests/collections/asr/decoding/test_batched_beam_decoding.py
tests/collections/asr/decoding/test_batched_beam_hyps.py
tests/collections/asr/decoding/test_batched_hyps_and_alignments.py
tests/collections/asr/decoding/test_ctc_decoding.py
tests/collections/asr/decoding/test_cuda_graph_rnnt_greedy_decoding.py
tests/collections/asr/decoding/test_multi_task_decoding.py
tests/collections/asr/decoding/test_rnnt_alignments.py
tests/collections/asr/decoding/test_rnnt_decoding.py
tests/collections/asr/decoding/test_streaming_buffer.py
tests/collections/asr/decoding/test_streaming_decoding.py
tests/collections/asr/decoding/test_timestamps.py
tests/collections/asr/decoding/utils.py
tests/collections/asr/k2/__init__.py
tests/collections/asr/k2/test_ctc.py
tests/collections/asr/k2/test_graph_transducer.py
tests/collections/asr/k2/test_rnnt.py
tests/collections/asr/k2/test_w_transducer.py
tests/collections/asr/mixins/__init__.py
tests/collections/asr/mixins/test_transcription.py
tests/collections/asr/mixins/adapters/__init__.py
tests/collections/asr/mixins/adapters/test_asr_adapter_mixin.py
tests/collections/asr/mixins/adapters/test_asr_adapter_modules.py
tests/collections/asr/numba/__init__.py
tests/collections/asr/numba/rnnt_loss/__init__.py
tests/collections/asr/numba/rnnt_loss/test_rnnt_pytorch.py
tests/collections/asr/numba/rnnt_loss/utils/__init__.py
tests/collections/asr/numba/rnnt_loss/utils/test_gpu_rnnt_kernel.py
tests/collections/asr/numba/rnnt_loss/utils/test_reduce.py
tests/collections/asr/numba/rnnt_loss/utils/test_rnnt_helper.py
tests/collections/asr/numba/spec_augment/__init__.py
tests/collections/asr/numba/spec_augment/test_spec_aug_numba.py
tests/collections/asr/utils/__init__.py
tests/collections/asr/utils/test_data_simul_utils_asr.py
tests/collections/asr/utils/test_vad_utils_asr.py
tests/collections/audio/__init__.py
tests/collections/audio/test_audio_data_simulation.py
tests/collections/audio/test_audio_datasets.py
tests/collections/audio/test_audio_losses.py
tests/collections/audio/test_audio_maxine_models.py
tests/collections/audio/test_audio_metrics.py
tests/collections/audio/test_audio_models_flow_matching.py
tests/collections/audio/test_audio_models_mask.py
tests/collections/audio/test_audio_models_predictive.py
tests/collections/audio/test_audio_models_schroedinger_bridge.py
tests/collections/audio/test_audio_models_score_based.py
tests/collections/audio/test_audio_modules.py
tests/collections/audio/test_audio_modules_projections.py
tests/collections/audio/test_audio_modules_transforms.py
tests/collections/audio/test_audio_part_submodules_multichannel.py
tests/collections/audio/test_audio_parts_submodules_backbones.py
tests/collections/audio/test_audio_parts_submodules_flow.py
tests/collections/audio/test_audio_parts_submodules_schroedinger_bridge.py
tests/collections/audio/utils/__init__.py
tests/collections/audio/utils/test_audio_utils.py
tests/collections/common/__init__.py
tests/collections/common/loss_inputs.py
tests/collections/common/perplexity_inputs.py
tests/collections/common/pl_utils.py
tests/collections/common/test_2d_bucketing_constraint.py
tests/collections/common/test_apply_chat_template.py
tests/collections/common/test_data_utils.py
tests/collections/common/test_ema.py
tests/collections/common/test_lhotse_dataloading.py
tests/collections/common/test_lhotse_multimodal_dataloading.py
tests/collections/common/test_lhotse_multirank_rng.py
tests/collections/common/test_lhotse_nemo_adapters.py
tests/collections/common/test_lhotse_prompt_format_data_types.py
tests/collections/common/test_lhotse_seqlen_filters.py
tests/collections/common/test_metrics.py
tests/collections/common/test_optional_cuda_graphs.py
tests/collections/common/test_perf_metrics.py
tests/collections/common/test_spc_tokenizer.py
tests/collections/common/test_utils.py
tests/collections/common/mixins/__init__.py
tests/collections/common/mixins/test_adapter_common_model_mixin.py
tests/collections/common/mixins/test_adapter_modules.py
tests/collections/common/prompt_formatters/__init__.py
tests/collections/common/prompt_formatters/conftest.py
tests/collections/common/prompt_formatters/test_canary_prompt_formatter.py
tests/collections/common/prompt_formatters/test_gemma_prompt_formatter.py
tests/collections/common/prompt_formatters/test_llama2_prompt_formatter.py
tests/collections/common/prompt_formatters/test_mistral_prompt_formatter.py
tests/collections/common/prompt_formatters/test_prompt_formatter_api.py
tests/collections/common/tokenizers/__init__.py
tests/collections/common/tokenizers/text_to_speech/__init__.py
tests/collections/common/tokenizers/text_to_speech/test_tokenizer_utils.py
tests/collections/common/tokenizers/text_to_speech/test_tts_tokenizers.py
tests/collections/llm/__init__.py
tests/collections/llm/bert_pretraining.py
tests/collections/llm/common.py
tests/collections/llm/gpt_finetuning.py
tests/collections/llm/llama3_pretraining.py
tests/collections/llm/lora_mistralai.py
tests/collections/llm/megatron_gpt_pretraining.py
tests/collections/llm/megatron_mixtral_pretraining.py
tests/collections/llm/megatron_t5_finetuning.py
tests/collections/llm/megatron_t5_pretraining.py
tests/collections/llm/test_api.py
tests/collections/llm/test_fault_nvrx.py
tests/collections/llm/test_hf_import.py
tests/collections/llm/test_local_ckpt.py
tests/collections/llm/test_mnist_model_nemo2.py
tests/collections/llm/test_mnist_model_nemo2_fsdp.py
tests/collections/llm/test_nemo_jit_cb.py
tests/collections/llm/auto_conf/__init__.py
tests/collections/llm/auto_conf/test_autoconf_utils.py
tests/collections/llm/auto_conf/test_generate_configs.py
tests/collections/llm/bert/__init__.py
tests/collections/llm/bert/test_loss.py
tests/collections/llm/bert/model/__init__.py
tests/collections/llm/bert/model/test_bert.py
tests/collections/llm/bert/model/test_bert_base.py
tests/collections/llm/bert/model/test_bert_spec.py
tests/collections/llm/bert/model/test_embedding.py
tests/collections/llm/bitexact/__init__.py
tests/collections/llm/bitexact/mixtral/__init__.py
tests/collections/llm/bitexact/mixtral/compare_ckpts.py
tests/collections/llm/bitexact/mixtral/pretrain_mini_mixtral.py
tests/collections/llm/conversion/__init__.py
tests/collections/llm/conversion/test_export_to_hf.py
tests/collections/llm/conversion/test_hyena_import_from_hf.py
tests/collections/llm/conversion/test_import_from_hf.py
tests/collections/llm/fn/__init__.py
tests/collections/llm/fn/test_activation.py
tests/collections/llm/fn/test_base.py
tests/collections/llm/fn/test_mixin.py
tests/collections/llm/gpt/__init__.py
tests/collections/llm/gpt/model/__init__.py
tests/collections/llm/gpt/model/test_baichuan.py
tests/collections/llm/gpt/model/test_base.py
tests/collections/llm/gpt/model/test_chatglm.py
tests/collections/llm/gpt/model/test_evo2.py
tests/collections/llm/gpt/model/test_gemma.py
tests/collections/llm/gpt/model/test_gemma3.py
tests/collections/llm/gpt/model/test_hf_llama_embedding.py
tests/collections/llm/gpt/model/test_hyena.py
tests/collections/llm/gpt/model/test_hyena_accuracy.py
tests/collections/llm/gpt/model/test_hyena_mixer.py
tests/collections/llm/gpt/model/test_hyena_mixer_cp.py
tests/collections/llm/gpt/model/test_hyena_mixer_kernel.py
tests/collections/llm/gpt/model/test_hyena_utils.py
tests/collections/llm/gpt/model/test_llama.py
tests/collections/llm/gpt/model/test_llama_nemotron.py
tests/collections/llm/gpt/model/test_mistral.py
tests/collections/llm/gpt/model/test_mixtral.py
tests/collections/llm/gpt/model/test_model_import.py
tests/collections/llm/gpt/model/test_nemotron.py
tests/collections/llm/gpt/model/test_nemotronh.py
tests/collections/llm/gpt/model/test_nmh_nemo2_mlm_accuracy_match.py
tests/collections/llm/gpt/model/test_phi3.py
tests/collections/llm/gpt/model/test_qwen2.py
tests/collections/llm/gpt/model/test_reranker.py
tests/collections/llm/gpt/model/test_ssm.py
tests/collections/llm/gpt/model/test_starcoder.py
tests/collections/llm/gpt/model/test_starcoder2.py
tests/collections/llm/hf/__init__.py
tests/collections/llm/hf/_masked_cross_entropy.py
tests/collections/llm/hf/peft_hf.py
tests/collections/llm/hf/peft_nemorun.py
tests/collections/llm/hf/pretrain.py
tests/collections/llm/hf/pretrain_nemorun.py
tests/collections/llm/hf/sft.py
tests/collections/llm/hf/sft_fsdp2.py
tests/collections/llm/hf/sft_nemorun.py
tests/collections/llm/hf/sft_nemorun_fsdp2.py
tests/collections/llm/hf/utils.py
tests/collections/llm/io/__init__.py
tests/collections/llm/io/test_drop_unexpected_params.py
tests/collections/llm/peft/__init__.py
tests/collections/llm/peft/lora_export.py
tests/collections/llm/peft/lora_merge.py
tests/collections/llm/recipes/__init__.py
tests/collections/llm/recipes/test_baichuan2_7b.py
tests/collections/llm/recipes/test_bert_110m.py
tests/collections/llm/recipes/test_bert_340m.py
tests/collections/llm/recipes/test_bert_embedding.py
tests/collections/llm/recipes/test_chatglm3_6b.py
tests/collections/llm/recipes/test_deepseek_v2.py
tests/collections/llm/recipes/test_deepseek_v2_lite.py
tests/collections/llm/recipes/test_deepseek_v3.py
tests/collections/llm/recipes/test_e5_340m.py
tests/collections/llm/recipes/test_executor.py
tests/collections/llm/recipes/test_gemma2_27b.py
tests/collections/llm/recipes/test_gemma2_2b.py
tests/collections/llm/recipes/test_gemma2_9b.py
tests/collections/llm/recipes/test_gemma3_1b.py
tests/collections/llm/recipes/test_gemma_2b.py
tests/collections/llm/recipes/test_gemma_7b.py
tests/collections/llm/recipes/test_gpt3_175b.py
tests/collections/llm/recipes/test_hyena_1b.py
tests/collections/llm/recipes/test_hyena_40b.py
tests/collections/llm/recipes/test_hyena_7b.py
tests/collections/llm/recipes/test_hyena_base.py
tests/collections/llm/recipes/test_llama2_7b.py
tests/collections/llm/recipes/test_llama31_405b.py
tests/collections/llm/recipes/test_llama31_70b.py
tests/collections/llm/recipes/test_llama31_8b.py
tests/collections/llm/recipes/test_llama31_nemotron_70b.py
tests/collections/llm/recipes/test_llama31_nemotron_nano_8b.py
tests/collections/llm/recipes/test_llama31_nemotron_ultra_253b.py
tests/collections/llm/recipes/test_llama32_1b.py
tests/collections/llm/recipes/test_llama32_3b.py
tests/collections/llm/recipes/test_llama33_nemotron_super_49b.py
tests/collections/llm/recipes/test_llama3_70b.py
tests/collections/llm/recipes/test_llama3_70b_16k.py
tests/collections/llm/recipes/test_llama3_70b_64k.py
tests/collections/llm/recipes/test_llama3_8b.py
tests/collections/llm/recipes/test_llama3_8b_128k.py
tests/collections/llm/recipes/test_llama3_8b_16k.py
tests/collections/llm/recipes/test_llama3_8b_64k.py
tests/collections/llm/recipes/test_llama4_e128.py
tests/collections/llm/recipes/test_llama4_e16.py
tests/collections/llm/recipes/test_llama_embedding_1b.py
tests/collections/llm/recipes/test_mamba2_130m.py
tests/collections/llm/recipes/test_mamba2_1_3b.py
tests/collections/llm/recipes/test_mamba2_2_7b.py
tests/collections/llm/recipes/test_mamba2_370m.py
tests/collections/llm/recipes/test_mamba2_780m.py
tests/collections/llm/recipes/test_mamba2_8b.py
tests/collections/llm/recipes/test_mamba2_hybrid_8b.py
tests/collections/llm/recipes/test_mistral.py
tests/collections/llm/recipes/test_mixed_precision.py
tests/collections/llm/recipes/test_mixtral_8x22b.py
tests/collections/llm/recipes/test_mixtral_8x22b_64k.py
tests/collections/llm/recipes/test_mixtral_8x7b.py
tests/collections/llm/recipes/test_mixtral_8x7b_16k.py
tests/collections/llm/recipes/test_mixtral_8x7b_64k.py
tests/collections/llm/recipes/test_nemotron.py
tests/collections/llm/recipes/test_nemotron3_22b.py
tests/collections/llm/recipes/test_nemotron3_22b_16k.py
tests/collections/llm/recipes/test_nemotron3_22b_64k.py
tests/collections/llm/recipes/test_nemotron3_4b.py
tests/collections/llm/recipes/test_nemotron3_8b.py
tests/collections/llm/recipes/test_nemotron4_15b.py
tests/collections/llm/recipes/test_nemotron4_15b_16k.py
tests/collections/llm/recipes/test_nemotron4_15b_64k.py
tests/collections/llm/recipes/test_nemotron4_340b.py
tests/collections/llm/recipes/test_nemotronh_47b.py
tests/collections/llm/recipes/test_nemotronh_4b.py
tests/collections/llm/recipes/test_nemotronh_56b.py
tests/collections/llm/recipes/test_nemotronh_8b.py
tests/collections/llm/recipes/test_phi3_mini_4k_instruct.py
tests/collections/llm/recipes/test_qwen25_14b.py
tests/collections/llm/recipes/test_qwen25_1p5b.py
tests/collections/llm/recipes/test_qwen25_32b.py
tests/collections/llm/recipes/test_qwen25_500m.py
tests/collections/llm/recipes/test_qwen25_72b.py
tests/collections/llm/recipes/test_qwen25_7b.py
tests/collections/llm/recipes/test_qwen2_1p5b.py
tests/collections/llm/recipes/test_qwen2_500m.py
tests/collections/llm/recipes/test_qwen2_72b.py
tests/collections/llm/recipes/test_qwen2_7b.py
tests/collections/llm/recipes/test_qwen3_14b.py
tests/collections/llm/recipes/test_qwen3_1p7b.py
tests/collections/llm/recipes/test_qwen3_235b_a22b.py
tests/collections/llm/recipes/test_qwen3_30b_a3b.py
tests/collections/llm/recipes/test_qwen3_32b.py
tests/collections/llm/recipes/test_qwen3_4b.py
tests/collections/llm/recipes/test_qwen3_600m.py
tests/collections/llm/recipes/test_qwen3_8b.py
tests/collections/llm/recipes/test_starcoder2_15b.py
tests/collections/llm/recipes/test_starcoder2_3b.py
tests/collections/llm/recipes/test_starcoder2_7b.py
tests/collections/llm/recipes/test_starcoder_15b.py
tests/collections/llm/recipes/test_t5_11b.py
tests/collections/llm/recipes/test_t5_220m.py
tests/collections/llm/recipes/test_t5_3b.py
tests/collections/multimodal/__init__.py
tests/collections/multimodal/test_emmett.py
tests/collections/multimodal/test_speechllm_dataset.py
tests/collections/multimodal/test_speechllm_models.py
tests/collections/multimodal/test_stable_diffusion_fast_geglu.py
tests/collections/speaker_tasks/__init__.py
tests/collections/speaker_tasks/test_diar_datasets.py
tests/collections/speaker_tasks/test_diar_label_models.py
tests/collections/speaker_tasks/test_diar_lhotse_datasets.py
tests/collections/speaker_tasks/test_diar_metrics.py
tests/collections/speaker_tasks/test_diar_neural_inference.py
tests/collections/speaker_tasks/test_diar_sortformer_models.py
tests/collections/speaker_tasks/test_speaker_label_models.py
tests/collections/speaker_tasks/mixins/__init__.py
tests/collections/speaker_tasks/mixins/test_diarization.py
tests/collections/speaker_tasks/utils/__init__.py
tests/collections/speaker_tasks/utils/test_data_simul_utils_speaker.py
tests/collections/speaker_tasks/utils/test_diar_utils.py
tests/collections/speaker_tasks/utils/test_multispeaker_utils.py
tests/collections/speaker_tasks/utils/test_vad_utils_speaker.py
tests/collections/speechlm/__init__.py
tests/collections/speechlm/speech_to_text_llm_train.py
tests/collections/speechlm/hf/__init__.py
tests/collections/speechlm/hf/sft.py
tests/collections/speechlm2/__init__.py
tests/collections/speechlm2/test_audio_placeholders.py
tests/collections/speechlm2/test_datamodule.py
tests/collections/speechlm2/test_duplex_s2s.py
tests/collections/speechlm2/test_duplex_s2s_speech_decoder.py
tests/collections/speechlm2/test_freezing_params.py
tests/collections/speechlm2/test_metrics.py
tests/collections/speechlm2/test_salm.py
tests/collections/tts/__init__.py
tests/collections/tts/conftest.py
tests/collections/tts/test_helpers.py
tests/collections/tts/test_spectrogram_enhancer.py
tests/collections/tts/test_torch_tts.py
tests/collections/tts/test_tts_exportables.py
tests/collections/tts/test_waveglow.py
tests/collections/tts/g2p/__init__.py
tests/collections/tts/g2p/test_modules.py
tests/collections/tts/losses/__init__.py
tests/collections/tts/losses/test_audio_codec_loss.py
tests/collections/tts/models/__init__.py
tests/collections/tts/models/test_aligner.py
tests/collections/tts/models/test_fastpitch.py
tests/collections/tts/models/test_hifigan.py
tests/collections/tts/models/test_mixerTTS.py
tests/collections/tts/models/test_tacotron2.py
tests/collections/tts/models/test_univNet.py
tests/collections/tts/models/test_vits.py
tests/collections/tts/models/test_waveGlow.py
tests/collections/tts/modules/__init__.py
tests/collections/tts/modules/test_audio_codec_modules.py
tests/collections/tts/modules/test_submodules.py
tests/collections/tts/modules/test_transformer_2501.py
tests/collections/tts/parts/__init__.py
tests/collections/tts/parts/preprocessing/__init__.py
tests/collections/tts/parts/preprocessing/test_audio_trimming.py
tests/collections/tts/parts/preprocessing/test_feature_processors.py
tests/collections/tts/parts/preprocessing/test_features.py
tests/collections/tts/parts/utils/__init__.py
tests/collections/tts/parts/utils/test_tts_dataset_utils.py
tests/collections/vlm/__init__.py
tests/collections/vlm/clip/__init__.py
tests/collections/vlm/hf/__init__.py
tests/collections/vlm/hf/peft_hf.py
tests/collections/vlm/hf/sft_fsdp2.py
tests/collections/vlm/hf/test_hf_auto_model_for_image_text_to_text.py
tests/collections/vlm/hf/test_hf_datamodule_hf_vlm.py
tests/collections/vlm/llava_next/__init__.py
tests/collections/vlm/llava_next/test_hf_conversion.py
tests/collections/vlm/llava_next/test_interleaved_sample_encoder.py
tests/collections/vlm/llava_next/test_llava_next_train.py
tests/collections/vlm/llava_next/test_task_encoder_llava_vlm.py
tests/collections/vlm/llava_next/test_vqa_sample_encoder.py
tests/collections/vlm/neva/__init__.py
tests/collections/vlm/neva/data/__init__.py
tests/collections/vlm/neva/data/test_conversation.py
tests/collections/vlm/recipes/__init__.py
tests/collections/vlm/recipes/test_clip_b32.py
tests/collections/vlm/recipes/test_gemma3vl_12b.py
tests/collections/vlm/recipes/test_gemma3vl_27b.py
tests/collections/vlm/recipes/test_gemma3vl_4b.py
tests/collections/vlm/recipes/test_llama4_omni_e128.py
tests/collections/vlm/recipes/test_llama4_omni_e16.py
tests/collections/vlm/recipes/test_llava15_13b.py
tests/collections/vlm/recipes/test_llava15_7b.py
tests/collections/vlm/recipes/test_llava_next_7b.py
tests/collections/vlm/recipes/test_mllama_11b.py
tests/collections/vlm/recipes/test_mllama_90b.py
tests/collections/vlm/recipes/test_neva_llama3_8b.py
tests/collections/vlm/vision/__init__.py
tests/collections/vlm/vision/test_base.py
tests/collections/vlm/vision/test_llava_next_InternVIT.py
tests/core/__init__.py
tests/core/test_config_utils.py
tests/core/test_dist_ckpt.py
tests/core/test_exp_manager.py
tests/core/test_fault_tolerance.py
tests/core/test_fileio.py
tests/core/test_neural_module.py
tests/core/test_neural_types.py
tests/core/test_optimizers_schedulers.py
tests/core/test_save_restore.py
tests/core/test_serialization.py
tests/core/test_straggler_det.py
tests/core/test_torch_jit_script.py
tests/core/test_typecheck.py
tests/core/mixins/__init__.py
tests/core/mixins/adapters/__init__.py
tests/core/mixins/adapters/test_adapter_mixin.py
tests/core/mixins/adapters/test_adapter_model_mixin.py
tests/core/mixins/adapters/test_adapter_strategy.py
tests/core_ptl/__init__.py
tests/core_ptl/check_for_ranks.py
tests/core_ptl/check_imports.py
tests/core_ptl/check_manual_upload_to_hf_hub.py
tests/core_ptl/test_ptl_stateless_timer.py
tests/deploy/__init__.py
tests/deploy/nemo_deploy.py
tests/deploy/test_deploy_base.py
tests/deploy/test_deploy_pytriton.py
tests/deploy/test_deploy_query.py
tests/deploy/test_deploy_utils.py
tests/deploy/test_deployment_service.py
tests/deploy/test_hf_deployable.py
tests/deploy/test_hf_import.py
tests/deploy/test_megatronllm_deployable.py
tests/deploy/test_query_llm.py
tests/deploy/test_query_multimodal.py
tests/deploy/test_triton_deployable.py
tests/evaluation/__init__.py
tests/evaluation/deploy_in_fw_script.py
tests/evaluation/deploy_script.py
tests/evaluation/eval_unittest.py
tests/evaluation/eval_unittest_legacy.py
tests/evaluation/test_evaluation.py
tests/evaluation/test_evaluation_legacy.py
tests/evaluation/adapters/__init__.py
tests/evaluation/adapters/conftest.py
tests/evaluation/adapters/test_adapter_server.py
tests/evaluation/adapters/test_reasoning.py
tests/evaluation/adapters/test_system_message.py
tests/evaluation/adapters/test_utils.py
tests/export/__init__.py
tests/export/nemo_export.py
tests/export/test_export_onnx.py
tests/export/test_mock_import.py
tests/export/test_model_loading.py
tests/export/test_onnx_llm_exporter.py
tests/export/test_quantizer.py
tests/export/test_sentencepiece_tokenizer.py
tests/export/test_tarutils.py
tests/export/test_tensorrt_lazy_compiler.py
tests/export/test_tensorrt_llm.py
tests/export/test_tensorrt_mm_exporter.py
tests/export/test_tiktoken_tokenizer.py
tests/export/test_trt_compile.py
tests/export/test_vllm_hf_exporter.py
tests/export/trt_llm/__init__.py
tests/export/trt_llm/test_tensorrt_llm_export.py
tests/export/trt_llm/converter/__init__.py
tests/export/trt_llm/converter/test_converter_utils.py
tests/export/trt_llm/converter/test_model_converter.py
tests/export/trt_llm/converter/test_model_to_trt_llm_ckpt.py
tests/hydra/__init__.py
tests/hydra/my_app.py
tests/hydra/test_hydra_runner.py
tests/lightning/__init__.py
tests/lightning/mcore_microbatch_utils.py
tests/lightning/test_ckpt_utils.py
tests/lightning/test_data.py
tests/lightning/test_ddp_parity_checker.py
tests/lightning/test_dist_ckpt.py
tests/lightning/test_megatron_parallel.py
tests/lightning/test_nemo_logger.py
tests/lightning/test_nemo_resume_from_ckpt.py
tests/lightning/test_nemo_run.py
tests/lightning/test_precision_plugin.py
tests/lightning/test_resume.py
tests/lightning/test_state_restoration.py
tests/lightning/test_strategy_lib.py
tests/lightning/_fabric/__init__.py
tests/lightning/_fabric/test_conversion.py
tests/lightning/_io/__init__.py
tests/lightning/_io/test_api.py
tests/lightning/_io/test_hf.py
tests/lightning/_io/test_mixin.py
tests/lightning/_io/test_state.py
tests/lightning/pytorch/__init__.py
tests/lightning/pytorch/pth_optim.py
tests/lightning/pytorch/test_trainer.py
tests/lightning/pytorch/callbacks/__init__.py
tests/lightning/pytorch/callbacks/test_flops_callback.py
tests/lightning/pytorch/callbacks/test_jit_transform.py
tests/lightning/pytorch/callbacks/test_model_checkpoint.py
tests/lightning/pytorch/callbacks/test_model_transform.py
tests/lightning/pytorch/callbacks/test_nsys.py
tests/lightning/pytorch/callbacks/test_peft.py
tests/lightning/pytorch/callbacks/test_preemption.py
tests/lightning/pytorch/callbacks/test_runtime_estimator.py
tests/lightning/pytorch/callbacks/test_speed_monitor.py
tests/lightning/pytorch/strategies/__init__.py
tests/lightning/pytorch/strategies/test_fsdp2_strategy.py
tests/lightning/pytorch/strategies/test_fsdp_strategy.py
tests/lightning/pytorch/strategies/test_megatron_strategy.py
tests/setup/__init__.py
tests/setup/__main__.py
tests/setup/models/__init__.py
tests/setup/models/create_hf_model.py
tests/utils/__init__.py
tests/utils/test_enum.py
tests/utils/test_env_var_parsing.py
tests/utils/test_flops_formulas.py
tests/utils/test_get_rank.py
tests/utils/test_import_utils.py
tests/utils/test_msc_utils.py
tests/utils/test_trainer_utils.py
tests/utils/test_utils.py
tools/ctc_segmentation/requirements.txt