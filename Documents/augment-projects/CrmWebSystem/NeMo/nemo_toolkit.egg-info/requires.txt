fsspec==2024.12.0
huggingface_hub>=0.24
numba
numpy>=1.22
onnx>=1.7.0
protobuf~=4.24.4
python-dateutil
ruamel.yaml
scikit-learn
setuptools>=70.0.0
tensorboard
text-unidecode
torch
tqdm>=4.41.0
wget
wrapt

[all]
black~=24.3
click>=8.1
coverage
isort<6.0.0,>5.1.0
parameterized
pytest
pytest-httpserver
pytest-mock
pytest-runner
ruamel.yaml
sphinx
sphinxcontrib-bibtex
wandb
wget
wrapt
nemo_run
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
webdataset>=0.2.86
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
braceexpand
editdistance
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer
num2words
numpy<2.0.0
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2
attrdict
cdifflib==1.2.6
janome
kornia
librosa
matplotlib
nltk
pypinyin
pypinyin-dict
seaborn
jiwer>=2.0.0
progress>=1.5
tabulate>=0.8.7
textdistance>=4.1.5
tqdm
addict
clip
diffusers>=0.19.3
einops_exts
imageio
megatron-energon==5.2.0
nerfacc>=0.5.3
open_clip_torch==2.24.0
qwen_vl_utils
taming-transformers
torchdiffeq
torchsde
trimesh
lhotse>=1.22.0
librosa>=0.10.0
pystoi
flask
nvidia-lm-eval

[all:"arm" not in platform_machine and "aarch" not in platform_machine and sys_platform != "darwin"]
nemo_text_processing

[all:platform_machine != "x86_64" or platform_system != "Darwin"]
pesq

[all:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[all:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72

[all:sys_platform == "linux" and platform_machine == "x86_64"]
decord

[asr]
braceexpand
editdistance
einops
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
ruamel.yaml
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer
num2words
numpy<2.0.0
datasets
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[asr-only]
braceexpand
editdistance
einops
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
ruamel.yaml
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer

[asr:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[audio]
einops
lhotse>=1.22.0
librosa>=0.10.0
matplotlib
pystoi
scipy>=0.14
soundfile
datasets
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[audio:platform_machine != "x86_64" or platform_system != "Darwin"]
pesq

[audio:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[automodel]

[automodel:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[common]
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[common-only]
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0

[common:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[core]
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[core:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[ctc_segmentation]
num2words
numpy<2.0.0

[deploy]
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2
flask
nvidia-lm-eval
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86
addict
clip
diffusers>=0.19.3
einops_exts
imageio
kornia
megatron-energon==5.2.0
nerfacc>=0.5.3
open_clip_torch==2.24.0
qwen_vl_utils
taming-transformers
torchdiffeq
torchsde
trimesh
attrdict
cdifflib==1.2.6
janome
librosa
matplotlib
nltk
pypinyin
pypinyin-dict
seaborn
braceexpand
editdistance
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
ruamel.yaml
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer
num2words
numpy<2.0.0
accelerate
fastapi
pydantic-settings
uvicorn

[deploy:"arm" not in platform_machine and "aarch" not in platform_machine and sys_platform != "darwin"]
nemo_text_processing

[deploy:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[deploy:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72
nvidia-pytriton

[deploy:sys_platform == "linux" and platform_machine == "x86_64"]
decord

[eval]
flask
nvidia-lm-eval

[lightning]
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[llm]
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2
flask
nvidia-lm-eval
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[llm:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[llm:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72

[multimodal]
addict
clip
diffusers>=0.19.3
einops_exts
imageio
kornia
megatron-energon==5.2.0
nerfacc>=0.5.3
open_clip_torch==2.24.0
qwen_vl_utils
taming-transformers
torchdiffeq
torchsde
trimesh
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2
flask
nvidia-lm-eval
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[multimodal-only]
addict
clip
diffusers>=0.19.3
einops_exts
imageio
kornia
megatron-energon==5.2.0
nerfacc>=0.5.3
open_clip_torch==2.24.0
qwen_vl_utils
taming-transformers
torchdiffeq
torchsde
trimesh

[multimodal-only:sys_platform == "linux" and platform_machine == "x86_64"]
decord

[multimodal:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[multimodal:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72

[multimodal:sys_platform == "linux" and platform_machine == "x86_64"]
decord

[nlp]
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2
flask
nvidia-lm-eval
datasets
einops
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[nlp-only]
accelerated-scan
boto3
faiss-cpu
flask_restful
ftfy
gdown
h5py
ijson
jieba
markdown2
matplotlib>=3.3.2
megatron_core
multi-storage-client>=0.21.0
nltk>=3.6.5
numpy<2
nvtx
opencc
pangu
prettytable
rapidfuzz
rouge_score
sacrebleu
sentence_transformers
tiktoken==0.7.0
unstructured==0.14.9
zarr<3.0.0,>=2.18.2

[nlp-only:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72

[nlp:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[nlp:platform_system != "Darwin"]
nvidia-modelopt[torch]<=0.31.0,>=0.27.0
nvidia-resiliency-ext<1.0.0,>=0.3.0
tensorstore<0.1.72

[run]
nemo_run

[slu]
jiwer>=2.0.0
progress>=1.5
tabulate>=0.8.7
textdistance>=4.1.5
tqdm
braceexpand
editdistance
einops
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
ruamel.yaml
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer
num2words
numpy<2.0.0
datasets
inflect
mediapy==1.1.6
pandas
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[slu:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[test]
black~=24.3
click>=8.1
coverage
isort<6.0.0,>5.1.0
parameterized
pytest
pytest-httpserver
pytest-mock
pytest-runner
ruamel.yaml
sphinx
sphinxcontrib-bibtex
wandb
wget
wrapt
attrdict
cdifflib==1.2.6
einops
janome
jieba
kornia
librosa
matplotlib
nltk
pandas
pypinyin
pypinyin-dict
seaborn
datasets
inflect
mediapy==1.1.6
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
webdataset>=0.2.86

[test:"arm" not in platform_machine and "aarch" not in platform_machine and sys_platform != "darwin"]
nemo_text_processing

[test:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5

[tts]
attrdict
cdifflib==1.2.6
einops
janome
jieba
kornia
librosa
matplotlib
nltk
pandas
pypinyin
pypinyin-dict
seaborn
braceexpand
editdistance
g2p_en
jiwer
kaldi-python-io
kaldiio
lhotse!=1.31.0
librosa>=0.10.1
marshmallow
optuna
packaging
pyannote.core
pyannote.metrics
pydub
pyloudnorm
resampy
ruamel.yaml
scipy>=0.14
soundfile
sox<=1.5.0
texterrors<1.0.0
whisper_normalizer
num2words
numpy<2.0.0
datasets
inflect
mediapy==1.1.6
sacremoses>=0.0.43
sentencepiece<1.0.0
cloudpickle
fiddle
hydra-core<=1.3.2,>1.3
lightning<=2.4.0,>2.2.1
omegaconf<=2.3
peft
torchmetrics>=0.11.0
transformers<=4.52.0,>=4.51.0
wandb
webdataset>=0.2.86

[tts:"arm" not in platform_machine and "aarch" not in platform_machine and sys_platform != "darwin"]
nemo_text_processing

[tts:platform_machine == "x86_64" and platform_system != "Darwin"]
bitsandbytes==0.45.5
