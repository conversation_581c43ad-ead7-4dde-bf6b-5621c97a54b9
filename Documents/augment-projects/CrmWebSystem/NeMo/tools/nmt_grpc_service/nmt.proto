// Copyright (c) 2021, NVIDIA CORPORATION.  All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package nvidia.riva.nmt;

option cc_enable_arenas = true;
option go_package = "nvidia.com/riva_speech";

// Riva NLP Services implement task-specific APIs for popular NLP tasks including
// intent recognition (as well as slot filling), and entity extraction.
service RivaTranslate {
  rpc TranslateText(TranslateTextRequest) returns (TranslateTextResponse) {}

}

message TranslateTextRequest {
    repeated string texts = 1;

    string source_language = 3;
    string target_language = 4;
}

message Translation {
    string translation = 1;

    string language = 2;
}

message TranslateTextResponse {
    repeated Translation translations = 1;

}
