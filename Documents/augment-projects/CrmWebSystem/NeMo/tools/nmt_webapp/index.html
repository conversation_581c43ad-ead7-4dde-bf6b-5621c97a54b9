<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Neural Machine Translation Demo App</title>
  <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">  
  <link rel="stylesheet" href="/resources/demos/style.css">
  <style>
    .ui-resizable-se {
      bottom: 17px;
    }
  </style>
  <link rel="stylesheet" href="style.css">
  <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script>
  $( function() {
    $( "#tgt_text" ).resizable({
      handles: "se"
    });
  } );
  $( function() {
    $( "#src_text" ).resizable({
      handles: "se"
    });
  } );
  $( function() {
    $( "#translate_button" ).button();
    $( "#translate_button" ).click( function( event ) {
        $.ajax({
          url: "http://*************:5000/translate",
          type: "GET",                    
          data: {
            text: $( "#src_text" ).val(),
            langpair: $('input[type=radio][name=langpair]:checked').attr('id'),
            do_moses: true
          },
          success: function (result) {            
            $( "#tgt_text" ).val(result.translation);
          },
          error: function (err) {
            alert('Sorry, can not reach translation service');            
          },
        })
    } );
  } );
  </script>
</head>
<body>
<div class="contact-us">
  <h1>Neural Machine Translation Demo</h1>
  <p>
  <br/>
  <h4>Running NeMo commit id: v1.0.2  </h4>
  <br/>
  </p>
  <p>
  <fieldset>
    <legend>Select translation direction: </legend>
    <table>
      <tr>
        <td>
          <label for="en-de">English to German</label>
          <input type="radio" name="langpair" id="en-de">
        </td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
        <td>
          <label for="de-en">German to English</label>
          <input type="radio" name="langpair" id="de-en">
        </td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
        <td>
          <label for="es-en">Spanish to English</label>
          <input type="radio" name="langpair" id="es-en">
        </td>
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	<td>
	  <label for="en-es" selected="selected">English to Spanish</label>
	  <input type="radio" name="langpair" id="en-es" checked>
  </td> 
      </tr>
      <tr>
         <td>
           <label for="en-ru">English to Russian</label>
           <input type="radio" name="langpair" id="en-ru">
         </td>
         <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
         <td>
           <label for="ru-en">Russian to English</label>
           <input type="radio" name="langpair" id="ru-en">
         </td>
         <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
         <td>
           <label for="en-fr">English to French</label>
	   <input type="radio" name="langpair" id="en-fr">
	 </td>
	 <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	 <td>
	  <label for="fr-en">French to English</label>
          <input type="radio" name="langpair" id="fr-en">
         </td>
         <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
         <td>
          <label for="en-zh">English to Chinese</label>
          <input type="radio" name="langpair" id="en-zh">
         </td>
	 <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
         <td>
          <label for="zh-en">Chinese to English</label>
          <input type="radio" name="langpair" id="zh-en">
         </td>
      </tr>
    </table>
  </fieldset>
  <div>
    <table>
      <tr>
        <td>
          <textarea id="src_text" rows="12" cols="60">Type something here, chose language pair and click "Translate" button.</textarea>
        </td>
      </tr>
      <tr>
        <td>
          <button id="translate_button">Translate !</button>
        </td>
      </tr>
      <tr>
        <td>
          <textarea id="tgt_text" rows="12" cols="60" readonly></textarea>
        </td>
      </tr>
    </table>
  </div>
  </p>
</div>
 
 
</body>
</html>
