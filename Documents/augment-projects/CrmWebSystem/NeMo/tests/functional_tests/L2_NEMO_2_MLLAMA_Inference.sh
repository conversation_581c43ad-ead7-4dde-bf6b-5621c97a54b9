# Copyright (c) 2020-2025, NVIDIA CORPORATION.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
TRANSFORMERS_OFFLINE=1 \
  coverage run -a --data-file=/workspace/.coverage --source=/workspace/nemo scripts/vlm/mllama_generate.py \
    --local_model_path /home/<USER>/nemo2_ckpt/Llama-3.2-11B-Vision-Instruct \
    --processor_name /home/<USER>/HF_HOME/hub/models--meta-llama--Llama-3.2-11B-Vision-Instruct/snapshots/9eb2daaa8597bf192a8b0e73f848f3a102794df5 \
    --num_tokens_to_generate 3
