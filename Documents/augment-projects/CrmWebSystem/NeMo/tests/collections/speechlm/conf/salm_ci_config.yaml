name: megatron_audio_gpt_peft


############ Data ############
data:
  common:
    global_batch_size: 2
    micro_batch_size: 2
    max_seq_length: 2048
    min_seq_length: 1
    sample_rate: 16000
    end_string: null
    context_key: 'context'
    answer_key: 'answer'
    prompt_format: null
    prompt_template: "Q: {context}\nA: {answer}" # fstring to use for assistant prompt.
    separate_prompt_and_response_with_newline: False
    truncation_field: 'context'
    add_eos: true
    add_sep: false
    add_bos: false
    tokens_to_generate: 128
    audio_locator: null
    add_boa_eoa: false

  train_ds:
    # Example of how to specify paths to multiple datasets
    # manifest_filepath:
    #   - /path/to/squad.jsonl
    #   - /path/to/mnli.jsonl
    #   - /path/to/boolq.jsonl
    # Example of how each dataset is formatted
    # {'audio_filepath': 'audio1.wav', 'offset': 0.0, 'duration': 12.3, 'context': 'transcribe this audio', 'answer': 'I have a dream...'}
    # the 'answer' field can also be 'text', and a default 'context' field is added if missing in manigests, so as to work with ASR manifests
    manifest_filepath: null # Path to a list of JSONL files corresponding to the source data.
    global_batch_size: ${data.common.global_batch_size}
    micro_batch_size: ${data.common.micro_batch_size}
    shuffle: True
    num_workers: 2
    pin_memory: True
    max_seq_length: ${data.common.max_seq_length}
    min_seq_length: ${data.common.min_seq_length}
    drop_last: True
    # Notably, the data weights are controlled by either bucketing_weights
    # or concat_sampling_probabilities depending on the dataset type (tar and
    # non-tar).
    concat_sampling_probabilities: null # When providing a list of datasets, this arg defines the sampling probabilities from each dataset when strategy='random'
    context_key: ${data.common.context_key}
    answer_key: ${data.common.answer_key}
    end_string: ${data.common.end_string}
    add_eos: ${data.common.add_eos}
    add_sep: ${data.common.add_sep}
    add_bos: ${data.common.add_bos}
    separate_prompt_and_response_with_newline: ${data.common.separate_prompt_and_response_with_newline}
    truncation_field: ${data.common.truncation_field} # Options: ['context', 'answer']
    prompt_template: ${data.common.prompt_template} # fstring to use for assistant prompt. Example: "Q: {input}\nA: {output}"
    # ASR configs
    sample_rate: ${data.common.sample_rate}
    max_duration: 24 # it is set for LibriSpeech, you may need to update it for your dataset
    min_duration: 0.1
    # tarred datasets
    is_concat: false
    is_tarred: false
    tarred_audio_filepaths: null
    shuffle_n: 2048
    # bucketing params
    bucketing_strategy: "fully_randomized"
    bucketing_batch_size: null
    audio_locator: ${data.common.audio_locator}
    prompt_format: ${data.common.prompt_format}

  validation_ds:
    manifest_filepath: null # Path to a list of JSONL files corresponding to the source data. Data format is identical to train_ds.
    global_batch_size: ${data.common.global_batch_size}
    micro_batch_size: ${data.common.micro_batch_size}
    shuffle: False
    num_workers: 2
    pin_memory: True
    max_seq_length: ${data.common.max_seq_length}
    min_seq_length: ${data.common.min_seq_length}
    drop_last: true  # no effect, the dataloader will drop last for train and validation anyway
    context_key: ${data.common.context_key}
    answer_key: ${data.common.answer_key}
    add_eos: ${data.common.add_eos}
    end_string: ${data.common.end_string}
    add_sep: ${data.common.add_sep}
    add_bos: ${data.common.add_bos}
    separate_prompt_and_response_with_newline: ${data.common.separate_prompt_and_response_with_newline}
    output_file_path_prefix: null # Prefix of the file to write predictions to.
    truncation_field: ${data.common.truncation_field} # Options: ['context', 'answer']
    index_mapping_dir: null # Path to a directory to write index mapping files.
    prompt_template: ${data.common.prompt_template} # fstring to use for assistant prompt. Example: "Q: {input}\nA: {output}"
    tokens_to_generate: ${data.common.tokens_to_generate}
    write_predictions_to_file: False
    # ASR configs
    sample_rate: ${data.common.sample_rate}
    audio_locator: ${data.train_ds.audio_locator}
    prompt_format: ${data.common.prompt_format}

    log_every_n_steps: 2
    metric:
      name: "loss" # Name of the evaluation metric to use. Options: ['exact_string_match', 'loss', 'wer', 'bleu', 'rouge']
      average: null # Average the metric over the dataset. Options: ['macro', 'micro']. Works only for 'F1', 'accuracy' etc. Refer to torchmetrics for metrics where this is supported.
      num_classes: null

############ Model ############
model:
  freeze_language_model: true
  freeze_speech_model: false
  freeze_modality_adapter: false
  
  llm:
    _target_: nemo.collections.llm.LlamaModel
    pretrained_model: null
    config: 
      _target_: tests.collections.llm.common.Llama3ConfigCI
    
  speech_encoder:
    pretrained_model: "stt_en_fastconformer_transducer_large"
    target_module: "encoder"

  modality_adapter:
    input_key_from: "d_model"  # attribute of model dim in the speech model
    input_key_to: "feat_in"  # attribute of input dim in the modality adapter
    output_key: "num_classes"  # attrubuite of output dim in the modality adapter
    config:
      _target_: nemo.collections.asr.modules.ConvASRDecoder
      feat_in: -1  # auto-set
      num_classes: -1  # auto-set
      add_blank: false  # don't add blank class

  peft:
    _target_: nemo.collections.llm.peft.LoRA
    dim: 16


############ Optimizer ############
optim:
  _target_: nemo.lightning.MegatronOptimizerModule
  config:
    _target_: megatron.core.optimizer.OptimizerConfig
    optimizer: adam
    lr: 1e-4
    clip_grad: 1.0
    weight_decay: 0.0001
  lr_scheduler:
    _target_: nemo.lightning.pytorch.optim.CosineAnnealingScheduler
    max_steps: ${trainer.max_steps}
    warmup_steps: 250
    constant_steps: 10000
    min_lr: 5e-5

############ Trainer ############

# Set this to "DD:HH:MM:SS" format to limit the max time for this job
# If `max_time_per_run` is set, `strategy.ckpt_async_save` must be set to false
max_time_per_run: "00:00:00:20"

trainer:
  # _target_: nemo.lightning.Trainer
  devices: -1
  accelerator: gpu
  num_nodes: 1
  max_epochs: 1000  # used to keep epoch logging correctly, but training will stop based on max_steps
  max_steps: 1000000 # 1M steps
  log_every_n_steps: 2 # frequency with which training steps are logged 
  val_check_interval: 1.0 # If is an int n > 1, will run val every n training steps, if a float 0.0 - 1.0 will run val every epoch fraction, e.g. 0.25 will run val every quarter epoch
  num_sanity_val_steps: 0

strategy:
  _target_: nemo.collections.speechlm.strategies.SpeechLMMegatronStrategy
  tensor_model_parallel_size: 1
  pipeline_model_parallel_size: 1
  ckpt_async_save: false

callbacks:
  checkpoint:
    _target_: nemo.lightning.pytorch.callbacks.ModelCheckpoint
    filename: '${name}--{${callbacks.checkpoint.monitor}:.3f}-{step}'
    monitor: "val_loss"
    mode: "min"
    save_last: true
    save_top_k: 1
    save_weights_only: false
    always_save_context: true
    save_optim_on_train_end: true

plugins:
  _target_: nemo.lightning.MegatronMixedPrecision
  precision: "bf16-mixed"
  autocast_enabled: null

############ AutoResume ############
resume:
  _target_: nemo.collections.speechlm.utils.resume.SpeechLMAutoResume
  resume_from_directory: null
  resume_from_path: null
  adapter_path: null
  resume_if_exists: true
  resume_past_end: false
  resume_ignore_no_checkpoint: true


############ Logging ############
logger:
  _target_: nemo.lightning.NeMoLogger
  log_dir: null  # default to ./nemo_experiments
  name: ${name}
