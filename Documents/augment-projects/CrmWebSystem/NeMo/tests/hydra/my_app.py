# Copyright (c) 2021, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from dataclasses import dataclass

import hydra
from omegaconf import MISSING, OmegaConf

from nemo.core.config import hydra_runner


@dataclass
class DefaultConfig:
    """
    This is structured config for this application.

    It provides the schema used for validation of user-written spec file
    as well as default values of the selected parameters.
    """

    # Dataset. Available options: [imdb, sst2]
    dataset_name: str = MISSING


@hydra_runner(config_name="DefaultConfig", schema=DefaultConfig)
def my_app(cfg):
    print(OmegaConf.to_yaml(cfg))
    # Get dataset_name.
    dataset_name = cfg.dataset_name


if __name__ == "__main__":
    my_app()
