{"cells": [{"cell_type": "markdown", "metadata": {"id": "IgwkJGe6ESZK"}, "source": ["# Multilingual ASR models with Subword Tokenization in NeMo\n", "This notebook helps you get started with NeMo multilingual ASR models; i.e. models that can transcribe audio in more than one language. You will learn how to use an existing pre-trained multilingual model for transcription, as well as how to create and train a new one.\n", "\n", "\n", "NOTE: User is responsible for checking the content of datasets and the applicable licenses and determining if suitable for the intended use.\n", "\n", "\n", "Please note that the current NeMo implementation is limited to models with subword tokenization. \n", "\n", "\n", "There are two general ways to create multilingual models.  One could simply combine datasets from different languages into one merged dataset and train a model on that dataset. For this to work, there's no need to even keep track of the language[s] in each sample. Nor would there be an easy way to determine which language was used during inference.\n", "\n", "\n", "The second approach, which we implemented in NeMo, centers around the idea of reusing existing pre-trained monolingual tokenizers for each language, and simply combining them. The tokenizers for each language could be trained on large non-ASR text corpora (for maximum generalization), extracted from existing monolingual NeMo checkpoints, or trained on monolingual ASR ground truth as usual.\n", "\n", "\n", "The diagram below illustrates how this works at inference time:\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Inference View](images/multilang_asr_inference.png)"]}, {"cell_type": "markdown", "metadata": {"id": "OyeYkFZBLS7b"}, "source": ["We see that each language is assigned a range of token ids and the model is trained to produce token ids in that range in order to produce samples in that language. For instance, in the example in the diagram, if the token id generated by the model is 100, we know that it belongs to *lang1* and should be sent to *lang1* for decoding. If the token id is 1500, it belongs to *lang2*.  Note that we do not need to know which language a particular audio sample is in *a priori*; but the model generates an implicit language id for each token based on its id.\n", "\n", "The total number of classes the model is trained to output is simply the sum of the tokenizer vocabulary lengths.\n", "\n", "Note that we need to subtract the language offset number from each token id in order for the \"as is\" monolingual tokenizer to decode it.\n", "\n", "At training time, however, we do need to provide language id for each training sample. Here's how it works:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Train View](images/multilang_asr_train.png)"]}, {"cell_type": "markdown", "metadata": {"id": "kyv-mNlBNK8D"}, "source": ["The audio for each sample is processed the same way as for the monolingual samples, but in order to generate the correct token ids for the transcripts, we need to know which tokenizer to use. Therefore, we do need an additional field ('lang') in the manifest for each sample.  After the monolingual tokenizer generates the token id, we offset it by the offset of each language and use it for model training.\n", "\n", "\n", "Now our picture is complete. Multilingual ground truth requires an extra field, 'lang' in the manifest for each sample and a separate monolingual tokenizer for each language. \n", "\n", "\n", "Let's see the code now."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "You can run either this notebook locally (if you have all the dependencies and a GPU) or on Google Colab.\n", "\n", "Instructions for setting up Colab are as follows:\n", "1. Open a new Python 3 notebook.\n", "2. Import this notebook from GitHub (File -> Upload Notebook -> \"GITHUB\" tab -> copy/paste GitHub URL)\n", "3. Connect to an instance with a GPU (Runtime -> Change runtime type -> select \"GPU\" for hardware accelerator)\n", "4. Run this cell to set up dependencies.\n", "5. Restart the runtime (Runtime -> Restart Runtime) for any upgraded packages to take effect\n", "\"\"\"\n", "# If you're using Google Colab and not running locally, run this cell.\n", "\n", "%env DEBIAN_FRONTEND=noninteractive \n", "%env DEBCONF_NONINTERACTIVE_SEEN=true\n", "\n", "## Install dependencies\n", "!pip install wget\n", "!apt-get update \n", "!apt-get -y install sox libsndfile1 ffmpeg\n", "!pip install text-unidecode\n", "!pip install matplotlib>=3.3.2\n", "# this is needed for RNNT loss\n", "!pip install --upgrade numba\n", "!pip install datasets==2.21.0 # downgrading to 2.21.0 because latest version (3.0.0) has some issues\n", "\n", "# this is needed to pre-process MCV Spanish dataset, which contains mp3 files\n", "!apt-get install -y sox libsox-fmt-mp3\n", "\n", "## Install NeMo\n", "## We are using the main branch but you might want to adjust that too\n", "BRANCH = 'main'\n", "!python -m pip install git+https://github.com/NVIDIA/NeMo.git@$BRANCH#egg=nemo_toolkit[all]\n", "\n", "\"\"\"\n", "Remember to restart the runtime for the kernel to pick up any upgraded packages (e.g. matplotlib)!\n", "Alternatively, you can uncomment the exit() below to crash and restart the kernel, in the case\n", "that you want to use the \"Run All Cells\" (or similar) option.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9580, "status": "ok", "timestamp": 1648773502616, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "n9SDDSwrWQt2", "outputId": "741d92fd-a121-493e-df45-8fd3cac2491e"}, "outputs": [], "source": ["import nemo.collections.asr as nemo_asr\n", "import os\n", "from omegaconf import OmegaConf"]}, {"cell_type": "markdown", "metadata": {"id": "LMKgO-4RaUfp"}, "source": ["## Datasets download\n", "Let's download the mini Librispeech (English) dataset. It is OK for the purposes of this tutorial, but for anything real, you will need to get at least the entire Librispeech dataset (960 hrs)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 5, "status": "ok", "timestamp": 1648773502616, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "3vWr1vndadFR"}, "outputs": [], "source": ["!mkdir -p datasets/mini"]}, {"cell_type": "markdown", "metadata": {"id": "tJB10XNUdLFT"}, "source": ["We will use the `get_librispeech_data.py` script located in the nemo/scripts/dataset_processing dir if you cloned NeMo repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 215, "status": "ok", "timestamp": 1648773502827, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "9A2eK01CahPi", "outputId": "9169d3d3-96b9-480c-c9c4-64ee13718d3e"}, "outputs": [], "source": ["if not os.path.exists(\"get_librispeech_data.py\"):\n", "    !wget https://raw.githubusercontent.com/NVIDIA/NeMo/main/scripts/dataset_processing/get_librispeech_data.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 67944, "status": "ok", "timestamp": 1648773570769, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "aIf1tDDZaySs", "outputId": "1c5e2ea8-ad22-44a1-81a0-7b9d47cddbdc"}, "outputs": [], "source": ["!python get_librispeech_data.py \\\n", "  --data_root \"datasets/mini/\" \\\n", "  --data_sets mini"]}, {"cell_type": "markdown", "metadata": {"id": "ATf7lapcbAxB"}, "source": ["Now, let's download the Mozilla CommonVoice Spanish dataset. We will ignore the larger train file and get just the test part for the purposes of this tutorial. For good results, you will need to get the train files and likely other datasets too, bringing the total to over 1k hours. \n", "\n", "Website steps:\n", "- Visit https://huggingface.co/settings/profile\n", "- Visit \"Access Tokens\" on list of items.\n", "- Create new token - provide a name for the token and \"read\" access is sufficient.\n", "  - PRESERVE THAT TOKEN API KEY. You can copy that key for next step.\n", "- Visit the [HuggingFace Dataset page for Mozilla Common Voice 3.0](https://huggingface.co/datasets/mozilla-foundation/common_voice_3_0)\n", "  - There should be a section that asks you for your approval.\n", "  - Make sure you are logged in and then read that agreement.\n", "  - If and only if you agree to the text, then accept the terms.\n", "\n", "Code steps:\n", "- Now below, run `login()` \n", "- Paste your preserved HF TOKEN API KEY to the text box."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 247, "status": "ok", "timestamp": 1648773571004, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "iVNuLvCbaydc"}, "outputs": [], "source": ["from huggingface_hub import login\n", "login()"]}, {"cell_type": "markdown", "metadata": {"id": "YpZNMYfKde9n"}, "source": ["We will use the `convert_hf_dataset_to_nemo.py` script located in the nemo/scripts/speech_recognition dir if you cloned NeMo repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 406, "status": "ok", "timestamp": 1648773571409, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "0Fdou8cubLZy", "outputId": "6db0085a-7ebf-4c68-a815-54846231e180"}, "outputs": [], "source": ["if not os.path.exists(\"convert_hf_dataset_to_nemo.py\"):\n", "    !wget https://raw.githubusercontent.com/NVIDIA/NeMo/$BRANCH/scripts/speech_recognition/convert_hf_dataset_to_nemo.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1000547, "status": "ok", "timestamp": 1648776340713, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "kJaihqWubtl4", "outputId": "72144055-f725-4c07-da21-e983a3ccb16f"}, "outputs": [], "source": ["! python convert_hf_dataset_to_nemo.py \\\n", "    path=\"mozilla-foundation/common_voice_3_0\" \\\n", "    output_dir=\"datasets\" name=\"es\" split='test' \\\n", "    use_auth_token=True"]}, {"cell_type": "markdown", "metadata": {"id": "4YO81u2tQtVP"}, "source": ["To save time, let us create smaller subsets of the Spanish dataset for training and validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 531, "status": "ok", "timestamp": 1648776349021, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "UFe1zmoUQsvA"}, "outputs": [], "source": ["!head -1000 datasets/mozilla-foundation/common_voice_3_0/es/test/test_mozilla-foundation_common_voice_3_0_manifest.json > commonvoice_dev_manifest_1000.json\n", "!tail -1729 datasets/mozilla-foundation/common_voice_3_0/es/test/test_mozilla-foundation_common_voice_3_0_manifest.json > commonvoice_train_manifest.json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!wc -l commonvoice_train_manifest.json"]}, {"cell_type": "markdown", "metadata": {"id": "OugXEqVfcSqY"}, "source": ["We will return to these datasets later for additional processing."]}, {"cell_type": "markdown", "metadata": {"id": "4YX8Ur8GY6U8"}, "source": ["# Inference using an existing multilingual checkpoint\n", "Pretrained Multilingual models can be worked with in the same way as monolingual ones. Let us start by downloading a pre-trained checkpoint from NGC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 13968, "status": "ok", "timestamp": 1648776367230, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "4hcDCSunWYSJ", "outputId": "4afeb4e4-664d-40fb-f616-e49f5887d44b"}, "outputs": [], "source": ["asr_model = nemo_asr.models.EncDecRNNTBPEModel.from_pretrained(model_name=\"stt_enes_contextnet_large\")"]}, {"cell_type": "markdown", "metadata": {"id": "TKq6EiPPZVty"}, "source": ["Let's take a look at the tokenizer of this model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 17, "status": "ok", "timestamp": 1648776367230, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "gxbs4ZbtEGAu", "outputId": "c729790d-b2ce-48d4-e850-bf8549344381"}, "outputs": [], "source": ["print(OmegaConf.to_yaml(asr_model.cfg.tokenizer))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 16, "status": "ok", "timestamp": 1648776367231, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "32FOXBq-Yq6_", "outputId": "b05a8b45-099a-4bd0-f9da-c290da91f546"}, "outputs": [], "source": ["print(OmegaConf.to_yaml(asr_model.tokenizer.langs))"]}, {"cell_type": "markdown", "metadata": {"id": "QrrNjVIpZdll"}, "source": ["We see that there are two tokenizers: English and Spanish, packaged inside the nemo tar archive. They are aggregated together using a special AggregateTokenizer class. \n", "\n", "In this case, there are two tokenizers, but you can have as many as you want.\n", "\n", "Let's transcribe some audio!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 520, "status": "ok", "timestamp": 1648776373743, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "uL701BkIZ3Z6"}, "outputs": [], "source": ["en_files = [\"./datasets/mini/LibriSpeech/dev-clean-2-processed/7976-110523-0000.wav\", \"./datasets/mini/LibriSpeech/dev-clean-2-processed/7976-110523-0001.wav\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 75}, "executionInfo": {"elapsed": 2364, "status": "ok", "timestamp": 1648776377784, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "kit95pAHdpi_", "outputId": "fe7e5e6a-908f-4829-c5a6-2693c6285e48"}, "outputs": [], "source": ["import librosa\n", "import IPython.display as ipd\n", "\n", "# Load and listen to an english audio sample\n", "audio, sample_rate = librosa.load(en_files[1])\n", "\n", "ipd.Audio(audio, rate=sample_rate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["fe775031e7b7423e8e581d44bac68e5e", "9f7966ff979248bcafaf389f389d7e52", "cbef296aea8746c38c02012e1e06a402", "013abc9bfddf456abf15dc2b0567d969", "2fb8b201a2064e008d643e51f5125608", "2b448e3e56534d08a721702f3ae37505", "c29036af3e4945739a58e372315a44f4", "9be44dd1d7c44a66b54a1af08255b86f", "5476c1db05024cd7ab38670d61f1951b", "f1ca1411499d4fd6a43c0a94884ab5c3", "********************************"]}, "executionInfo": {"elapsed": 729, "status": "ok", "timestamp": 1648776378709, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "GhUh8bMnbGXC", "outputId": "55f19bed-1ab8-4b5a-d4f6-360c6fd2007e"}, "outputs": [], "source": ["transcripts = asr_model.transcribe(audio = en_files) [0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4, "status": "ok", "timestamp": 1648776378710, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "Fk5FdZ5sd9yg", "outputId": "62a36060-4511-49f6-8d3c-915190f06e88"}, "outputs": [], "source": ["transcripts"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 1, "status": "ok", "timestamp": 1648776380072, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "UtExsEMTbHnt"}, "outputs": [], "source": ["es_files = ['datasets/mozilla-foundation/common_voice_3_0/es/test/clips/common_voice_es_18481930.wav', 'datasets/mozilla-foundation/common_voice_3_0/es/test/clips/common_voice_es_18481932.wav']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 75}, "executionInfo": {"elapsed": 1075, "status": "ok", "timestamp": 1648776381904, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "f3gL5RTef8of", "outputId": "884353d0-407b-4b3b-f18b-7342ec75fb5f"}, "outputs": [], "source": ["# Now let's listen to a Spanish sample:\n", "audio, sample_rate = librosa.load(es_files[0])\n", "\n", "ipd.Audio(audio, rate=sample_rate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 84, "referenced_widgets": ["d3e926edb0b24e86ae84f8747a643643", "9db91769279b436e87ef80e3852d8f32", "47e6fb44301c4379a9c92377e8b1c08c", "1d4d163aa0e04ff29848a2babf459b8e", "8c13b23dd74f41069127594cf6fd23b0", "2cdf65a289d04702af1d137039f635db", "fbfa7ddf1cff48f19f3bc4bc80e2d145", "ff5a4e29c48f4442af2978ed764b5d11", "309f4c845b984e23ae4a90400ca5cb57", "52d501330f524d38adf3310e80d01ca4", "93f385bc4c12410c90d434308fcfdb5c"]}, "executionInfo": {"elapsed": 254, "status": "ok", "timestamp": 1648776383144, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "wspBidyabN5N", "outputId": "cee56a53-37ca-46d7-b222-3173512b8400"}, "outputs": [], "source": ["asr_model.transcribe(audio = es_files) [0]"]}, {"cell_type": "markdown", "metadata": {"id": "TLGSLdzDcHoV"}, "source": ["We see that audio samples from both languages are transcribed well."]}, {"cell_type": "markdown", "metadata": {"id": "KA3OKDBAct_0"}, "source": ["# Creating a new multilingual ASR model checkpoint from a monolingual one\n", "\n", "Training - or finetuning - a multilingual ASR model is no different to how you would do that with a monolingual model. The only difference is that you need to specify the language (id) for each sample in the manifest, e.g.\n", "```\n", "{\"audio_filepath\": \"/data/datasets/LibriSpeech/dev-clean-processed/422-122949-0002.wav\", \"duration\": 4.475, \"text\": \"we truthful ones the nobility in ancient greece called themselves\", \"lang\": \"en\"}\n", "```\n", "These language ids need to correspond to the language ids in your model's tokenizer config that we saw above, e.g.\n", "\n", "```\n", "tokenizer:\n", "  type:agg\n", "  langs:\n", "    en:\n", "      type: bpe\n", "      dir: en_tokenizer_dir\n", "    es:\n", "      type: bpe\n", "      dir: es_tokenizer_dir\n", "```\n", "As long as these are in place, training or fine tuning can proceed as usual.\n", "\n", "But, what if we are trying to create a brand new multilingual model and we want to start from a checkpoint created from a different language set?\n", "\n", "This works with all ASR models that use tokenization.\n", "\n", "In this notebook, we will use the small monolingual Conformer Transducer model checkpoint pre-trained on English:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 11589, "status": "ok", "timestamp": 1648776397759, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "rE84E-AmhRQF", "outputId": "101d3494-995e-4257-8f92-1fdd6b5f5dd4"}, "outputs": [], "source": ["asr_model = nemo_asr.models.EncDecRNNTBPEModel.from_pretrained(model_name=\"stt_en_conformer_transducer_small\")"]}, {"cell_type": "markdown", "metadata": {"id": "OaIZtWELTe44"}, "source": ["We will need to change the tokenizer for our loaded checkpoint to a multilingual one. You also could simply transfer an already trained tokenizer from another nemo model - every .nemo archive has the tokenizer (or more than one tokenizer) packaged inside.\n", "\n", "You also could train the tokenizer on any NLP ground truth - not necessarily ASR ground truth, potentially leading to better results.  \n", "\n", "But the *simplest* way of doing this would be to train a tokenizer on our datasets at hand.  Let's see how we can do that:  \n"]}, {"cell_type": "markdown", "metadata": {"id": "sIDFDQqBjnS7"}, "source": ["Let's download the tokenizer creation script:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 220, "status": "ok", "timestamp": 1648776398175, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "RMpefBZn1reO", "outputId": "c43e97a8-10ee-4292-9f99-9b7ab99a9213"}, "outputs": [], "source": ["if not os.path.exists(\"process_asr_text_tokenizer.py\"):\n", "  !wget  https://raw.githubusercontent.com/NVIDIA/NeMo/main/scripts/tokenizers/process_asr_text_tokenizer.py"]}, {"cell_type": "markdown", "metadata": {"id": "Klk6Bmmm4ZND"}, "source": ["Now let's create / train our tokenizers on our training manifests. You will notice later that we will train the model on the \"test\" manifests and validate it against the \"dev\" manifests - just to shorten training time. We will also train our Spanish tokenizer on the entire test manifest, not limiting it to the first 1500 lines."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 900, "status": "ok", "timestamp": 1648776399708, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "IQMgsA4r3Vnx"}, "outputs": [], "source": ["ENGLISH_TOKENIZER_DIR = \"tokenizers/en\"\n", "SPANISH_TOKENIZER_DIR = \"tokenizers/es\"\n", "!mkdir -p $ENGLISH_TOKENIZER_DIR\n", "!mkdir -p $SPANISH_TOKENIZER_DIR"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the entire Spanish test manifest to train our tokenizer to improve results. The more data is usually the better."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 10244, "status": "ok", "timestamp": 1648776412068, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "s5SRfudY3FH3", "outputId": "5a77ea16-ba0b-4005-f64c-cf59484baf22"}, "outputs": [], "source": ["!python process_asr_text_tokenizer.py \\\n", "  --manifest=\"commonvoice_train_manifest.json\" \\\n", "  --data_root=$SPANISH_TOKENIZER_DIR \\\n", "  --vocab_size=128 \\\n", "  --tokenizer=\"spe\" \\\n", "  --spe_type=bpe \\\n", "  --spe_character_coverage=1.0 \\\n", "  --log"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4531, "status": "ok", "timestamp": 1648776416595, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "WDtKNwLb5NCc", "outputId": "d37dee0d-0ee2-4e70-cebf-5e566a711af3"}, "outputs": [], "source": ["!python process_asr_text_tokenizer.py \\\n", "  --manifest=\"datasets/mini/train_clean_5.json\" \\\n", "  --data_root=$ENGLISH_TOKENIZER_DIR \\\n", "  --vocab_size=128 \\\n", "  --tokenizer=\"spe\" \\\n", "  --spe_type=bpe \\\n", "  --spe_character_coverage=1.0 \\\n", "  --log"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 669, "status": "ok", "timestamp": 1648776420179, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "DISSpZGm5qwd", "outputId": "d3d5b659-59ef-4f70-eefd-1c15110183ab"}, "outputs": [], "source": ["!ls $ENGLISH_TOKENIZER_DIR/tokenizer_spe_bpe_v128\n", "!ls $SPANISH_TOKENIZER_DIR/tokenizer_spe_bpe_v128"]}, {"cell_type": "markdown", "metadata": {"id": "I5B9g_l5-c9i"}, "source": ["All good. Let's get a Spanish tokenizer from a Spanish model"]}, {"cell_type": "markdown", "metadata": {"id": "wcR3ThUJ_Cxj"}, "source": ["Now we can create a config for our new aggregate Tokenizer."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 391, "status": "ok", "timestamp": 1648776423255, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "BCXjT2k-G9jx"}, "outputs": [], "source": ["from omegaconf import OmegaConf\n", "new_tokenizer_cfg = OmegaConf.create({'type': 'agg', 'langs': {}})\n", "english_tokenizer_cfg = OmegaConf.create({'dir': ENGLISH_TOKENIZER_DIR + '/tokenizer_spe_bpe_v128', 'type': 'bpe'})\n", "spanish_tokenizer_cfg = OmegaConf.create({'dir': SPANISH_TOKENIZER_DIR + '/tokenizer_spe_bpe_v128', 'type': 'bpe'})\n", "new_tokenizer_cfg.langs['en'] = english_tokenizer_cfg\n", "new_tokenizer_cfg.langs['es'] = spanish_tokenizer_cfg"]}, {"cell_type": "markdown", "metadata": {"id": "TrC04Kdo_Lzy"}, "source": ["And apply it to our loaded checkpoint.  Note that this completely replaces the decoder."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 826, "status": "ok", "timestamp": 1648776426269, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "CL68UJq_icuW", "outputId": "40d31aff-d164-450c-9202-0d86fd2a5ac6"}, "outputs": [], "source": ["asr_model.change_vocabulary(\n", "        new_tokenizer_dir=new_tokenizer_cfg,\n", "        new_tokenizer_type=\"agg\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You may notice that some of the tokens contain characters that are neither part of the English nor Spanish alphabet. This is because the official Mozilla Common Voice data for each language sometimes contains a few utterances from a different language due to some data processing errors. Ideally we would remove these other-language utterances before training. For the purposes of this tutorial, we have skipped this step."]}, {"cell_type": "markdown", "metadata": {"id": "SrimSD1EjulS"}, "source": ["The tokenizer has been changed, let's save our new checkpoint!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 1308, "status": "ok", "timestamp": 1648776428864, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "r3g1URYoj03N"}, "outputs": [], "source": ["asr_model.save_to(\"multi.nemo\")"]}, {"cell_type": "markdown", "metadata": {"id": "ZbQ5kGYdl3Dw"}, "source": ["Now, let's load this new checkpoint and make sure the tokenizer config looks ok:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2358, "status": "ok", "timestamp": 1648776432349, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "J1fcl41glj6F", "outputId": "1d7b9869-29b3-446a-8225-35cd04d84c09"}, "outputs": [], "source": ["asr_model = nemo_asr.models.EncDecRNNTBPEModel.restore_from(restore_path=\"multi.nemo\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1648776433074, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "HXq2CRYBlsqc", "outputId": "4bdc98ac-6f37-460d-e024-34055523d2a0"}, "outputs": [], "source": ["print(asr_model.cfg.tokenizer)\n", "print(asr_model.tokenizer.langs)"]}, {"cell_type": "markdown", "metadata": {"id": "AltE_vEzmD0a"}, "source": ["This new checkpoint has encoder weights from the English model, but the decoder has been reinitialized. Let's see how well it does on English speech:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["79e283235b5f4fd49c100800a91d2861", "61a7db49408b4c499e19a38b6f4e613e", "277c5c042efe4ff7b490312a1907b3dd", "e421dabbe0ad4acb9d3110c20378d83f", "ab806646980a476f9932eb8e64c2436a", "8b610e07536d4b2f8637105e9084505a", "44c2050dbc1247f8bc2e1b8a4ccacdf9", "5b6733e0e1a541bc986108ecb14c768a", "3ab12cae61f04ea19699398092b5a442", "d0189dd2da4d44dea98c12eccb42d345", "0c60a47ed2ae4bb4a9f44df199b2d48f"]}, "executionInfo": {"elapsed": 2371, "status": "ok", "timestamp": 1648776437926, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "8S2GczoimDBg", "outputId": "e56c272c-c1f2-4311-9be5-b4a4087a3046"}, "outputs": [], "source": ["asr_model.transcribe(audio = en_files)[0]"]}, {"cell_type": "markdown", "metadata": {"id": "3vmoGxZAmjU_"}, "source": ["How about Spanish?"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["a92d74a3ec5e478385e98b6c59f29dd7", "e120f52c2b30479a97b6e413e954aa0c", "4a4036a89a804e228e9b96f8355c8587", "4fd184009f544e82b65e94645e60ae1d", "5a21fc37b45f49698c1f0ad9ccd76234", "46c962632f2d414d986470039dcce0c6", "faf0f1ee88e44249adc2e287371e68ce", "c3dfbc759e3f4bc591e249d64959beb7", "572657e5cef94caf88bc5248f87cc3c3", "b43d51335a634ff385a2d690d91c797b", "5c568f31f6f1444f87013aec8c7037ac"]}, "executionInfo": {"elapsed": 1149, "status": "ok", "timestamp": 1648776439291, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "T882odxtmlRO", "outputId": "e73991d5-494a-40b5-c4a3-3fbb284f15a4"}, "outputs": [], "source": ["asr_model.transcribe(audio = es_files)[0]"]}, {"cell_type": "markdown", "metadata": {"id": "BPVsG7jAmpUA"}, "source": ["That didn't work. The decoder + joint weights are now larger, and they don't correspond to the original weights. Also, the RNNT blank token is now shared between the two languages, not duplicated at the end of each languages range. The model needs to be fine-tuned before we can use it."]}, {"cell_type": "markdown", "metadata": {"id": "F49I_1MO4kje"}, "source": ["## Creating training / validation sets\n", "We previously downloaded an English and Spanish dataset, but we will need to make adjustments in order to use them for training and eval."]}, {"cell_type": "markdown", "metadata": {"id": "f9c8rUJm6S1r"}, "source": ["Let us take a look at the preprocessed mini librispeech manifest:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 206, "status": "ok", "timestamp": 1648776443626, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "76bxoKpI5nUD"}, "outputs": [], "source": ["train_manifest_en_in = \"datasets/mini/train_clean_5.json\"\n", "val_manifest_en_in = \"datasets/mini/dev_clean_2.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 724, "status": "ok", "timestamp": 1648776446545, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "KuKXK-1n5qh6", "outputId": "93aa6144-056d-4103-d9c6-a7ea0f3a3e2a"}, "outputs": [], "source": ["!head -5 $train_manifest_en_in"]}, {"cell_type": "markdown", "metadata": {"id": "hTrvk40_6jgW"}, "source": ["As described above, multilingual models require the language id field for each sample. This manifest is all english, so it's easy to add the lang: "]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 204, "status": "ok", "timestamp": 1648776448243, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "fNMreApz61tB"}, "outputs": [], "source": ["train_manifest_en = \"datasets/mini/train_clean_5_en.json\"\n", "val_manifest_en = \"datasets/mini/dev_clean_2_en.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1648776449229, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "8AL1VXcv_0Fu"}, "outputs": [], "source": ["import json\n", "def add_lang(in_manifest_file, out_manifest_file, lang):\n", "    with open(in_manifest_file) as in_file:\n", "        with open(out_manifest_file, 'w') as out_file:\n", "            for line in in_file:\n", "                o = json.loads(line)\n", "                o['lang'] = lang\n", "                s = json.dumps(o)\n", "                out_file.write(s + '\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 202, "status": "ok", "timestamp": 1648776451235, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "CAKZR4CEEIx2"}, "outputs": [], "source": ["add_lang(train_manifest_en_in, train_manifest_en, 'en')\n", "add_lang(val_manifest_en_in, val_manifest_en, 'en')"]}, {"cell_type": "markdown", "metadata": {"id": "v8EOOhy0E5So"}, "source": ["Same needs to be done to Spanish manifests. Note that we will use the entire Spanish test manifest for training in order to improve results - even though this creates an imbalanced training set. You can try to use the smaller (1500) manifest too for quicker training, but the results will not be as good. We can still keep the Spanish validation set small."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 514, "status": "ok", "timestamp": 1648776453777, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "GxAuCaJkEV_h"}, "outputs": [], "source": ["train_manifest_es_in = \"commonvoice_train_manifest.json\"\n", "val_manifest_es_in = \"commonvoice_dev_manifest_1000.json\"\n", "train_manifest_es = \"commonvoice_train_manifest_es.json\"\n", "val_manifest_es = \"commonvoice_dev_manifest_1000_es.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 526, "status": "ok", "timestamp": 1648776454779, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "uum4VPiYEu_F"}, "outputs": [], "source": ["add_lang(train_manifest_es_in, train_manifest_es, 'es')\n", "add_lang(val_manifest_es_in, val_manifest_es, 'es')"]}, {"cell_type": "markdown", "metadata": {"id": "kgFe5bIqACOJ"}, "source": ["Let's check that the lang fields were added:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 476, "status": "ok", "timestamp": 1648776456688, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "GazCYyGN_4ZH", "outputId": "5eb8d662-b6e8-4c99-cd24-85e7ca32ed8a"}, "outputs": [], "source": ["!head -3 $train_manifest_en"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 697, "status": "ok", "timestamp": 1648776457652, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "rjCxI8g3FBbZ", "outputId": "fb1ebe70-5d5c-41df-e445-cda34ebdd94f"}, "outputs": [], "source": ["!head -3 $train_manifest_es"]}, {"cell_type": "markdown", "metadata": {"id": "To0bBZOmSKZ6"}, "source": ["Great! Let us initialize the PyTorch Lightning trainer. Note that we are using native 16-bit precision. Please change this to 32 bit if your model shows any signs of divergence."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 702, "status": "ok", "timestamp": 1648776468425, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "HidMfcldAfBf", "outputId": "ed37b4cb-fdf0-46c1-c3be-5ad855bebdf0"}, "outputs": [], "source": ["import torch\n", "import lightning.pytorch as ptl"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Initializing the trainer\n", "We will run for just 5 epochs to save time, even though you will likely see better results at about 10-15. You may want to increase gradient accumulation if the loss oscillates too much.  If your machine has more than one GPU, add them to the GPUs list. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 702, "status": "ok", "timestamp": 1648776468425, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "HidMfcldAfBf", "outputId": "ed37b4cb-fdf0-46c1-c3be-5ad855bebdf0"}, "outputs": [], "source": ["GRAD_ACCUM=1\n", "MAX_EPOCHS=5\n", "GPUS=[0]\n", "LOG_EVERY_N_STEPS=10\n", "\n", "trainer = ptl.Trainer(devices=GPUS, \n", "                      accelerator=\"gpu\",\n", "                      max_epochs=MAX_EPOCHS, \n", "                      accumulate_grad_batches=GRAD_ACCUM,\n", "                      precision=16,\n", "                      enable_checkpointing=False,\n", "                      logger=False,\n", "                      log_every_n_steps=LOG_EVERY_N_STEPS,\n", "                      enable_progress_bar=True,\n", "                      check_val_every_n_epoch=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 702, "status": "ok", "timestamp": 1648776468425, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "HidMfcldAfBf", "outputId": "ed37b4cb-fdf0-46c1-c3be-5ad855bebdf0"}, "outputs": [], "source": ["asr_model.set_trainer(trainer)"]}, {"cell_type": "markdown", "metadata": {"id": "dIwzcmsRSdO0"}, "source": ["Now the training dataloader. Observe that we will be training on a mix of spanish and english"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 237, "status": "ok", "timestamp": 1648776472482, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "5p2GUoCIBo2v"}, "outputs": [], "source": ["train_ds = {}\n", "train_ds['manifest_filepath'] = [train_manifest_en,train_manifest_es]\n", "train_ds['sample_rate'] = 16000\n", "train_ds['batch_size'] = 16\n", "train_ds['fused_batch_size'] = 16\n", "train_ds['shuffle'] = True\n", "train_ds['max_duration'] = 16.7\n", "train_ds['pin_memory'] = True\n", "train_ds['is_tarred'] = False\n", "train_ds['num_workers'] = 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 780, "status": "ok", "timestamp": 1648776479861, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "BPrK7MvvFDIh", "outputId": "5fa78385-5f76-4b2b-f2fc-241f193cd871"}, "outputs": [], "source": ["asr_model.setup_training_data(train_data_config=train_ds)  "]}, {"cell_type": "markdown", "metadata": {"id": "N3p46a8VSnH2"}, "source": ["For the validation dataset, we follow a similar process. The batch size for validation can be higher since memory pressure is lower."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 1, "status": "ok", "timestamp": 1648776481223, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "tdTa46atCnpr"}, "outputs": [], "source": ["validation_ds = {}\n", "validation_ds['sample_rate'] = 16000\n", "validation_ds['manifest_filepath'] = [val_manifest_en,val_manifest_es]\n", "validation_ds['batch_size'] = 32\n", "validation_ds['shuffle'] = False\n", "validation_ds['num_workers'] = 4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the `setup_multiple_validation_data` call to see validation numbers for English and Spanish datasets separately"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 467, "status": "ok", "timestamp": 1648776483724, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "QrfDhIenFE9B", "outputId": "ff3eb8d7-b4e8-4cdf-8868-0ceb6acaaca1"}, "outputs": [], "source": ["asr_model.setup_multiple_validation_data(val_data_config=validation_ds)"]}, {"cell_type": "markdown", "metadata": {"id": "n06REaHQhvf-"}, "source": ["Now, let us tweak the model optimizer parameters. The model was pre-trained with a higher learning rate; we may want to lower it a little for fine tuning."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 492, "status": "ok", "timestamp": 1648776539343, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "XWfQhGeBh7SF"}, "outputs": [], "source": ["optimizer_conf = {}\n", "\n", "optimizer_conf['name'] = 'adamw'\n", "optimizer_conf['lr'] = 0.01\n", "optimizer_conf['betas'] =  [0.9, 0.98]\n", "optimizer_conf['weight_decay'] = 0\n", "\n", "sched = {}\n", "sched['name'] = 'CosineAnnealing'\n", "sched['warmup_steps'] = None\n", "sched['warmup_ratio'] = 0.10\n", "sched['min_lr'] = 1e-6\n", "optimizer_conf['sched'] = sched"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asr_model.setup_optimization(optimizer_conf)"]}, {"cell_type": "markdown", "metadata": {"id": "DQpvI8IliD66"}, "source": ["Let us freeze the encoder for easier initial convergence and faster training. On a smaller dataset when retraining the decoder, this is often a good idea.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 376, "status": "ok", "timestamp": 1648776545202, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "_H5P2u8FJ4M8"}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "def enable_bn_se(m):\n", "    if type(m) == nn.BatchNorm1d:\n", "        m.train()\n", "        for param in m.parameters():\n", "            param.requires_grad_(True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 472, "status": "ok", "timestamp": 1648776546895, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "pgdrz1kNJ5A8", "outputId": "6f8485b0-5db9-4e68-b67f-40b67231b286"}, "outputs": [], "source": ["asr_model.encoder.freeze()\n", "asr_model.encoder.apply(enable_bn_se)"]}, {"cell_type": "markdown", "metadata": {"nteract": {"transient": {"deleting": false}}}, "source": ["Let is enable the logging of the validation loss and suppress the printing of log predictions during training, in order to not pollute the Jupiter notebook output"]}, {"cell_type": "code", "execution_count": null, "metadata": {"nteract": {"transient": {"deleting": false}}}, "outputs": [], "source": ["asr_model.wer.log_predictions = False\n", "# set to True if you would like to track the evaluation loss\n", "asr_model.compute_eval_loss = False"]}, {"cell_type": "markdown", "metadata": {"id": "YIjvGFy6kzPy"}, "source": ["Finally, let's set up an experiment manager to keep track of our checkpoints.  Also, we want to use W&B for experiment management!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 239, "status": "ok", "timestamp": *************, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "VDlwk1D1lBo_"}, "outputs": [], "source": ["import wandb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since we will use Weights and Biases to observe our run, you will need an account.  You will also need to rename the project name and experiment name."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 239, "status": "ok", "timestamp": *************, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "VDlwk1D1lBo_"}, "outputs": [], "source": ["WANDB_PROJ_NAME = \"multilang_asr\"\n", "WANDB_EXP_NAME = \"tutorial\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "executionInfo": {"elapsed": 8515, "status": "ok", "timestamp": *************, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "mBSDWAtwli0e", "outputId": "71d27e5b-7c33-4e37-cc51-492153f33385"}, "outputs": [], "source": ["# enter your W&B key when prompted\n", "wandb_logged_in = wandb.login(relogin=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Nemo experiment manager will interface with W&B as well as save your intermediate checkpoints."]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 242, "status": "ok", "timestamp": 1648776656285, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "RNSmapeTlF6s"}, "outputs": [], "source": ["from nemo.utils import exp_manager\n", "import os\n", "\n", "config = exp_manager.ExpManagerConfig(\n", "    exp_dir=f'experiments/multi/',\n", "    name=f\"ASR-Model-multi\",\n", "    checkpoint_callback_params=exp_manager.CallbackParams(\n", "        monitor=\"val_wer\",\n", "        mode=\"min\",\n", "        always_save_nemo=True,\n", "        save_best_model=True,\n", "    ),\n", "    create_wandb_logger=wandb_logged_in,\n", "    wandb_logger_kwargs = {\"name\": WANDB_EXP_NAME, \"project\": WANDB_PROJ_NAME}\n", ")\n", "\n", "config = OmegaConf.structured(config)\n", "\n", "logdir = exp_manager.exp_manager(trainer, config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training\n", "Now we start our run with a frozen encoder."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["6453caf9c1404659bb898d8e25a21f3e", "e8d079f77cc7494ba90dcda7e57db66e", "4fb0e6a15ef941d489f2d65c5e93a8bf", "c324d503d5fe4765850fa41a66bef2e8", "322fc5abfaf347dea3e3a20a02a05cca", "a797bf05c0644bfab8115075b4c1dc31", "2681c47b4ef140b78aa76cd2fe053d98", "33cf9cdc03e54242849b1426165c0664", "03e4934b30b448fbaa7b9570cff2a6f5", "fa39c664adf548008af200a0270e08f3", "cfb6b1008acd4e22aba17bd6241c8d4c", "ed63208ad78c4235830ba0781ba36e10", "c199bb8f20b641aba9b4d2a8e484d72a", "c6dab436efd244b79d9988181c4c90f9", "babc2434df464ed0a61eb4da75273be2", "3641921a088846b1a7bbc9fe42c7e9a0", "7afd32af8fd949ffba8e600bf43a4c25", "395292edc81b437aa3c521a68a81dab2", "5f4ecc27cafe4ec890a16c992fea9926", "c6cc8e310f704ab8beed94ebaf05d36c", "1c8938004b774ba0907e931ea5c73762", "e6bd26fc78d149af820bd3f3d623f11c"]}, "id": "b7jf3CdjFMAh", "outputId": "28657778-7e00-4a32-b6d3-c287ce20db75"}, "outputs": [], "source": ["trainer.fit(asr_model)"]}, {"cell_type": "markdown", "metadata": {"id": "otAbsYepBH2O"}, "source": ["Let us now test our trained model on the English validation set to measure the WER:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "J6MBqBfejJb-"}, "outputs": [], "source": ["test_ds_en = {}\n", "test_ds_en['sample_rate'] = 16000\n", "test_ds_en['manifest_filepath'] = [val_manifest_en]\n", "test_ds_en['batch_size'] = 32\n", "test_ds_en['num_workers'] = 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8K_6XmDKAM6T"}, "outputs": [], "source": ["asr_model.setup_test_data(test_data_config=test_ds_en)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["fc0626a546a542a899d1f8e50c8cadbd", "68a44646a5444b37a8a6f7b5c4c50be2", "dd33d1be6c3648f0b1b0558bbc7376c1", "11b3a77abbab4e7a8e637b3e23e882a7", "8cbc7b792c274a5399d9b6f699e08d21", "bb282a10679f4c24954864e3f05232b4", "bf0ac5f3c56240dca3d7b1186bb69760", "206895a2dd534353acd3ce5c8848668e", "e823f6ae144c4cb0ab225d10a1668520", "********************************", "acac011b7dc54da7a2b6097aa45426ce"]}, "executionInfo": {"elapsed": 96455, "status": "ok", "timestamp": 1648772300548, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "WQ5pdNmBAVa1", "outputId": "f9541390-2cdb-40a9-834e-eb2b64e8322a"}, "outputs": [], "source": ["trainer.test(asr_model)"]}, {"cell_type": "markdown", "metadata": {"id": "zqxL4X3uBNWV"}, "source": ["We should get a WER of 15 - 20%.\n", "\n", "How about Spanish?"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 123, "status": "ok", "timestamp": 1648772312279, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "krPLVTEFBMpB"}, "outputs": [], "source": ["test_ds_es = {}\n", "test_ds_es['sample_rate'] = 16000\n", "test_ds_es['manifest_filepath'] = [val_manifest_es]\n", "test_ds_es['batch_size'] = 32\n", "test_ds_es['num_workers'] = 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 126, "status": "ok", "timestamp": 1648772316871, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "C6yvTQgPBXWL", "outputId": "04d9f275-a3e7-4189-9842-2d3d7894adfa"}, "outputs": [], "source": ["asr_model.setup_test_data(test_data_config=test_ds_es)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["d661a894b22646bb9f990cc9d6da481a", "5ac2a46594314b71ab5d0d002ad15a13", "3b31eb197363425dbc8ba7afe8bdd8c2", "34b6767f2e3e480d9af9d5bc57dcd9a6", "********************************", "97c23c0623cd4b5dafa23576fd8ad24d", "8a804c121cdd453c8117b6705e0ec772", "c1e77cd51e84433a9e5335d3dc782af7", "46e272d4d0b84e40b8625eb19f195c58", "********************************", "adae19d1995143c4a5fe0c83d5b8d726"]}, "executionInfo": {"elapsed": 51927, "status": "ok", "timestamp": 1648772370478, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "MLL4Uyc9BaOZ", "outputId": "28e6966d-dfa2-45ff-95d0-2edefb8d4119"}, "outputs": [], "source": ["trainer.test(asr_model)"]}, {"cell_type": "markdown", "metadata": {"id": "DRHgO3_BftZv"}, "source": ["Spanish WER is considerably worse; around 60 - 65% . But, consider that we got this number despite the fact that we are still using a frozen encoder that only saw English speech!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 760, "status": "ok", "timestamp": 1648772434632, "user": {"displayName": "<PERSON><PERSON>", "userId": "02120478255215436047"}, "user_tz": 420}, "id": "nXI3QMbcLOiN"}, "outputs": [], "source": ["asr_model.save_to(\"multi_trained.nemo\")"]}, {"cell_type": "markdown", "metadata": {"id": "1SX3C9zhjNwc"}, "source": ["# Next steps\n", "In order to achieve better results, try continuing to fine tune from the saved checkpoint using an unfrozen encoder.  You will achieve a much better result if you download full Librispeech english\n", "and Mozilla Commonvoice Spanish datasets and finetune on them - you will not need to freeze the encoder due to the larger dataset size."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyP7viKmKWOfvu1jqB3qy/gX", "collapsed_sections": [], "mount_file_id": "1m43MIesMguFaXWXbS1qPgnCEf2J4BVim", "name": "multilang_asr.ipynb", "provenance": []}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}, "nteract": {"version": "0.28.0"}, "vscode": {"interpreter": {"hash": "1aaa02ce0ce2638a6e16a203f0ce39bc7495f7236d7115882d2d3541e1318e7a"}}, "widgets": {"application/vnd.jupyter.widget-state+json": {"013abc9bfddf456abf15dc2b0567d969": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f1ca1411499d4fd6a43c0a94884ab5c3", "placeholder": "​", "style": "IPY_MODEL_********************************", "value": " 1/1 [00:01&lt;00:00,  1.14s/it]"}}, "03e4934b30b448fbaa7b9570cff2a6f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0c60a47ed2ae4bb4a9f44df199b2d48f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "11b3a77abbab4e7a8e637b3e23e882a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_********************************", "placeholder": "​", "style": "IPY_MODEL_acac011b7dc54da7a2b6097aa45426ce", "value": " 35/35 [01:36&lt;00:00,  2.31s/it]"}}, "1c8938004b774ba0907e931ea5c73762": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d4d163aa0e04ff29848a2babf459b8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_52d501330f524d38adf3310e80d01ca4", "placeholder": "​", "style": "IPY_MODEL_93f385bc4c12410c90d434308fcfdb5c", "value": " 1/1 [00:00&lt;00:00,  2.30it/s]"}}, "206895a2dd534353acd3ce5c8848668e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2681c47b4ef140b78aa76cd2fe053d98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "277c5c042efe4ff7b490312a1907b3dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5b6733e0e1a541bc986108ecb14c768a", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3ab12cae61f04ea19699398092b5a442", "value": 1}}, "2b448e3e56534d08a721702f3ae37505": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2cdf65a289d04702af1d137039f635db": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2fb8b201a2064e008d643e51f5125608": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "309f4c845b984e23ae4a90400ca5cb57": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "322fc5abfaf347dea3e3a20a02a05cca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "33cf9cdc03e54242849b1426165c0664": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "34b6767f2e3e480d9af9d5bc57dcd9a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_********************************", "placeholder": "​", "style": "IPY_MODEL_adae19d1995143c4a5fe0c83d5b8d726", "value": " 32/32 [00:51&lt;00:00,  1.28s/it]"}}, "3641921a088846b1a7bbc9fe42c7e9a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "395292edc81b437aa3c521a68a81dab2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3ab12cae61f04ea19699398092b5a442": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3b31eb197363425dbc8ba7afe8bdd8c2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c1e77cd51e84433a9e5335d3dc782af7", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_46e272d4d0b84e40b8625eb19f195c58", "value": 1}}, "44c2050dbc1247f8bc2e1b8a4ccacdf9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "46c962632f2d414d986470039dcce0c6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "46e272d4d0b84e40b8625eb19f195c58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "47e6fb44301c4379a9c92377e8b1c08c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ff5a4e29c48f4442af2978ed764b5d11", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_309f4c845b984e23ae4a90400ca5cb57", "value": 1}}, "4a4036a89a804e228e9b96f8355c8587": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3dfbc759e3f4bc591e249d64959beb7", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_572657e5cef94caf88bc5248f87cc3c3", "value": 1}}, "4fb0e6a15ef941d489f2d65c5e93a8bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33cf9cdc03e54242849b1426165c0664", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_03e4934b30b448fbaa7b9570cff2a6f5", "value": 2}}, "4fd184009f544e82b65e94645e60ae1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b43d51335a634ff385a2d690d91c797b", "placeholder": "​", "style": "IPY_MODEL_5c568f31f6f1444f87013aec8c7037ac", "value": " 1/1 [00:01&lt;00:00,  1.02s/it]"}}, "52d501330f524d38adf3310e80d01ca4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5476c1db05024cd7ab38670d61f1951b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "572657e5cef94caf88bc5248f87cc3c3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5a21fc37b45f49698c1f0ad9ccd76234": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5ac2a46594314b71ab5d0d002ad15a13": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_97c23c0623cd4b5dafa23576fd8ad24d", "placeholder": "​", "style": "IPY_MODEL_8a804c121cdd453c8117b6705e0ec772", "value": "Testing: 100%"}}, "5b6733e0e1a541bc986108ecb14c768a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c568f31f6f1444f87013aec8c7037ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5f4ecc27cafe4ec890a16c992fea9926": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61a7db49408b4c499e19a38b6f4e613e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8b610e07536d4b2f8637105e9084505a", "placeholder": "​", "style": "IPY_MODEL_44c2050dbc1247f8bc2e1b8a4ccacdf9", "value": "Transcribing: 100%"}}, "********************************": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "6453caf9c1404659bb898d8e25a21f3e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e8d079f77cc7494ba90dcda7e57db66e", "IPY_MODEL_4fb0e6a15ef941d489f2d65c5e93a8bf", "IPY_MODEL_c324d503d5fe4765850fa41a66bef2e8"], "layout": "IPY_MODEL_322fc5abfaf347dea3e3a20a02a05cca"}}, "68a44646a5444b37a8a6f7b5c4c50be2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb282a10679f4c24954864e3f05232b4", "placeholder": "​", "style": "IPY_MODEL_bf0ac5f3c56240dca3d7b1186bb69760", "value": "Testing: 100%"}}, "79e283235b5f4fd49c100800a91d2861": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_61a7db49408b4c499e19a38b6f4e613e", "IPY_MODEL_277c5c042efe4ff7b490312a1907b3dd", "IPY_MODEL_e421dabbe0ad4acb9d3110c20378d83f"], "layout": "IPY_MODEL_ab806646980a476f9932eb8e64c2436a"}}, "7afd32af8fd949ffba8e600bf43a4c25": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8a804c121cdd453c8117b6705e0ec772": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8b610e07536d4b2f8637105e9084505a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c13b23dd74f41069127594cf6fd23b0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8cbc7b792c274a5399d9b6f699e08d21": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "93f385bc4c12410c90d434308fcfdb5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "97c23c0623cd4b5dafa23576fd8ad24d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9be44dd1d7c44a66b54a1af08255b86f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9db91769279b436e87ef80e3852d8f32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2cdf65a289d04702af1d137039f635db", "placeholder": "​", "style": "IPY_MODEL_fbfa7ddf1cff48f19f3bc4bc80e2d145", "value": "Transcribing: 100%"}}, "********************************": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f7966ff979248bcafaf389f389d7e52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2b448e3e56534d08a721702f3ae37505", "placeholder": "​", "style": "IPY_MODEL_c29036af3e4945739a58e372315a44f4", "value": "Transcribing: 100%"}}, "********************************": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a797bf05c0644bfab8115075b4c1dc31": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a92d74a3ec5e478385e98b6c59f29dd7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e120f52c2b30479a97b6e413e954aa0c", "IPY_MODEL_4a4036a89a804e228e9b96f8355c8587", "IPY_MODEL_4fd184009f544e82b65e94645e60ae1d"], "layout": "IPY_MODEL_5a21fc37b45f49698c1f0ad9ccd76234"}}, "ab806646980a476f9932eb8e64c2436a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "acac011b7dc54da7a2b6097aa45426ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "adae19d1995143c4a5fe0c83d5b8d726": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b43d51335a634ff385a2d690d91c797b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "babc2434df464ed0a61eb4da75273be2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1c8938004b774ba0907e931ea5c73762", "placeholder": "​", "style": "IPY_MODEL_e6bd26fc78d149af820bd3f3d623f11c", "value": " 173/255 [08:05&lt;03:50,  2.81s/it, loss=75.8]"}}, "bb282a10679f4c24954864e3f05232b4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf0ac5f3c56240dca3d7b1186bb69760": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c199bb8f20b641aba9b4d2a8e484d72a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7afd32af8fd949ffba8e600bf43a4c25", "placeholder": "​", "style": "IPY_MODEL_395292edc81b437aa3c521a68a81dab2", "value": "Epoch 4:  68%"}}, "c1e77cd51e84433a9e5335d3dc782af7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c29036af3e4945739a58e372315a44f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c324d503d5fe4765850fa41a66bef2e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fa39c664adf548008af200a0270e08f3", "placeholder": "​", "style": "IPY_MODEL_cfb6b1008acd4e22aba17bd6241c8d4c", "value": " 2/2 [00:29&lt;00:00, 14.39s/it]"}}, "c3dfbc759e3f4bc591e249d64959beb7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6cc8e310f704ab8beed94ebaf05d36c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c6dab436efd244b79d9988181c4c90f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f4ecc27cafe4ec890a16c992fea9926", "max": 255, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c6cc8e310f704ab8beed94ebaf05d36c", "value": 173}}, "cbef296aea8746c38c02012e1e06a402": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9be44dd1d7c44a66b54a1af08255b86f", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5476c1db05024cd7ab38670d61f1951b", "value": 1}}, "cfb6b1008acd4e22aba17bd6241c8d4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d0189dd2da4d44dea98c12eccb42d345": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3e926edb0b24e86ae84f8747a643643": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9db91769279b436e87ef80e3852d8f32", "IPY_MODEL_47e6fb44301c4379a9c92377e8b1c08c", "IPY_MODEL_1d4d163aa0e04ff29848a2babf459b8e"], "layout": "IPY_MODEL_8c13b23dd74f41069127594cf6fd23b0"}}, "d661a894b22646bb9f990cc9d6da481a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5ac2a46594314b71ab5d0d002ad15a13", "IPY_MODEL_3b31eb197363425dbc8ba7afe8bdd8c2", "IPY_MODEL_34b6767f2e3e480d9af9d5bc57dcd9a6"], "layout": "IPY_MODEL_********************************"}}, "dd33d1be6c3648f0b1b0558bbc7376c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_206895a2dd534353acd3ce5c8848668e", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e823f6ae144c4cb0ab225d10a1668520", "value": 1}}, "e120f52c2b30479a97b6e413e954aa0c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_46c962632f2d414d986470039dcce0c6", "placeholder": "​", "style": "IPY_MODEL_faf0f1ee88e44249adc2e287371e68ce", "value": "Transcribing: 100%"}}, "********************************": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e421dabbe0ad4acb9d3110c20378d83f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d0189dd2da4d44dea98c12eccb42d345", "placeholder": "​", "style": "IPY_MODEL_0c60a47ed2ae4bb4a9f44df199b2d48f", "value": " 1/1 [00:02&lt;00:00,  2.13s/it]"}}, "e6bd26fc78d149af820bd3f3d623f11c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e823f6ae144c4cb0ab225d10a1668520": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e8d079f77cc7494ba90dcda7e57db66e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a797bf05c0644bfab8115075b4c1dc31", "placeholder": "​", "style": "IPY_MODEL_2681c47b4ef140b78aa76cd2fe053d98", "value": "Validation sanity check: 100%"}}, "ed63208ad78c4235830ba0781ba36e10": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c199bb8f20b641aba9b4d2a8e484d72a", "IPY_MODEL_c6dab436efd244b79d9988181c4c90f9", "IPY_MODEL_babc2434df464ed0a61eb4da75273be2"], "layout": "IPY_MODEL_3641921a088846b1a7bbc9fe42c7e9a0"}}, "f1ca1411499d4fd6a43c0a94884ab5c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa39c664adf548008af200a0270e08f3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "faf0f1ee88e44249adc2e287371e68ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fbfa7ddf1cff48f19f3bc4bc80e2d145": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fc0626a546a542a899d1f8e50c8cadbd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_68a44646a5444b37a8a6f7b5c4c50be2", "IPY_MODEL_dd33d1be6c3648f0b1b0558bbc7376c1", "IPY_MODEL_11b3a77abbab4e7a8e637b3e23e882a7"], "layout": "IPY_MODEL_8cbc7b792c274a5399d9b6f699e08d21"}}, "fe775031e7b7423e8e581d44bac68e5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9f7966ff979248bcafaf389f389d7e52", "IPY_MODEL_cbef296aea8746c38c02012e1e06a402", "IPY_MODEL_013abc9bfddf456abf15dc2b0567d969"], "layout": "IPY_MODEL_2fb8b201a2064e008d643e51f5125608"}}, "ff5a4e29c48f4442af2978ed764b5d11": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 4}