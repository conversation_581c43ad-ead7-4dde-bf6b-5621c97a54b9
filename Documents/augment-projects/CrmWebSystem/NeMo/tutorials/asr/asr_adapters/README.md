# Speech Recognition Tutorials With Adapters
------------

In this repository, you will find several tutorials discussing how to utilize Adapter modules for Automatic Speech Recognition (ASR). We also discuss the general concepts and specific ways to apply Adapters for multiple sub-domains of ASR such as domain adaptation.


------------

# Automatic Speech Recognition

1) `ASR_with_Adapters`: An introduction of adapters and their use case with ASR models. Dives into domain adaptation of a pre-trained model with adapter modules, general advantages and disadvantages of adapters and finally trains a model to adapt on a toy dataset.

2) `Multi_Task_Adapters`: An introduction of how to customize multi-task models with adapters. We will train a model on two tasks, one being ASR and the other being a downstream task. We will discuss how to use adapters to finetune a model for Speech Recognition and Speech Translation task on a toy dataset, and dive into construction of custom datasets and prompt formatters for Multi Task Models.

------------