{"cells": [{"cell_type": "markdown", "id": "cd13460c", "metadata": {}, "source": ["# Domain Adaptive Pre-Training (DAPT)\n", "\n", "## Goal\n", "\n", "Given a foundational language model (in this case llama-2-7B) that was pre-trained on a broad, general-purpose corpus, our goal is to further pretrain the model on a specific domain (in this example, ChipDesign) to enhance its understanding of domain-specific language and context. This process is called Domain-Adaptive Pretraining (DAPT). DAPT adapts a general-purpose model to specialized tasks within a particular field. Instead of training from scratch, we aim to “specialize” the model by focusing on a target domain corpus, allowing it to adapt to the unique vocabulary, semantics, and syntax of that field.\n", "\n", "Our primary goals with respect to DAPT are as follows:\n", "* Improve the model’s performance and accuracy on domain-specific tasks\n", "* Ensure the model retains general language capabilities\n", "* Minimize pretraining time by leveraging existing knowledge in the model\n", "\n", "DAPT typically enhances a model’s efficacy in downstream tasks for the domain by exposing it to domain-relevant texts. This pretraining phase can result in more accurate and context-aware predictions on domain-specific data, as the model gains an understanding of field-specific terminology, abbreviations, and common phrases."]}, {"cell_type": "markdown", "id": "c43ef563", "metadata": {}, "source": ["# NeMo Tools and Resources\n", "\n", "* [NeMo Framework](https://docs.nvidia.com/nemo-framework/user-guide/latest/overview.html)"]}, {"cell_type": "markdown", "id": "bea0b51f", "metadata": {}, "source": ["# Software Requirements\n", "* Access to latest NeMo Framework NGC Containers\n", "* This playbook has been tested on: nvcr.io/nvidia/nemo:dev. It is expected to work similarly on other environments.\n", "\n", "\n", "#### Launch the NeMo Framework container as follows: \n", "\n", "```\n", "docker run -it -p 8080:8080 -p 8088:8088 --rm --gpus '\"device=0,1\"' --ipc=host --network host -v $(pwd):/workspace nvcr.io/nvidia/nemo:dev\n", "```\n", "\n", "#### Launch Jupyter Notebook as follows: \n", "```\n", "jupyter notebook --allow-root --ip 0.0.0.0 --port 8088 --no-browser --NotebookApp.token=''\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "7137e1db", "metadata": {}, "source": ["# Hardware Requirements\n", "\n", "* This playbook has been tested on 2xA100 80G but can be scaled to multiple GPUs as well as multiple nodes by modifying the appropriate parameters"]}, {"cell_type": "markdown", "id": "91ecb0d3", "metadata": {}, "source": ["# Data\n", "\n", "* In this playbook, we will leverage chip domain/hardware datasets from open-source GitHub repositories, wiki URLs, and academic papers. Data has been processed and curated using [NeMo Curator](https://github.com/NVIDIA/NeMo-Curator/tree/main) as shown in this [playbook](https://github.com/jvamaraju/ndc_dapt_playbook/tree/dapt_jv)"]}, {"cell_type": "markdown", "id": "ba16a72b", "metadata": {}, "source": ["# Notebook Outline\n", "\n", "* Step 1: Prepare the data for pretraining. This is a multi-step process discussed in detail later in the specific section (later in the notebook).\n", "\n", "* Step 2: Download the llama-2-7B hugging face checkpoint and convert to .nemo format.\n", "\n", "* Step 3: Continued pretraining the llama-2-7b model using the prepared data and the custom trained tokenizer (from the previous notebook)."]}, {"cell_type": "markdown", "id": "ec372453", "metadata": {}, "source": ["# Step 1: Data Preparation for pretraining\n", "\n", "Identify the different file types (example: code, text, etc) in the pretraining data, in this case we only have 'code' type files. This is typically dataset dependent. \n", "\n", "If you used the Data Curation tutorial as instructed in the Readme, you can point ```data_path ``` variable to the path containing the curated data."]}, {"cell_type": "code", "execution_count": 1, "id": "2c935b99", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Number of Files containing 'file_type':'text': 0\n", "Number of Files containing 'file_type':'code': 8835\n"]}], "source": ["import os\n", "import json\n", "\n", "# Function to count the number of files in each of the different file types- code, text\n", "def identify_jsonl_files(data_path):\n", "    code_files = []\n", "    text_files = []\n", "    cnt_text = 0\n", "    cnt_code = 0\n", "    for root, _, files in os.walk(data_path):\n", "        for file in files:\n", "            if file.endswith('.jsonl'):\n", "                file_path = os.path.join(root, file)\n", "                with open(file_path, 'r') as f:\n", "                    has_code = False\n", "                    has_text = False\n", "                    for line in f:\n", "                        try:\n", "                            json_obj = json.loads(line.strip())\n", "                            file_type = json_obj.get('file_type', '').lower()\n", "                            if file_type == 'code':\n", "                                has_code = True\n", "                            elif file_type == 'text':\n", "                                has_text = True\n", "                            if has_code and has_text:\n", "                                break\n", "                        except json.JSONDecodeError:\n", "                            continue\n", "                if has_code:\n", "                    code_files.append(file_path)\n", "                    cnt_code = cnt_code + 1\n", "                if has_text:\n", "                    text_files.append(file_path)\n", "                    cnt_text = cnt_text + 1\n", "    return code_files, text_files, cnt_code, cnt_text\n", "\n", "# Modify data path to point to jsonl data source, in this case data_path='code/data/all_jsonl_data'\n", "data_path = 'code/data/all_jsonl_data'\n", "\n", "code_files, text_files, cnt_code, cnt_text = identify_jsonl_files(data_path)\n", "\n", "print(\"\\nNumber of Files containing 'file_type':'text':\", cnt_text)\n", "print(\"Number of Files containing 'file_type':'code':\", cnt_code)"]}, {"cell_type": "markdown", "id": "60987ff2", "metadata": {}, "source": ["### Merging code JSONL files into a single JSONL file for further preprocessing"]}, {"cell_type": "markdown", "id": "c02f2e6f", "metadata": {}, "source": ["This is an optional step, it is possible to use multiple jsonl files in this workflow as well. This example uses a single merged. jsonl file"]}, {"cell_type": "code", "execution_count": 3, "id": "892f4493", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "\n", "def list_jsonl_files(directory):\n", "    jsonl_files = []\n", "    for root, _, files in os.walk(directory):\n", "        for file in files:\n", "            if file.endswith('.jsonl'):\n", "                jsonl_files.append(os.path.join(root, file))\n", "    return jsonl_files\n", "\n", "# Function to merge multiple jsonl files into a single file \n", "def merge_jsonl_files(directory, output_file):\n", "    jsonl_files = list_jsonl_files(directory)\n", "    \n", "    with open(output_file, 'w') as outfile:\n", "        for input_file in jsonl_files:\n", "            with open(input_file, 'r') as infile:\n", "                for line in infile:\n", "                    try:\n", "                        json_object = json.loads(line.strip())\n", "                        json.dump(json_object, outfile)\n", "                        outfile.write('\\n')\n", "                    except json.JSONDecodeError:\n", "                        print(f\"Skipping invalid JSON in {input_file}: {line.strip()}\")\n", "\n", "    print(f\"Merged {len(jsonl_files)} JSONL files into {output_file}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "9bb0c80a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Merged 8835 JSONL files into code_merged_output.jsonl\n"]}], "source": ["directory = 'code/data/all_jsonl_data'\n", "output_file = 'code_merged_output.jsonl'\n", "merge_jsonl_files(directory, output_file)"]}, {"cell_type": "markdown", "id": "6d00ad63", "metadata": {}, "source": ["### Data Format Conversion for pretraining: JSONL to bin/idx files \n", "\n", "For efficient pretraining, we convert data from JSONL to bin/idx format. \n", "\n", "JSONL files, while convenient for storing structured text data, are not optimized for high-speed data loading during large language model training. In pretraining workflows, particularly those with large datasets and complex model architectures, the need for fast data access and efficient memory management is essential.\n", "\n", "The bin/idx format is a binary format specifically designed to facilitate high-throughput data loading. This format allows direct, randomized access to data samples, which speeds up I/O operations and reduces the memory footprint compared to loading JSONL files. By converting data to bin/idx format, hardware utilization can be maximized and bottlenecks in data processing can be avoided, leading to a more efficient pretraining process.\n", "\n", "#### Benefits of bin/idx format for Pretraining:\n", "\n", "* **Optimized I/O Performance:** The binary format enables quicker data reads and reduces latency, allowing the model to continuously access data at high speeds.\n", "* **Efficient Memory Usage:** Data in bin/idx format consumes less memory during loading, making it suitable for large datasets and enabling better use of available system resources.\n", "* **Enhanced Scalability:** With bin/idx, it’s easier to handle shuffling and batching of large datasets, which is essential for pretraining on diverse domain-specific data."]}, {"cell_type": "code", "execution_count": 5, "id": "709f2c08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["merges.txt\t\t tokenizer.json\t\tvocab.json\r\n", "special_tokens_map.json  tokenizer_config.json\r\n"]}], "source": ["# After the running through the custom_tokenization.ipynb, you would have \n", "# the new domain adpated tokenizer model in the following directory\n", "!ls models/tokenizer/llama2/custom_tokenizer_init_20000_json"]}, {"cell_type": "markdown", "id": "de696d7b", "metadata": {}, "source": ["Modify the `input` to point to the merged `jsonl` file. Similarly modify paths to `vocab`, `tokenizer-model`, `merge-file` to point to relevant file paths. \n", "\n", "In the following code block, ```tokenizer-model``` is set to using the original tokenizer that comes as a part of llama2-7b-hf, but `tokenizer-model` should point to the custom tokenizer (trained in the custom tokenizer training notebook) if your data has domain specific terminology"]}, {"cell_type": "code", "execution_count": null, "id": "dcbf66a2", "metadata": {}, "outputs": [], "source": ["!python3 /opt/NeMo/scripts/nlp_language_modeling/preprocess_data_for_megatron.py \\\n", "--input='code_merged_output.jsonl' \\\n", "--json-keys=text \\\n", "--tokenizer-library=sentencepiece \\\n", "--vocab 'models/tokenizer/llama2/custom_tokenizer_init_20000_json/vocab.json' \\\n", "--dataset-impl mmap \\\n", "--tokenizer-model '/workspace/Llama-2-7b-hf/tokenizer.model' \\\n", "--tokenizer-type llama \\\n", "--merge-file 'models/tokenizer/llama2/custom_tokenizer_init_20000_json/merges.txt' \\\n", "--append-eod \\\n", "--output-prefix='preprocessed_data'"]}, {"cell_type": "code", "execution_count": 6, "id": "0f05efa5", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["README.md\t\t\t   nemo_experiments\r\n", "cdeng\t\t\t\t   preprocessed_data_text_document\r\n", "code\t\t\t\t   preprocessed_data_text_document.bin\r\n", "code_merged_output.jsonl\t   preprocessed_data_text_document.idx\r\n", "domain_adaptive_pretraining.ipynb  venv\r\n"]}], "source": ["# If the above step runs successfully, two files with the extensions .bin and .idx will be generated\n", "!ls "]}, {"cell_type": "markdown", "id": "82f95149", "metadata": {}, "source": ["# Step 2: Download Llama-2-7b Hugging Face checkpoint and convert to .nemo checkpoint\n", "\n", "The code below assumes you already have the llama-2-7b checkpoint downloaded in ```/workspace/Llama-2-7b-hf/```\n", "\n", "Llama-2-7b-hf checkpoint can be downloaded from https://huggingface.co/meta-llama/Llama-2-7b-hf/tree/main"]}, {"cell_type": "code", "execution_count": null, "id": "46c7f997", "metadata": {}, "outputs": [], "source": ["!python /opt/NeMo/scripts/checkpoint_converters/convert_llama_hf_to_nemo.py --input_name_or_path=/workspace/Llama-2-7b-hf/ --output_path=/workspace/llama2-7b.nemo"]}, {"cell_type": "markdown", "id": "b94e774b", "metadata": {}, "source": ["The conversion will generate a ```llama2-7b.nemo``` file which can be used for the continued pretraining using NeMo Toolkit as shown in Step 3. "]}, {"cell_type": "code", "execution_count": 7, "id": "c689e584", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Llama-2-7b-hf\t\t  dapt-custom-tokenization  megatron_llama\r\n", "bin-idx-conversion.ipynb  dapt-data-curation\t    megatron_llama_config.yaml\r\n", "convert.py\t\t  llama2-7b.nemo\t    sentencepiece\r\n", "custom-tokenizer\t  loader_llama2.py\t    venv\r\n"]}], "source": ["!ls /workspace"]}, {"cell_type": "markdown", "id": "fe1bdfe0", "metadata": {}, "source": ["# Step 3: Continued Pretraining using Llama2-7b with NeMo\n", "\n", "For this step `megatron_gpt_pretraining.py` from NeMo Toolkit is used for continued pretraining, this step allows to configure different parameters for the pretraining depending on the set up. For example `trainer.devices` `model.tensor_model_parallel_size` depend on the number of GPUs available for this job. \n", "\n", "Additionally, specify the path to the custom trained tokenizer for `model.tokenizer.model`, the `.nemo` checkpoint for `model.restore_from_path`. \n", "\n", "The `model.data.data_prefix` is specified in the form [weightage to data, datafile] Example `[1,preprocessed_data_text_document]` assigns the whole weightage [=1] to `preprocessed_data_text_document`. If there are multiple files, different weightage (should sum to 1) can be assigned to each file to control the data blend for pretraining. \n"]}, {"cell_type": "code", "execution_count": null, "id": "4a40f547", "metadata": {}, "outputs": [], "source": ["# Test out the pretraining set up with mock data: model.data.data_impl=mock\n", "\n", "!python /opt/NeMo/examples/nlp/language_modeling/megatron_gpt_pretraining.py  \\\n", "    --config-path=/opt/NeMo/examples/nlp/language_modeling/conf \\\n", "    --config-name=megatron_llama_config \\\n", "    trainer.precision=bf16 \\\n", "    trainer.devices=1 \\\n", "    trainer.num_nodes=1 \\\n", "    trainer.max_steps=2 \\\n", "    trainer.val_check_interval=8 \\\n", "    model.data.data_impl=mock \\\n", "    model.micro_batch_size=1 \\\n", "    model.global_batch_size=4 \\\n", "    model.tensor_model_parallel_size=1 \\\n", "    model.pipeline_model_parallel_size=1 \\\n", "    model.tokenizer.library=sentencepiece \\\n", "    model.tokenizer.model=/workspace/Llama-2-7b-hf/tokenizer.model \\\n", "    +model.restore_from_path=/workspace/llama2-7b.nemo \\\n", "    exp_manager.name=megatron_llama_continual \\\n", "    exp_manager.resume_ignore_no_checkpoint=false \\\n", "    exp_manager.resume_if_exists=false "]}, {"cell_type": "code", "execution_count": null, "id": "71672ff4", "metadata": {}, "outputs": [], "source": ["# Pretraining using preprocessed data (+model.data.data_prefix)\n", "\n", "!python /opt/NeMo/examples/nlp/language_modeling/megatron_gpt_pretraining.py  \\\n", "    --config-path=/opt/NeMo/examples/nlp/language_modeling/conf \\\n", "    --config-name=megatron_llama_config \\\n", "    trainer.precision=bf16 \\\n", "    trainer.devices=2 \\\n", "    trainer.num_nodes=1 \\\n", "    trainer.max_steps=5 \\\n", "    trainer.val_check_interval=8 \\\n", "    model.micro_batch_size=1 \\\n", "    model.global_batch_size=4 \\\n", "    model.tensor_model_parallel_size=2 \\\n", "    model.pipeline_model_parallel_size=1 \\\n", "    model.tokenizer.library=sentencepiece \\\n", "    model.tokenizer.model=/workspace/Llama-2-7b-hf/tokenizer.model \\\n", "    model.megatron_amp_O2=True \\\n", "    +model.restore_from_path=/workspace/llama2-7b.nemo \\\n", "    +model.data.data_prefix=[1,preprocessed_data_text_document] \\\n", "    exp_manager.name=megatron_llama_continual \\\n", "    exp_manager.resume_ignore_no_checkpoint=true \\\n", "    exp_manager.resume_if_exists=false "]}, {"cell_type": "markdown", "id": "cf30d8c8", "metadata": {}, "source": ["### To monitor the training, launch Tensorboard from another terminal\n", "\n", "`tensorboard --logdir nemo_experiments --bind_all`"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}