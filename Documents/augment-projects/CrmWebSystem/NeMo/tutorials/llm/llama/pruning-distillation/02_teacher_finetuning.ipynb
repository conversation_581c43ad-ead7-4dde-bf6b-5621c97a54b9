{"cells": [{"cell_type": "markdown", "id": "84b146ba-08b6-4adb-a858-8e4294c5e781", "metadata": {}, "source": ["### Step 2: Fine-tune the teacher on the dataset\n", "\n", "We fine-tune the unpruned model on our dataset to correct the distribution shift from the original dataset the model was trained on. We use [NeMo Run](https://docs.nvidia.com/nemo-framework/user-guide/latest/nemo-2.0/quickstart.html) to run the fine-tuning recipe.\n", "\n", "According to the [blog](https://developer.nvidia.com/blog/how-to-prune-and-distill-llama-3-1-8b-to-an-nvidia-llama-3-1-minitron-4b-model/) and [tech report](https://arxiv.org/pdf/2408.11796), experiments showed that without correcting for this distribution shift, the teacher provides suboptimal guidance on the dataset during distillation.\n", "\n", "> `NOTE:` For this demonstration, training run is capped by `STEPS`, and validation is carried out every `VAL_INTERVAL` steps. Please change the fine-tuning recipe for your model and dataset:"]}, {"cell_type": "code", "execution_count": null, "id": "576d7990", "metadata": {}, "outputs": [], "source": ["import nemo_run as run\n", "from nemo.collections import llm"]}, {"cell_type": "markdown", "id": "a973f2b6", "metadata": {}, "source": ["Let's define the recipe and executor for running it. We will use the `torchrun` launcher for this but you can use the Slurm launcher as well for multi-node runs."]}, {"cell_type": "code", "execution_count": null, "id": "8a904921", "metadata": {}, "outputs": [], "source": ["# Set path(s) if different:\n", "ROOT_DIR = \"/workspace\"\n", "MODEL_PATH = f\"{ROOT_DIR}/Llama-3.1-8B-nemo\"\n", "SEQ_LENGTH = 8192\n", "EXP_NAME = \"Llama-3.1-8B-nemo-ft\"\n", "EXP_DIR = f\"{ROOT_DIR}/{EXP_NAME}\"\n", "DATA_PATH = f\"{ROOT_DIR}/wikitext-data\"\n", "DATA_PATHS = {\n", "    \"train\": [1.0, f\"{DATA_PATH}/wikitext_tokenized_train_text_document\"],\n", "    \"validation\": [f\"{DATA_PATH}/wikitext_tokenized_test_text_document\"],\n", "    \"test\": [f\"{DATA_PATH}/wikitext_tokenized_val_text_document\"],\n", "}\n", "INDEX_MAPPING_DIR = f\"{DATA_PATH}/index_mappings\"\n", "\n", "# Change these to accommodate resources:\n", "DEVICES = 8\n", "NODES = 1\n", "TENSOR_PARALLEL_SIZE = DEVICES\n", "PIPELINE_PARALLEL_SIZE = NODES\n", "MICRO_BATCH_SIZE = 4\n", "\n", "# Change the fine-tuning recipe for your model and dataset (below values for demonstration purposes):\n", "STEPS = 30\n", "GLOBAL_BATCH_SIZE = 128\n", "LR = 1e-4\n", "MIN_LR = 1e-5\n", "WARMUP_STEPS = 2\n", "LOG_INTERVAL = 1\n", "VAL_INTERVAL = 10\n", "NUM_VAL_BATCHES = 5\n", "\n", "\n", "def configure_recipe():\n", "    # Define the recipe\n", "    recipe = llm.llama31_8b.finetune_recipe(\n", "        num_nodes=NODES,\n", "        num_gpus_per_node=DEVICES,\n", "        peft_scheme=None,  # Full finetuning\n", "        seq_length=SEQ_LENGTH,\n", "    )\n", "    recipe.resume.restore_config.path = MODEL_PATH\n", "    recipe.log.explicit_log_dir = EXP_DIR\n", "    recipe.log.ckpt.every_n_train_steps = VAL_INTERVAL\n", "\n", "    # Change dataset (default is Squad dataset)\n", "    recipe.data = run.Config(\n", "        llm.PreTrainingDataModule,\n", "        paths=DATA_PATHS,\n", "        index_mapping_dir=INDEX_MAPPING_DIR,\n", "        seq_length=SEQ_LENGTH,\n", "        micro_batch_size=MICRO_BATCH_SIZE,\n", "        global_batch_size=GLOBAL_BATCH_SIZE,\n", "    )\n", "\n", "    # Set the training parameters if you dont want to use the recipe defaults\n", "    recipe.trainer.max_steps = STEPS\n", "    recipe.trainer.log_every_n_steps = LOG_INTERVAL\n", "    recipe.trainer.val_check_interval = VAL_INTERVAL\n", "    recipe.trainer.limit_val_batches = NUM_VAL_BATCHES\n", "    recipe.trainer.strategy.tensor_model_parallel_size = TENSOR_PARALLEL_SIZE\n", "    recipe.trainer.strategy.pipeline_model_parallel_size = PIPELINE_PARALLEL_SIZE\n", "    recipe.trainer.strategy.sequence_parallel = TENSOR_PARALLEL_SIZE > 1\n", "    recipe.optim.config.lr = LR\n", "    recipe.optim.lr_scheduler.warmup_steps = WARMUP_STEPS\n", "    recipe.optim.lr_scheduler.min_lr = MIN_LR\n", "\n", "    return recipe\n", "\n", "\n", "recipe = configure_recipe()\n", "print(recipe)\n", "env_vars = {\n", "    \"TORCH_NCCL_AVOID_RECORD_STREAMS\": \"1\",  # Disable caching NCCL communication buffer memory\n", "    \"NCCL_NVLS_ENABLE\": \"0\",  # Disable NVLink SHARP to save memory\n", "}\n", "executor = run.LocalExecutor(ntasks_per_node=recipe.trainer.devices, launcher=\"torchrun\", env_vars=env_vars)"]}, {"cell_type": "markdown", "id": "ba566a7a", "metadata": {}, "source": ["Let's run the recipe. This is expected to take at least 20 minutes to run on 8x 80GB H100 GPUs (may vary depending on GPU and recipe)."]}, {"cell_type": "code", "execution_count": null, "id": "a86e74eb", "metadata": {}, "outputs": [], "source": ["run.run(recipe, executor=executor, name=EXP_NAME)"]}, {"cell_type": "markdown", "id": "3040a993-8423-475f-8bc6-d1dd1ce16a83", "metadata": {}, "source": ["This will create save topk fine-tuned teacher models at `/workspace/Llama-3.1-8B-nemo-ft/checkpoints/{model_name}--{val_loss:.2f}-{step}-{consumed_samples}`. Let's rename the one with lowest `val_loss` to to `/workspace/Llama-3.1-8B-nemo-ft/checkpoints/best` so its easier to find. We'll use this later."]}, {"cell_type": "code", "execution_count": null, "id": "f846b122", "metadata": {}, "outputs": [], "source": ["# NOTE: Rename path based on your training run\n", "!mv \"{ROOT_DIR}/Llama-3.1-8B-nemo-ft/checkpoints/model_name=0--val_loss=2.03-step=29-consumed_samples=3840.0\" \"{ROOT_DIR}/Llama-3.1-8B-nemo-ft/checkpoints/best\""]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}