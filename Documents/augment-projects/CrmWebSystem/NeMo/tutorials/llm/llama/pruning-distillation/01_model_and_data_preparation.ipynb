{"cells": [{"cell_type": "markdown", "id": "ab9e2e97-7f10-4353-859e-693842bde465", "metadata": {}, "source": ["In this notebook, we will prepare the model and dataset for the subsequent steps.\n", "\n", "Let's define the paths to the model and the dataset."]}, {"cell_type": "code", "execution_count": null, "id": "73efb325", "metadata": {}, "outputs": [], "source": ["HF_MODEL_NAME_OR_PATH = \"meta-llama/Llama-3.1-8B\"\n", "\n", "ROOT_DIR = \"/workspace\"\n", "NEMO_OUTPUT_PATH = f\"{ROOT_DIR}/Llama-3.1-8B-nemo\"\n", "DATA_PATH = f\"{ROOT_DIR}/wikitext-data\""]}, {"cell_type": "markdown", "id": "fca2355f", "metadata": {}, "source": ["### Step 1: Convert the Hugging Face model to NeMo checkoint format\n", "\n", "You can skip this step if you already have the model in NeMo 2.0 checkpoint format."]}, {"cell_type": "code", "execution_count": null, "id": "84cb6425", "metadata": {}, "outputs": [], "source": ["!python -c 'from nemo.collections import llm; llm.import_ckpt(llm.LlamaModel(llm.Llama31Config8B()), source=\"hf://{HF_MODEL_NAME_OR_PATH}\", output_path=\"{NEMO_OUTPUT_PATH}\")'"]}, {"cell_type": "markdown", "id": "78831e12", "metadata": {}, "source": ["This is an example of what the nemo checkpoint should look like:\n", "\n", "```\n", "Llama-3.1-8B-nemo/\n", "├── context\n", "│   ├── artifacts\n", "│   │   └── generation_config.json\n", "│   ├── io.json\n", "│   ├── model.yaml\n", "│   └── nemo_tokenizer\n", "│       ├── special_tokens_map.json\n", "│       ├── tokenizer_config.json\n", "│       └── tokenizer.json\n", "└── weights\n", "    ├── __0_0.distcp\n", "    ├── __0_1.distcp\n", "    ├── common.pt\n", "    └── metadata.json\n", "```\n", "\n", "\n", "`NOTE:` If you wish to convert the NeMo models back to Hugging Face format after pruning and distillation, you can use the following command:\n", "\n", "```bash\n", "python -c 'from nemo.collections import llm; llm.export_ckpt(path=\"<NEMO_MODEL_PATH>\", target=\"hf\", output_path=\"<HF_OUTPUT_PATH>\")'\n", "```"]}, {"cell_type": "markdown", "id": "16a9e822", "metadata": {}, "source": ["### Step 2: Prepare the dataset\n", "\n", "**Obtain the dataset**: Generate the `wikitext-{train/validation/test}.jsonl` splits after loading the [WikiText-103-v1](https://huggingface.co/datasets/Salesforce/wikitext/viewer/wikitext-103-v1) dataset."]}, {"cell_type": "code", "execution_count": null, "id": "6a3a359a", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from datasets import load_dataset\n", "\n", "# Load the WikiText-103 dataset\n", "dataset = load_dataset(\"wikitext\", \"wikitext-103-v1\")\n", "\n", "# Define the destination folder\n", "os.makedirs(DATA_PATH, exist_ok=True)\n", "\n", "\n", "# Function to save dataset split to a JSONL file\n", "def save_to_jsonl(file_path, data):\n", "    with open(file_path, \"w\") as file:\n", "        for item in data:\n", "            file.write(json.dumps(item) + \"\\n\")\n", "\n", "\n", "# Define splits\n", "splits = [\"train\", \"validation\", \"test\"]\n", "file_paths = {split: os.path.join(DATA_PATH, f\"wikitext-{split}.jsonl\") for split in splits}\n", "\n", "# Save splits to JSONL files and calculate their sizes\n", "for split in splits:\n", "    if split in dataset:\n", "        print(f\"Saving {split} split to {file_paths[split]}\")\n", "        save_to_jsonl(file_paths[split], dataset[split])\n", "    else:\n", "        print(f\"Split {split} not found in the dataset.\")\n", "\n", "print(\"Dataset saved to JSONL files.\")"]}, {"cell_type": "markdown", "id": "f7a6333c", "metadata": {}, "source": ["The dataset has to be preprocessed using the [preprocess_data_for_megatron.py](https://github.com/NVIDIA/NeMo/blob/main/scripts/nlp_language_modeling/preprocess_data_for_megatron.py) script included in the NeMo Framework. This step will also tokenize data using the `meta-llama/Llama-3.1-8B` tokenizer model to convert the data into a memory map format.\n", "\n", "> `NOTE:` In the block of code below, pass the paths to your train, test, and validation data files."]}, {"cell_type": "code", "execution_count": null, "id": "6505c00b-9eb4-4087-9e49-423f6228e690", "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["!python /opt/NeMo/scripts/nlp_language_modeling/preprocess_data_for_megatron.py \\\n", "    --input=\"{DATA_PATH}/wikitext-train.jsonl\" \\\n", "    --tokenizer-library=huggingface \\\n", "    --tokenizer-type=\"{HF_MODEL_NAME_OR_PATH}\" \\\n", "    --output-prefix=\"{DATA_PATH}/wikitext_tokenized_train\" \\\n", "    --append-eod \\\n", "    --workers=32"]}, {"cell_type": "code", "execution_count": null, "id": "42bec54a-94f6-4c87-8e14-2726ef6c2625", "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["!python /opt/NeMo/scripts/nlp_language_modeling/preprocess_data_for_megatron.py \\\n", "    --input=\"{DATA_PATH}/wikitext-validation.jsonl\" \\\n", "    --tokenizer-library=huggingface \\\n", "    --tokenizer-type=\"{HF_MODEL_NAME_OR_PATH}\" \\\n", "    --output-prefix=\"{DATA_PATH}/wikitext_tokenized_val\" \\\n", "    --append-eod \\\n", "    --workers=32"]}, {"cell_type": "code", "execution_count": null, "id": "fb1aa80f-70bc-4dff-8b08-3bff48d9a1c3", "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["!python /opt/NeMo/scripts/nlp_language_modeling/preprocess_data_for_megatron.py \\\n", "    --input=\"{DATA_PATH}/wikitext-test.jsonl\" \\\n", "    --tokenizer-library=huggingface \\\n", "    --tokenizer-type=\"{HF_MODEL_NAME_OR_PATH}\" \\\n", "    --output-prefix=\"{DATA_PATH}/wikitext_tokenized_test\" \\\n", "    --append-eod \\\n", "    --workers=32"]}, {"cell_type": "markdown", "id": "5d77ee8a-e0dc-44f7-b5e8-3b6025d979d7", "metadata": {}, "source": ["After running the above scripts, you will see the preprocesed `/workspace/wikitext-data/wikitext_tokenized_{train/val/test}_text_document.{idx/bin}`files. These output files will be used in the next step."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}