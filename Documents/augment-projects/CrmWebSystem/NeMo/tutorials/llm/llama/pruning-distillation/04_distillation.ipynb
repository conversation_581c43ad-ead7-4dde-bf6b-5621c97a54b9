{"cells": [{"cell_type": "markdown", "id": "09d30e35-8e9d-4d2e-bd14-738c627a3963", "metadata": {}, "source": ["### Step 4: Distill knowledge from teacher into pruned students\n", "In this step, we will distill the depth and width pruned models using Knowledge Distillation. We use [NeMo Run](https://docs.nvidia.com/nemo-framework/user-guide/latest/nemo-2.0/quickstart.html) to run the distillation recipe. For usage details of distillation recipe or alternative commandline script, please refer to the [distillation docs](https://docs.nvidia.com/nemo-framework/user-guide/latest/model-optimization/distillation/distillation.html).\n", "\n", "Let's define the common recipe setup for depth and width pruned model's distillation.\n", "\n", "> `NOTE:` For this demonstration, training run is capped by `STEPS`, and validation is carried out every `VAL_INTERVAL` steps. Please change the recipe for your model and dataset:"]}, {"cell_type": "code", "execution_count": null, "id": "a9885569", "metadata": {}, "outputs": [], "source": ["import nemo_run as run\n", "from nemo.collections import llm\n", "from nemo.collections.llm.modelopt.recipes import distillation_recipe\n", "from nemo.lightning.pytorch.strategies.utils import RestoreConfig"]}, {"cell_type": "code", "execution_count": null, "id": "17ad5860", "metadata": {}, "outputs": [], "source": ["# Set path(s) if different:\n", "ROOT_DIR = \"/workspace\"\n", "TEACHER_MODEL_PATH = f\"{ROOT_DIR}/Llama-3.1-8B-nemo-ft/checkpoints/best\"\n", "SEQ_LENGTH = 8192\n", "DATA_PATH = f\"{ROOT_DIR}/wikitext-data\"\n", "DATA_PATHS = {\n", "    \"train\": [1.0, f\"{DATA_PATH}/wikitext_tokenized_train_text_document\"],\n", "    \"validation\": [f\"{DATA_PATH}/wikitext_tokenized_test_text_document\"],\n", "    \"test\": [f\"{DATA_PATH}/wikitext_tokenized_val_text_document\"],\n", "}\n", "INDEX_MAPPING_DIR = f\"{DATA_PATH}/index_mappings\"\n", "\n", "# Change these to accommodate resources:\n", "DEVICES = 8\n", "NODES = 1\n", "TENSOR_PARALLEL_SIZE = DEVICES\n", "PIPELINE_PARALLEL_SIZE = NODES\n", "MICRO_BATCH_SIZE = 4\n", "\n", "# Change the fine-tuning recipe for your model and dataset (below values for demonstration purposes):\n", "STEPS = 30\n", "GLOBAL_BATCH_SIZE = 128\n", "LR = 1e-4\n", "MIN_LR = 1e-5\n", "WARMUP_STEPS = 2\n", "LOG_INTERVAL = 1\n", "VAL_INTERVAL = 10\n", "NUM_VAL_BATCHES = 5\n", "\n", "\n", "def configure_recipe(student_model_path, exp_dir, exp_name):\n", "    # Define the recipe\n", "    recipe = distillation_recipe(\n", "        student_model_path=student_model_path,\n", "        teacher_model_path=TEACHER_MODEL_PATH,\n", "        name=exp_name,\n", "        num_nodes=NODES,\n", "        num_gpus_per_node=DEVICES,\n", "    )\n", "    recipe.resume.restore_config = run.Config(\n", "        RestoreConfig,\n", "        path=student_model_path,\n", "    )\n", "    recipe.log.explicit_log_dir = exp_dir\n", "    recipe.log.ckpt.every_n_train_steps = VAL_INTERVAL\n", "    del recipe.log.ckpt.train_time_interval\n", "\n", "    # Change dataset (default is Squad dataset)\n", "    recipe.data = run.Config(\n", "        llm.PreTrainingDataModule,\n", "        paths=DATA_PATHS,\n", "        index_mapping_dir=INDEX_MAPPING_DIR,\n", "        seq_length=SEQ_LENGTH,\n", "        micro_batch_size=MICRO_BATCH_SIZE,\n", "        global_batch_size=GLOBAL_BATCH_SIZE,\n", "    )\n", "\n", "    # Set the training parameters if you dont want to use the recipe defaults\n", "    recipe.trainer.max_steps = STEPS\n", "    recipe.trainer.log_every_n_steps = LOG_INTERVAL\n", "    recipe.trainer.val_check_interval = VAL_INTERVAL\n", "    recipe.trainer.limit_val_batches = NUM_VAL_BATCHES\n", "    recipe.trainer.strategy.tensor_model_parallel_size = TENSOR_PARALLEL_SIZE\n", "    recipe.trainer.strategy.pipeline_model_parallel_size = PIPELINE_PARALLEL_SIZE\n", "    recipe.trainer.strategy.sequence_parallel = TENSOR_PARALLEL_SIZE > 1\n", "    recipe.optim.config.lr = LR\n", "    recipe.optim.lr_scheduler.warmup_steps = WARMUP_STEPS\n", "    recipe.optim.lr_scheduler.min_lr = MIN_LR\n", "\n", "    return recipe"]}, {"cell_type": "markdown", "id": "c33cf641-0d27-417f-b3ee-c06701698184", "metadata": {}, "source": ["#### Step 4a: Distilling depth-pruned student\n", "While distilling knowledge from the teacher to depth-pruned model, the `student_model_path` model would be  `/workspace/Llama-3.1-8B-nemo-ft-depth-pruned` as produced by the depth-pruning step in the [pruning](./03_pruning.ipynb) notebook."]}, {"cell_type": "code", "execution_count": null, "id": "5d23a01e-4912-47cb-bf21-b4fd72007ec1", "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["student_model_path=f\"{ROOT_DIR}/Llama-3.1-8B-nemo-ft-depth-pruned\"\n", "exp_name=\"Llama-3.1-8B-nemo-ft-depth-distilled\"\n", "exp_dir=f\"{ROOT_DIR}/{exp_name}\"\n", "\n", "recipe = configure_recipe(student_model_path, exp_dir, exp_name)\n", "print(recipe)\n", "\n", "env_vars = {\n", "    \"TORCH_NCCL_AVOID_RECORD_STREAMS\": \"1\",  # Disable caching NCCL communication buffer memory\n", "    \"NCCL_NVLS_ENABLE\": \"0\",  # Disable NVLink SHARP to save memory\n", "}\n", "executor = run.LocalExecutor(ntasks_per_node=recipe.trainer.devices, launcher=\"torchrun\", env_vars=env_vars)"]}, {"cell_type": "code", "execution_count": null, "id": "37784aa1", "metadata": {}, "outputs": [], "source": ["run.run(recipe, executor=executor, name=exp_name)"]}, {"cell_type": "markdown", "id": "42d910d9-14dd-44ba-bf2c-0064737c70fa", "metadata": {}, "source": ["This will create the final distilled model at something like `/workspace/Llama-3.1-8B-nemo-ft-depth-distilled/checkpoints/{model_name}--{val_loss:.2f}-{step}-{consumed_samples}`. Exact path depends on your distillation run. The corresponding tensorboard logs will be saved at `/workspace/Llama-3.1-8B-nemo-ft-depth-distilled/tb_logs`.\n", "\n", "> `NOTE:`This script takes at least 35 minutes to run (depends on GPU) and generate the final distilled model.\n", "\n", "Here is an image of the validation loss over 30 steps of running distillation:\n", "\n", "<img src=\"./imgs/val_loss_depth_pruned_student_distillation.png\" width=\"400px\" alt=\"Validation Loss plot when using the Depth-pruned model as the student\">"]}, {"cell_type": "markdown", "id": "cfa05542", "metadata": {}, "source": ["#### Step 4b: Distilling width-pruned student\n", "While distilling knowledge from the teacher to width-pruned model, the `student_model_path` model would be  `/workspace/Llama-3.1-8B-nemo-ft-width-pruned` as produced by the width-pruning step in the [pruning](./03_pruning.ipynb) notebook."]}, {"cell_type": "code", "execution_count": null, "id": "66c2c44a", "metadata": {}, "outputs": [], "source": ["student_model_path=f\"{ROOT_DIR}/Llama-3.1-8B-nemo-ft-width-pruned\"\n", "exp_name=\"Llama-3.1-8B-nemo-ft-width-distilled\"\n", "exp_dir=f\"{ROOT_DIR}/{exp_name}\"\n", "\n", "recipe = configure_recipe(student_model_path, exp_dir, exp_name)\n", "print(recipe)\n", "\n", "env_vars = {\n", "    \"TORCH_NCCL_AVOID_RECORD_STREAMS\": \"1\",  # Disable caching NCCL communication buffer memory\n", "    \"NCCL_NVLS_ENABLE\": \"0\",  # Disable NVLink SHARP to save memory\n", "}\n", "executor = run.LocalExecutor(ntasks_per_node=recipe.trainer.devices, launcher=\"torchrun\", env_vars=env_vars)"]}, {"cell_type": "code", "execution_count": null, "id": "c71c17fc", "metadata": {}, "outputs": [], "source": ["run.run(recipe, executor=executor, name=exp_name)"]}, {"cell_type": "markdown", "id": "0638f6d9", "metadata": {}, "source": ["This will create the final distilled model at something like `/workspace/Llama-3.1-8B-nemo-ft-width-distilled/checkpoints/{model_name}--{val_loss:.2f}-{step}-{consumed_samples}`. Exact path depends on your distillation run. The corresponding tensorboard logs will be saved at `/workspace/Llama-3.1-8B-nemo-ft-width-distilled/tb_logs`.\n", "\n", "> `NOTE:`This script takes at least 35 minutes to run (depends on GPU) and generate the final distilled model.\n", "\n", "Here is an image of the validation loss over 30 steps of running distillation:\n", "\n", "<img src=\"./imgs/val_loss_width_pruned_student_distillation.png\" width=\"400px\" alt=\"Validation Loss plot when using the width-pruned model as the student\">\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}