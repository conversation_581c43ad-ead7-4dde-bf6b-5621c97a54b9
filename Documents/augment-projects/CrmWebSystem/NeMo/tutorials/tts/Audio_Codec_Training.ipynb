{"cells": [{"cell_type": "markdown", "metadata": {"id": "7X-TwhdTGmlc"}, "source": ["# License"]}, {"cell_type": "markdown", "metadata": {"id": "fCQUeZRPGnoe"}, "source": ["> Copyright (c) 2025, NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", ">\n", "> Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except in compliance with the License. You may obtain a copy of the License at\n", ">\n", "> http://www.apache.org/licenses/LICENSE-2.0\n", ">\n", "> Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "rtBDkKqVGZJ8"}, "source": ["# Introduction"]}, {"cell_type": "markdown", "metadata": {"id": "pZ2QSsXuGbMe"}, "source": ["In this tutorial we show how to use NeMo to train and fine-tune **neural audio codecs**.\n", "\n", "Neural audio codecs are deep learning models that compress audio into a low bitrate representation. The compact embedding space created by these models can be useful for various speech tasks, such as TTS and ASR.\n", "\n", "<div>\n", "<img src=\"https://github.com/NVIDIA/NeMo/releases/download/v1.22.0/nemo_audio_codec.png\" width=\"800\", height=\"400\"/>\n", "</div>\n", "\n", "Audio codec models typically have an *encoder-quantizer-decoder* structure. The **encoder** takes an input audio signal and encodes it into a sequence of embeddings. The **quantizer** discretizes the embeddings to create a lookup table known as a **codebook**. The embeddings saved in the codebook are referred to as **audio codes**. The **decoder** takes the audio codes as input and attempts to reconstruct the original audio signal.\n", "\n", "To store compressed audio we only need to save the codebook index for each embedding in an audio sequence. This is how audio codec models achieve low bitrates. The codebook indices for an audio are referred to **audio tokens**. It is becoming common for speech generation models to synthesize speech by predicting audio tokens.\n", "\n", "In NeMo we have implementations of the [SEANet encoder and decoder](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/modules/encodec_modules.py#L146)  used by [EnCodec](https://github.com/facebookresearch/encodec). As well as a [ResNet encoder](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/modules/audio_codec_modules.py#L1035) and [HiFi-GAN decoder](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/modules/audio_codec_modules.py#L875). For quantizers we support [Residual Vector Quantizer](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/modules/encodec_modules.py#L694) (**RVQ**) and [Finite Scalar Quantizer](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/modules/audio_codec_modules.py#L409) (**FSQ**).\n"]}, {"cell_type": "markdown", "metadata": {"id": "3OZassNG5xff"}, "source": ["# Install"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WZvQvPkIhRi3"}, "outputs": [], "source": ["BRANCH = 'main'\n", "# Install NeMo library. If you are running locally (rather than on Google Colab), comment out the below line\n", "# and instead follow the instructions at https://github.com/NVIDIA/NeMo#Installation\n", "!python -m pip install git+https://github.com/NVIDIA/NeMo.git@$BRANCH#egg=nemo_toolkit[tts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "v8NGOM0EzK8W"}, "outputs": [], "source": ["from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tvsgWO_WhV3M"}, "outputs": [], "source": ["# Directory where tutorialscripts will run and outputs will be saved.\n", "ROOT_DIR = Path().absolute() / \"codec_tutorial\"\n", "\n", "# Nemo code paths\n", "NEMO_DIR = ROOT_DIR / \"nemo\"\n", "NEMO_SCRIPT_DIR = NEMO_DIR / \"scripts\" / \"dataset_processing\" / \"tts\"\n", "NEMO_EXAMPLES_DIR = NEMO_DIR / \"examples\" / \"tts\"\n", "NEMO_CONFIG_DIR = NEMO_EXAMPLES_DIR / \"conf\"\n", "\n", "nemo_download_dir = str(NEMO_DIR)\n", "# Download local version of NeMo scripts. If you are running locally and want to use your own local NeMo code,\n", "# comment out the below line and set NEMO_ROOT_DIR to your local path.\n", "!git clone -b $<PERSON>ANCH https://github.com/NVIDIA/NeMo.git $nemo_download_dir"]}, {"cell_type": "markdown", "metadata": {"id": "KAbH7N427FdT"}, "source": ["# Configuration"]}, {"cell_type": "markdown", "metadata": {"id": "ODgdGgsAAUku"}, "source": ["Predefined model configurations are available in https://github.com/NVIDIA/NeMo/tree/main/examples/tts/conf/audio_codec.\n", "\n", "Configurations available include:\n", "\n", "*   **audio_codec_*.yaml**: Audio codec configurations optimized for various sampling rates.\n", "*   **mel_codec_*.yaml**: A mel-spectrogram based codec designed to maximize the performance of speech synthesis models.\n", "*   **encodec_*.yaml**: A reproduction of the original [EnCodec](https://arxiv.org/abs/2210.13438) model setup.\n", "*   **audio_codec_low_frame_rate_22050.yaml**: The [Low Frame-rate Speech Codec](https://arxiv.org/abs/2409.12117): an audio codec that achieves high-quality audio compression with a 1.89 kbps bitrate and 21.5 frames per second.\n", "\n", "This tutorial can be run with any of our predefined configs. As a default we have selected `audio_codec_16000.yaml`, which works for 16kHz audio."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SPtjS2LkzE9Q"}, "outputs": [], "source": ["from omegaconf import OmegaConf"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iCPJFKg63Dsv"}, "outputs": [], "source": ["CONFIG_FILENAME = \"audio_codec_22050.yaml\"\n", "CONFIG_DIR = NEMO_CONFIG_DIR / \"audio_codec\"\n", "\n", "config_filepath = CONFIG_DIR / CONFIG_FILENAME\n", "\n", "if not config_filepath.exists():\n", "  raise ValueError(f\"Config file does not exist {config_filepath}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QE0HYh7FjAR3"}, "outputs": [], "source": ["# Read model name and sample rate from model configuration\n", "omega_conf = OmegaConf.load(config_filepath)\n", "MODEL_NAME = omega_conf.name\n", "SAMPLE_RATE = omega_conf.sample_rate\n", "print(f\"Training {MODEL_NAME} with sample rate {SAMPLE_RATE}\")"]}, {"cell_type": "markdown", "metadata": {"id": "W7F--_0maLh5"}, "source": ["We provide pretrained model checkpoints for fine-tuning.\n", "\n", "A list of models available on NGC can be found [here](https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/tts/models/audio_codec.py#L645).\n", "\n", "A list of models available on Hugging Face can be found [here](https://huggingface.co/collections/nvidia/nemo-audio-codecs-674f57ab6cb1324f997b5d5b). To use a checkpoint from hugging face, add \"nvidia/\" before the model name."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cADIAIDUcGWd"}, "outputs": [], "source": ["from nemo.collections.tts.models.audio_codec import AudioCodecModel\n", "\n", "pretrained_model_name = \"nvidia/audio-codec-22khz\"\n", "\n", "if pretrained_model_name is None:\n", "  MODEL_CHECKPOINT_PATH = None\n", "else:\n", "  MODEL_CHECKPOINT_PATH = AudioCodecModel.from_pretrained(model_name=pretrained_model_name, return_model_file=True)"]}, {"cell_type": "markdown", "metadata": {"id": "fM4QPsLTnzK7"}, "source": ["# Dataset Preparation"]}, {"cell_type": "markdown", "metadata": {"id": "tkZC6Dl7KRl6"}, "source": ["For our tutorial, we use a subset of [VCTK](https://datashare.ed.ac.uk/handle/10283/2950) dataset with 5 speakers (p225-p229)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sYzvAYr2vo1K"}, "outputs": [], "source": ["import tarfile\n", "import wget\n", "\n", "from nemo.collections.asr.parts.utils.manifest_utils import read_manifest, write_manifest"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aoxN1QsUzX-k"}, "outputs": [], "source": ["# Create dataset directory\n", "DATA_DIR = ROOT_DIR / \"data\"\n", "\n", "DATA_DIR.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mArlQd5Hk36b"}, "outputs": [], "source": ["# Download the dataset\n", "dataset_url = \"https://vctk-subset.s3.amazonaws.com/vctk_subset_multispeaker.tar.gz\"\n", "dataset_tar_filepath = DATA_DIR / \"vctk.tar.gz\"\n", "\n", "if not dataset_tar_filepath.exists():\n", "    wget.download(dataset_url, out=str(dataset_tar_filepath))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p987cjtOy9C7"}, "outputs": [], "source": ["# Extract the dataset\n", "with tarfile.open(dataset_tar_filepath) as tar_f:\n", "    tar_f.extractall(DATA_DIR)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ko6dxYJW0i3G"}, "outputs": [], "source": ["DATASET_DIR = DATA_DIR / \"vctk_subset_multispeaker\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "We5FHYQt5BeO"}, "outputs": [], "source": ["# Visualize the raw dataset\n", "train_raw_filepath = DATASET_DIR / \"train.json\"\n", "!head $train_raw_filepath"]}, {"cell_type": "markdown", "metadata": {"id": "i3jsk2HCMSU5"}, "source": ["## Manifest Processing"]}, {"cell_type": "markdown", "metadata": {"id": "N8WuAGJsMHRn"}, "source": ["The downloaded manifest is formatted for TTS training, which contains metadata such as text and speaker.\n", "\n", "For codec training we need `audio_filepath`. The `audio_filepath` field can either be an *absolute path*, or a *relative path* with the root directory provided as an argument to each script. Here we use relative paths.\n", "\n", "If you include `duration` the training script will automatically calculate the total size of each dataset used, and can be useful for filtering based on utterance length."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zoCRrKQ20VZP"}, "outputs": [], "source": ["def update_manifest(data_type):\n", "    input_filepath = DATASET_DIR / f\"{data_type}.json\"\n", "    output_filepath = DATASET_DIR / f\"{data_type}_raw.json\"\n", "\n", "    entries = read_manifest(input_filepath)\n", "    new_entries = []\n", "    for entry in entries:\n", "        # Provide relative path instead of absolute path\n", "        audio_filepath = entry[\"audio_filepath\"].replace(\"audio/\", \"\")\n", "        duration = round(entry[\"duration\"], 2)\n", "        new_entry = {\n", "            \"audio_filepath\": audio_filepath,\n", "            \"duration\": duration\n", "        }\n", "        new_entries.append(new_entry)\n", "\n", "    write_manifest(output_path=output_filepath, target_manifest=new_entries, ensure_ascii=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PaCc3GCG1UbH"}, "outputs": [], "source": ["update_manifest(\"dev\")\n", "update_manifest(\"train\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bVLIB3Ip1Aqn"}, "outputs": [], "source": ["# Visualize updated 'audio_filepath' field.\n", "train_filepath = DATASET_DIR / \"train_raw.json\"\n", "!head $train_filepath"]}, {"cell_type": "markdown", "metadata": {"id": "alrRDWio41qi"}, "source": ["## Audio Preprocessing"]}, {"cell_type": "markdown", "metadata": {"id": "4WfEaMwpUsFt"}, "source": ["Next we process the audio data using [preprocess_audio.py](https://github.com/NVIDIA/NeMo/blob/main/scripts/dataset_processing/tts/preprocess_audio.py).\n", "\n", "During this step we can apply the following transformations:\n", "\n", "1. Resample the audio from 48khz to the target sample rate for codec training.\n", "2. Remove long silence from the beginning and end of each audio file. This can be done using an *energy* based approach which will work on clean audio, or using *voice activity detection (VAD)* which is slower but also works on audio with background or static noise (eg. from a microphone). Here we suggest VAD because some audio in VCTK has background noise."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WEvIefjnd7AG"}, "outputs": [], "source": ["import IPython.display as ipd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-qEuCH8S4vFP"}, "outputs": [], "source": ["# Python wrapper to invoke the given bash script with the given input args\n", "def run_script(script, args):\n", "    args = ' \\\\'.join(args)\n", "    cmd = f\"python {script} \\\\{args}\"\n", "\n", "    print(cmd.replace(\" \\\\\", \"\\n\"))\n", "    print()\n", "    !$cmd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0kQ1UDnGfdX6"}, "outputs": [], "source": ["audio_preprocessing_script = NEMO_SCRIPT_DIR / \"preprocess_audio.py\"\n", "\n", "# Directory with raw audio data\n", "input_audio_dir = DATASET_DIR / \"audio\"\n", "# Directory to write preprocessed audio to\n", "output_audio_dir = DATASET_DIR / \"audio_preprocessed\"\n", "# Whether to overwrite existing audio, if it exists in the output directory\n", "overwrite_audio = True\n", "# Whether to overwrite output manifest, if it exists\n", "overwrite_manifest = True\n", "# Number of threads to parallelize audio processing across\n", "num_workers = 4\n", "# Format of output audio files. Use \"flac\" to compress to a smaller file size.\n", "output_format = \"flac\"\n", "# Method for silence trimming. Can use \"energy.yaml\" or \"vad.yaml\".\n", "trim_config_path = NEMO_CONFIG_DIR / \"trim\" / \"vad.yaml\"\n", "\n", "def preprocess_audio(data_type):\n", "    input_filepath = DATASET_DIR / f\"{data_type}_raw.json\"\n", "    output_filepath = DATASET_DIR / f\"{data_type}_manifest.json\"\n", "\n", "    args = [\n", "        f\"--input_manifest={input_filepath}\",\n", "        f\"--output_manifest={output_filepath}\",\n", "        f\"--input_audio_dir={input_audio_dir}\",\n", "        f\"--output_audio_dir={output_audio_dir}\",\n", "        f\"--num_workers={num_workers}\",\n", "        f\"--output_sample_rate={SAMPLE_RATE}\",\n", "        f\"--output_format={output_format}\",\n", "        f\"--trim_config_path={trim_config_path}\"\n", "    ]\n", "    if overwrite_manifest:\n", "        args.append(\"--overwrite_manifest\")\n", "    if overwrite_audio:\n", "        args.append(\"--overwrite_audio\")\n", "\n", "    run_script(audio_preprocessing_script, args)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ai0zbXSOriuY"}, "outputs": [], "source": ["preprocess_audio(\"dev\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NUKnidQYfgDo"}, "outputs": [], "source": ["preprocess_audio(\"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "x2yhJtsj2lDR"}, "source": ["Before we proceed, it is important to verify that the audio processing works as expected. Let's listen to an audio file before and after processing.\n", "\n", "Note that the processed audio is shorter because we trimmed the leading and trailing silence."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AfdHUHAWuF-G"}, "outputs": [], "source": ["!ls $processed_audio_filepath"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_fM3GwJxkjOA"}, "outputs": [], "source": ["audio_file = \"p228_009.wav\"\n", "audio_filepath = input_audio_dir / audio_file\n", "processed_audio_filepath = output_audio_dir / audio_file.replace(\".wav\", \".flac\")\n", "\n", "print(\"Original audio.\")\n", "ipd.display(ipd.Audio(audio_filepath, rate=SAMPLE_RATE))\n", "\n", "print(\"Processed audio.\")\n", "ipd.display(ipd.Audio(processed_audio_filepath, rate=SAMPLE_RATE))"]}, {"cell_type": "markdown", "metadata": {"id": "oRO842MUyODC"}, "source": ["# Audio Codec Training"]}, {"cell_type": "markdown", "metadata": {"id": "E4wUKYOfH8ax"}, "source": ["Here we show how to train an audio codec model from scratch. Instructions and checkpoints for fine-tuning will be provided later.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pqfl9jAYMJob"}, "outputs": [], "source": ["import os\n", "import torch\n", "from omegaconf import OmegaConf"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jK2rr-Kr6Qg8"}, "outputs": [], "source": ["dataset_name = \"vctk\"\n", "audio_dir = DATASET_DIR / \"audio_preprocessed\"\n", "train_manifest_filepath = DATASET_DIR / \"train_manifest.json\"\n", "dev_manifest_filepath = DATASET_DIR / \"dev_manifest.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Vr4D-NB-yQx8"}, "outputs": [], "source": ["audio_codec_training_script = NEMO_EXAMPLES_DIR / \"audio_codec.py\"\n", "\n", "# The total number of training steps will be (epochs * steps_per_epoch)\n", "epochs = 10\n", "steps_per_epoch = 10\n", "\n", "# Name of the experiment that will determine where it is saved locally and in TensorBoard and WandB\n", "run_id = \"test_run\"\n", "exp_dir = ROOT_DIR / \"exps\"\n", "codec_exp_output_dir = exp_dir / MODEL_NAME / run_id\n", "# Directory where predicted audio will be stored periodically throughout training\n", "codec_log_dir = codec_exp_output_dir / \"logs\"\n", "# Optionally log visualization of learned codes.\n", "log_dequantized = True\n", "# Optionally log predicted audio and other artifacts to WandB\n", "log_to_wandb = False\n", "# Optionally log predicted audio and other artifacts to Tensorboard\n", "log_to_tensorboard = False\n", "\n", "if torch.cuda.is_available():\n", "    accelerator=\"gpu\"\n", "    batch_size = 4\n", "    devices = -1\n", "else:\n", "    import multiprocessing\n", "    accelerator=\"cpu\"\n", "    batch_size = 2\n", "    devices = multiprocessing.cpu_count()\n", "\n", "args = [\n", "    f\"--config-path={CONFIG_DIR}\",\n", "    f\"--config-name={CONFIG_FILENAME}\",\n", "    f\"max_epochs={epochs}\",\n", "    f\"weighted_sampling_steps_per_epoch={steps_per_epoch}\",\n", "    f\"batch_size={batch_size}\",\n", "    f\"log_dir={codec_log_dir}\",\n", "    f\"exp_manager.exp_dir={exp_dir}\",\n", "    f\"+exp_manager.version={run_id}\",\n", "    f\"model.log_config.log_wandb={log_to_wandb}\",\n", "    f\"model.log_config.log_tensorboard={log_to_tensorboard}\",\n", "    f\"model.log_config.generators.0.log_dequantized={log_dequantized}\",\n", "    f\"trainer.accelerator={accelerator}\",\n", "    f\"trainer.devices={devices}\",\n", "    f\"+train_ds_meta.{dataset_name}.manifest_path={train_manifest_filepath}\",\n", "    f\"+train_ds_meta.{dataset_name}.audio_dir={audio_dir}\",\n", "    f\"+val_ds_meta.{dataset_name}.manifest_path={dev_manifest_filepath}\",\n", "    f\"+val_ds_meta.{dataset_name}.audio_dir={audio_dir}\",\n", "    f\"+log_ds_meta.{dataset_name}.manifest_path={dev_manifest_filepath}\",\n", "    f\"+log_ds_meta.{dataset_name}.audio_dir={audio_dir}\"\n", "]\n", "\n", "if MODEL_CHECKPOINT_PATH is not None:\n", "  args.append(f\"+init_from_nemo_model={MODEL_CHECKPOINT_PATH}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Bn8lQG0PxWGi"}, "outputs": [], "source": ["# If an error occurs, log the entire stacktrace.\n", "os.environ[\"HYDRA_FULL_ERROR\"] = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yUxFCNrE3Ywi"}, "outputs": [], "source": ["# Do the model training. For some configurations this step might hang when using CPU.\n", "run_script(audio_codec_training_script, args)"]}, {"cell_type": "markdown", "metadata": {"id": "BBPIpS-lL6z9"}, "source": ["During training, the model will automatically save predictions for all audio files specified in the `log_ds_meta` manifest."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rSFOm1Sg46Lh"}, "outputs": [], "source": ["codec_log_epoch_dir = codec_log_dir / \"epoch_10\" / dataset_name\n", "!ls $codec_log_epoch_dir"]}, {"cell_type": "markdown", "metadata": {"id": "oCJs7oCLMIjD"}, "source": ["This makes it easy to listen to the audio to determine how well the model is performing. We can decide to stop training when either:\n", "\n", "*   The predicted audio sounds almost identical to the original audio.\n", "*   The predicted audio stops improving in between epochs.\n", "\n", "**Note that when training from scratch, the dataset in this tutorial is too small to get good audio quality.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "G6k4ymzfJ5Y6"}, "outputs": [], "source": ["audio_filepath_ground_truth = output_audio_dir / \"p228_009.flac\"\n", "audio_filepath_reconstructed = codec_log_epoch_dir / \"p228_009_audio_out.wav\"\n", "\n", "print(\"Ground truth audio.\")\n", "ipd.display(ipd.Audio(audio_filepath_ground_truth, rate=SAMPLE_RATE))\n", "\n", "print(\"Reconstructed audio.\")\n", "ipd.display(ipd.Audio(audio_filepath_reconstructed, rate=SAMPLE_RATE))\n", "\n", "dequantized_filepath = codec_log_epoch_dir / \"p228_009_dequantized.png\"\n", "ipd.Image(dequantized_filepath)"]}, {"cell_type": "markdown", "metadata": {"id": "rynZYwg2VP5d"}, "source": ["# Related Information"]}, {"cell_type": "markdown", "metadata": {"id": "_LtyHHuLkNDv"}, "source": ["To learn more about audio codec models in NeMo, look at our [documentation](https://docs.nvidia.com/nemo-framework/user-guide/latest/nemotoolkit/tts/models.html#codecs).\n", "\n", "For more information on how to download and run pre-trained audio codec models, visit [NGC](https://catalog.ngc.nvidia.com/models?filters=&orderBy=scoreDESC&query=codec)."]}, {"cell_type": "markdown", "metadata": {"id": "LeqV3VvJVOb-"}, "source": ["# References"]}, {"cell_type": "markdown", "metadata": {"id": "Rvu4w2x_3RSY"}, "source": ["1.   [EnCodec](https://arxiv.org/abs/2210.13438)\n", "2.   [Finite Scalar Quantization (FSQ)](https://arxiv.org/abs/2309.15505)\n", "3.   [HiFi-GAN](https://arxiv.org/abs/2010.05646)\n", "4.   [SEANet](https://arxiv.org/abs/2009.02095)\n", "5.   [Spectral Codecs](https://arxiv.org/abs/2406.05298)\n", "6.   [Low Frame-rate Speech Codec](https://arxiv.org/abs/2409.12117)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}