# NeMo MultiModal Tutorials

<Intro>
The goal of this collection of IPython notebooks is to familiarize users with NeMo multimodal offerings. By launching the latest Nemo container, users can easily step through the tutorials, conduct experiments, and build custom application.

#### Getting Started Checklist
* Register a NGC account
* Generate your NGC API key for pulling NeMo contrainer (please refer to this [video](https://youtu.be/yBNt4qSnn0k?feature=shared) for details)
* Make sure the container host system meets the minimal GPU requirement (i.e. NVIDIA A100 GPU)

## What does this repository contain?
This repository contains the following resources:
* [Data Preparation](./Multimodal%20Data%20Preparation.ipynb)
* [Train And Infer Stable Diffusion Model](./Stable%20Diffusion%20Tutorial.ipynb)
* [Train DreanBooth Model](./DreamBooth%20Tutorial.ipynb)
* [Train Neva Model](./NeVA%20Tutorial.ipynb)
* [LITA Tutorial](./LITA%20Tutorial.ipynb)
