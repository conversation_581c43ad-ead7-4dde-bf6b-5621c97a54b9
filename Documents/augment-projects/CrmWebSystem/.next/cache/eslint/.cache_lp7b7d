[{"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/ai/converse/route.ts": "1", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/[...nextauth]/route.ts": "2", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/forgot-password/route.ts": "3", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/register/route.ts": "4", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/reset-password/route.ts": "5", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/verify-email/route.ts": "6", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/dashboard/stats/route.ts": "7", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/route.ts": "8", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/tasks/[taskId]/route.ts": "9", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/tasks/route.ts": "10", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/route.ts": "11", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx": "12", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/login/page.tsx": "13", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/register/page.tsx": "14", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/reset-password/page.tsx": "15", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx": "16", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx": "17", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx": "18", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx": "19", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx": "20", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx": "21", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx": "22", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/new/page.tsx": "23", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/new/page.tsx": "24", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx": "25", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx": "26", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx": "27", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/page.tsx": "28", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx": "29", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/main-nav.tsx": "30", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/mobile-nav.tsx": "31", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/search.tsx": "32", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/user-nav.tsx": "33", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/theme-provider.tsx": "34", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/theme-toggle.tsx": "35", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/alert.tsx": "36", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/animate.tsx": "37", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/avatar.tsx": "38", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/badge.tsx": "39", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/breadcrumb.tsx": "40", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/button.tsx": "41", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/card.tsx": "42", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/dialog.tsx": "43", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/dropdown-menu.tsx": "44", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/form.tsx": "45", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/hover-card.tsx": "46", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/input.tsx": "47", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/label.tsx": "48", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/navigation-menu.tsx": "49", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/scroll-area.tsx": "50", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/select.tsx": "51", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/separator.tsx": "52", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/sheet.tsx": "53", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/spinner.tsx": "54", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/switch.tsx": "55", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/tabs.tsx": "56", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/textarea.tsx": "57", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth/auth.config.ts": "58", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth.ts": "59", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/email.ts": "60", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/fonts.ts": "61", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma/index.ts": "62", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts": "63", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/utils.ts": "64", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/middleware.ts": "65", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/types/next-auth.d.ts": "66", "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/types/speech.d.ts": "67"}, {"size": 5967, "mtime": 1749886808061, "results": "68", "hashOfConfig": "69"}, {"size": 2355, "mtime": 1749888072151, "results": "70", "hashOfConfig": "69"}, {"size": 2057, "mtime": 1749855184524, "results": "71", "hashOfConfig": "69"}, {"size": 2430, "mtime": 1749885461071, "results": "72", "hashOfConfig": "69"}, {"size": 1543, "mtime": 1749855199305, "results": "73", "hashOfConfig": "69"}, {"size": 1373, "mtime": 1749855328954, "results": "74", "hashOfConfig": "69"}, {"size": 1917, "mtime": 1749884427677, "results": "75", "hashOfConfig": "69"}, {"size": 2282, "mtime": 1749853607628, "results": "76", "hashOfConfig": "69"}, {"size": 3574, "mtime": 1749853612871, "results": "77", "hashOfConfig": "69"}, {"size": 2653, "mtime": 1749886326529, "results": "78", "hashOfConfig": "69"}, {"size": 1830, "mtime": 1749884567828, "results": "79", "hashOfConfig": "69"}, {"size": 3240, "mtime": 1749855183390, "results": "80", "hashOfConfig": "69"}, {"size": 3972, "mtime": 1749855330274, "results": "81", "hashOfConfig": "69"}, {"size": 4155, "mtime": 1749855023173, "results": "82", "hashOfConfig": "69"}, {"size": 4730, "mtime": 1749855181787, "results": "83", "hashOfConfig": "69"}, {"size": 3203, "mtime": 1749855327763, "results": "84", "hashOfConfig": "69"}, {"size": 1906, "mtime": 1749884962553, "results": "85", "hashOfConfig": "69"}, {"size": 7396, "mtime": 1749884873109, "results": "86", "hashOfConfig": "69"}, {"size": 8811, "mtime": 1749885047954, "results": "87", "hashOfConfig": "69"}, {"size": 5886, "mtime": 1749853739410, "results": "88", "hashOfConfig": "69"}, {"size": 6942, "mtime": 1749853723185, "results": "89", "hashOfConfig": "69"}, {"size": 7621, "mtime": 1749853867614, "results": "90", "hashOfConfig": "69"}, {"size": 8176, "mtime": 1749887196132, "results": "91", "hashOfConfig": "69"}, {"size": 5041, "mtime": 1749853939664, "results": "92", "hashOfConfig": "69"}, {"size": 5050, "mtime": 1749884903419, "results": "93", "hashOfConfig": "69"}, {"size": 10031, "mtime": 1749886206310, "results": "94", "hashOfConfig": "69"}, {"size": 448, "mtime": 1749883882044, "results": "95", "hashOfConfig": "69"}, {"size": 993, "mtime": 1749851882344, "results": "96", "hashOfConfig": "69"}, {"size": 582, "mtime": 1749885753076, "results": "97", "hashOfConfig": "69"}, {"size": 1871, "mtime": 1749884987998, "results": "98", "hashOfConfig": "69"}, {"size": 2092, "mtime": 1749881686035, "results": "99", "hashOfConfig": "69"}, {"size": 991, "mtime": 1749881684592, "results": "100", "hashOfConfig": "69"}, {"size": 3403, "mtime": 1749881840998, "results": "101", "hashOfConfig": "69"}, {"size": 259, "mtime": 1749888118135, "results": "102", "hashOfConfig": "69"}, {"size": 1055, "mtime": 1749885599290, "results": "103", "hashOfConfig": "69"}, {"size": 1614, "mtime": 1749853665349, "results": "104", "hashOfConfig": "69"}, {"size": 1444, "mtime": 1749881730290, "results": "105", "hashOfConfig": "69"}, {"size": 1097, "mtime": 1749854467979, "results": "106", "hashOfConfig": "69"}, {"size": 1631, "mtime": 1749853687980, "results": "107", "hashOfConfig": "69"}, {"size": 1980, "mtime": 1749856084871, "results": "108", "hashOfConfig": "69"}, {"size": 2123, "mtime": 1749853665320, "results": "109", "hashOfConfig": "69"}, {"size": 1989, "mtime": 1749853665328, "results": "110", "hashOfConfig": "69"}, {"size": 3982, "mtime": 1749853665351, "results": "111", "hashOfConfig": "69"}, {"size": 8284, "mtime": 1749854467991, "results": "112", "hashOfConfig": "69"}, {"size": 3759, "mtime": 1749853665336, "results": "113", "hashOfConfig": "69"}, {"size": 1652, "mtime": 1749888152993, "results": "114", "hashOfConfig": "69"}, {"size": 967, "mtime": 1749853665338, "results": "115", "hashOfConfig": "69"}, {"size": 611, "mtime": 1749853665338, "results": "116", "hashOfConfig": "69"}, {"size": 6664, "mtime": 1749881498224, "results": "117", "hashOfConfig": "69"}, {"size": 1645, "mtime": 1749881674980, "results": "118", "hashOfConfig": "69"}, {"size": 6253, "mtime": 1749853665347, "results": "119", "hashOfConfig": "69"}, {"size": 699, "mtime": 1749854003656, "results": "120", "hashOfConfig": "69"}, {"size": 4090, "mtime": 1749881674990, "results": "121", "hashOfConfig": "69"}, {"size": 816, "mtime": 1749856027907, "results": "122", "hashOfConfig": "69"}, {"size": 1177, "mtime": 1749885059516, "results": "123", "hashOfConfig": "69"}, {"size": 1969, "mtime": 1749885059526, "results": "124", "hashOfConfig": "69"}, {"size": 759, "mtime": 1749853665339, "results": "125", "hashOfConfig": "69"}, {"size": 2033, "mtime": 1749888105193, "results": "126", "hashOfConfig": "69"}, {"size": 1686, "mtime": 1749884036899, "results": "127", "hashOfConfig": "69"}, {"size": 741, "mtime": 1749855186099, "results": "128", "hashOfConfig": "69"}, {"size": 149, "mtime": 1749883160831, "results": "129", "hashOfConfig": "69"}, {"size": 284, "mtime": 1749851832797, "results": "130", "hashOfConfig": "69"}, {"size": 313, "mtime": 1749854886430, "results": "131", "hashOfConfig": "69"}, {"size": 166, "mtime": 1749853647466, "results": "132", "hashOfConfig": "69"}, {"size": 324, "mtime": 1749854921606, "results": "133", "hashOfConfig": "69"}, {"size": 350, "mtime": 1749883944748, "results": "134", "hashOfConfig": "69"}, {"size": 2897, "mtime": 1749886625525, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ie6lt2", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/ai/converse/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/forgot-password/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/register/route.ts", ["337"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/reset-password/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/verify-email/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/dashboard/stats/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/route.ts", ["338", "339", "340"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/tasks/[taskId]/route.ts", ["341", "342"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/tasks/route.ts", ["343"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/route.ts", ["344"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx", ["345"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/login/page.tsx", ["346"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/register/page.tsx", ["347"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/reset-password/page.tsx", ["348"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx", ["349"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx", ["350", "351"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx", ["352"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx", ["353"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx", ["354"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/new/page.tsx", ["355", "356"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/new/page.tsx", ["357"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx", ["358"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/main-nav.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/mobile-nav.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/search.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/dashboard/user-nav.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/theme-provider.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/theme-toggle.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/animate.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/form.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/hover-card.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/select.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/spinner.tsx", ["359"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth/auth.config.ts", ["360", "361"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth.ts", ["362"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/email.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/fonts.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma/index.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/middleware.ts", ["363"], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/types/next-auth.d.ts", [], [], "/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/types/speech.d.ts", ["364", "365", "366", "367", "368", "369", "370", "371", "372", "373", "374", "375"], [], {"ruleId": "376", "severity": 2, "message": "377", "line": 36, "column": 11, "nodeType": null, "messageId": "378", "endLine": 36, "endColumn": 34}, {"ruleId": "376", "severity": 2, "message": "379", "line": 36, "column": 12, "nodeType": null, "messageId": "378", "endLine": 36, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 71, "column": 12, "nodeType": null, "messageId": "378", "endLine": 71, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 98, "column": 12, "nodeType": null, "messageId": "378", "endLine": 98, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 50, "column": 12, "nodeType": null, "messageId": "378", "endLine": 50, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 139, "column": 12, "nodeType": null, "messageId": "378", "endLine": 139, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 98, "column": 12, "nodeType": null, "messageId": "378", "endLine": 98, "endColumn": 17}, {"ruleId": "376", "severity": 2, "message": "379", "line": 70, "column": 12, "nodeType": null, "messageId": "378", "endLine": 70, "endColumn": 17}, {"ruleId": "380", "severity": 2, "message": "381", "line": 40, "column": 21, "nodeType": "382", "messageId": "383", "endLine": 40, "endColumn": 24, "suggestions": "384"}, {"ruleId": "376", "severity": 2, "message": "379", "line": 49, "column": 14, "nodeType": null, "messageId": "378", "endLine": 49, "endColumn": 19}, {"ruleId": "380", "severity": 2, "message": "381", "line": 53, "column": 21, "nodeType": "382", "messageId": "383", "endLine": 53, "endColumn": 24, "suggestions": "385"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 57, "column": 21, "nodeType": "382", "messageId": "383", "endLine": 57, "endColumn": 24, "suggestions": "386"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 42, "column": 23, "nodeType": "382", "messageId": "383", "endLine": 42, "endColumn": 26, "suggestions": "387"}, {"ruleId": "376", "severity": 2, "message": "388", "line": 24, "column": 26, "nodeType": null, "messageId": "378", "endLine": 24, "endColumn": 32}, {"ruleId": "376", "severity": 2, "message": "379", "line": 45, "column": 14, "nodeType": null, "messageId": "378", "endLine": 45, "endColumn": 19}, {"ruleId": "376", "severity": 2, "message": "389", "line": 26, "column": 17, "nodeType": null, "messageId": "378", "endLine": 26, "endColumn": 24}, {"ruleId": "376", "severity": 2, "message": "389", "line": 34, "column": 17, "nodeType": null, "messageId": "378", "endLine": 34, "endColumn": 24}, {"ruleId": "376", "severity": 2, "message": "389", "line": 27, "column": 17, "nodeType": null, "messageId": "378", "endLine": 27, "endColumn": 24}, {"ruleId": "376", "severity": 2, "message": "389", "line": 27, "column": 17, "nodeType": null, "messageId": "378", "endLine": 27, "endColumn": 24}, {"ruleId": "380", "severity": 2, "message": "381", "line": 49, "column": 42, "nodeType": "382", "messageId": "383", "endLine": 49, "endColumn": 45, "suggestions": "390"}, {"ruleId": "376", "severity": 2, "message": "389", "line": 21, "column": 17, "nodeType": null, "messageId": "378", "endLine": 21, "endColumn": 24}, {"ruleId": "376", "severity": 2, "message": "379", "line": 49, "column": 14, "nodeType": null, "messageId": "378", "endLine": 49, "endColumn": 19}, {"ruleId": "391", "severity": 2, "message": "392", "line": 3, "column": 11, "nodeType": "393", "messageId": "394", "endLine": 3, "endColumn": 23, "suggestions": "395"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 48, "column": 26, "nodeType": "382", "messageId": "383", "endLine": 48, "endColumn": 29, "suggestions": "396"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 82, "column": 26, "nodeType": "382", "messageId": "383", "endLine": 82, "endColumn": 29, "suggestions": "397"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 52, "column": 26, "nodeType": "382", "messageId": "383", "endLine": 52, "endColumn": 29, "suggestions": "398"}, {"ruleId": "376", "severity": 2, "message": "399", "line": 5, "column": 23, "nodeType": null, "messageId": "378", "endLine": 5, "endColumn": 26}, {"ruleId": "380", "severity": 2, "message": "381", "line": 19, "column": 58, "nodeType": "382", "messageId": "383", "endLine": 19, "endColumn": 61, "suggestions": "400"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 20, "column": 51, "nodeType": "382", "messageId": "383", "endLine": 20, "endColumn": 54, "suggestions": "401"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 21, "column": 75, "nodeType": "382", "messageId": "383", "endLine": 21, "endColumn": 78, "suggestions": "402"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 22, "column": 72, "nodeType": "382", "messageId": "383", "endLine": 22, "endColumn": 75, "suggestions": "403"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 23, "column": 71, "nodeType": "382", "messageId": "383", "endLine": 23, "endColumn": 74, "suggestions": "404"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 24, "column": 56, "nodeType": "382", "messageId": "383", "endLine": 24, "endColumn": 59, "suggestions": "405"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 25, "column": 58, "nodeType": "382", "messageId": "383", "endLine": 25, "endColumn": 61, "suggestions": "406"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 26, "column": 57, "nodeType": "382", "messageId": "383", "endLine": 26, "endColumn": 60, "suggestions": "407"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 27, "column": 59, "nodeType": "382", "messageId": "383", "endLine": 27, "endColumn": 62, "suggestions": "408"}, {"ruleId": "380", "severity": 2, "message": "381", "line": 28, "column": 53, "nodeType": "382", "messageId": "383", "endLine": 28, "endColumn": 56, "suggestions": "409"}, {"ruleId": "410", "severity": 2, "message": "411", "line": 34, "column": 3, "nodeType": "412", "messageId": "413", "endLine": 34, "endColumn": 89}, {"ruleId": "410", "severity": 2, "message": "411", "line": 40, "column": 3, "nodeType": "412", "messageId": "413", "endLine": 40, "endColumn": 99}, "@typescript-eslint/no-unused-vars", "'verificationTokenExpiry' is assigned a value but never used.", "unusedVar", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["414", "415"], ["416", "417"], ["418", "419"], ["420", "421"], "'update' is assigned a value but never used.", "'session' is assigned a value but never used.", ["422", "423"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["424"], ["425", "426"], ["427", "428"], ["429", "430"], "'req' is defined but never used.", ["431", "432"], ["433", "434"], ["435", "436"], ["437", "438"], ["439", "440"], ["441", "442"], ["443", "444"], ["445", "446"], ["447", "448"], ["449", "450"], "@typescript-eslint/no-misused-new", "Interfaces cannot be constructed, only classes.", "TSConstructSignatureDeclaration", "errorMessageInterface", {"messageId": "451", "fix": "452", "desc": "453"}, {"messageId": "454", "fix": "455", "desc": "456"}, {"messageId": "451", "fix": "457", "desc": "453"}, {"messageId": "454", "fix": "458", "desc": "456"}, {"messageId": "451", "fix": "459", "desc": "453"}, {"messageId": "454", "fix": "460", "desc": "456"}, {"messageId": "451", "fix": "461", "desc": "453"}, {"messageId": "454", "fix": "462", "desc": "456"}, {"messageId": "451", "fix": "463", "desc": "453"}, {"messageId": "454", "fix": "464", "desc": "456"}, {"messageId": "465", "fix": "466", "desc": "467"}, {"messageId": "451", "fix": "468", "desc": "453"}, {"messageId": "454", "fix": "469", "desc": "456"}, {"messageId": "451", "fix": "470", "desc": "453"}, {"messageId": "454", "fix": "471", "desc": "456"}, {"messageId": "451", "fix": "472", "desc": "453"}, {"messageId": "454", "fix": "473", "desc": "456"}, {"messageId": "451", "fix": "474", "desc": "453"}, {"messageId": "454", "fix": "475", "desc": "456"}, {"messageId": "451", "fix": "476", "desc": "453"}, {"messageId": "454", "fix": "477", "desc": "456"}, {"messageId": "451", "fix": "478", "desc": "453"}, {"messageId": "454", "fix": "479", "desc": "456"}, {"messageId": "451", "fix": "480", "desc": "453"}, {"messageId": "454", "fix": "481", "desc": "456"}, {"messageId": "451", "fix": "482", "desc": "453"}, {"messageId": "454", "fix": "483", "desc": "456"}, {"messageId": "451", "fix": "484", "desc": "453"}, {"messageId": "454", "fix": "485", "desc": "456"}, {"messageId": "451", "fix": "486", "desc": "453"}, {"messageId": "454", "fix": "487", "desc": "456"}, {"messageId": "451", "fix": "488", "desc": "453"}, {"messageId": "454", "fix": "489", "desc": "456"}, {"messageId": "451", "fix": "490", "desc": "453"}, {"messageId": "454", "fix": "491", "desc": "456"}, {"messageId": "451", "fix": "492", "desc": "453"}, {"messageId": "454", "fix": "493", "desc": "456"}, "suggestUnknown", {"range": "494", "text": "495"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "496", "text": "497"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "498", "text": "495"}, {"range": "499", "text": "497"}, {"range": "500", "text": "495"}, {"range": "501", "text": "497"}, {"range": "502", "text": "495"}, {"range": "503", "text": "497"}, {"range": "504", "text": "495"}, {"range": "505", "text": "497"}, "replaceEmptyInterfaceWithSuper", {"range": "506", "text": "507"}, "Replace empty interface with a type alias.", {"range": "508", "text": "495"}, {"range": "509", "text": "497"}, {"range": "510", "text": "495"}, {"range": "511", "text": "497"}, {"range": "512", "text": "495"}, {"range": "513", "text": "497"}, {"range": "514", "text": "495"}, {"range": "515", "text": "497"}, {"range": "516", "text": "495"}, {"range": "517", "text": "497"}, {"range": "518", "text": "495"}, {"range": "519", "text": "497"}, {"range": "520", "text": "495"}, {"range": "521", "text": "497"}, {"range": "522", "text": "495"}, {"range": "523", "text": "497"}, {"range": "524", "text": "495"}, {"range": "525", "text": "497"}, {"range": "526", "text": "495"}, {"range": "527", "text": "497"}, {"range": "528", "text": "495"}, {"range": "529", "text": "497"}, {"range": "530", "text": "495"}, {"range": "531", "text": "497"}, {"range": "532", "text": "495"}, {"range": "533", "text": "497"}, [1372, 1375], "unknown", [1372, 1375], "never", [1659, 1662], [1659, 1662], [1841, 1844], [1841, 1844], [1305, 1308], [1305, 1308], [1526, 1529], [1526, 1529], [34, 105], "type SpinnerProps = React.HTMLAttributes<HTMLDivElement>", [1278, 1281], [1278, 1281], [1988, 1991], [1988, 1991], [1230, 1233], [1230, 1233], [568, 571], [568, 571], [631, 634], [631, 634], [718, 721], [718, 721], [802, 805], [802, 805], [885, 888], [885, 888], [953, 956], [953, 956], [1023, 1026], [1023, 1026], [1092, 1095], [1092, 1095], [1163, 1166], [1163, 1166], [1228, 1231], [1228, 1231]]