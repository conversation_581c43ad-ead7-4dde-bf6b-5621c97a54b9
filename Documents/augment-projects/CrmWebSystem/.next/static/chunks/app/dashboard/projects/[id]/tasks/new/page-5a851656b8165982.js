(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[94],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...l})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>d,TN:()=>o});var s=a(5155);a(2115);var r=a(2085),n=a(9434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:a}),t),...r})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},6896:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(5155),r=a(5365),n=a(285),i=a(6695),d=a(2523),o=a(9409),l=a(8539),c=a(2108),u=a(5695),p=a(2115),x=a(6671);function g(e){let{params:t}=e,a=(0,u.useRouter)(),{data:g}=(0,c.useSession)(),[v,f]=(0,p.useState)(!1),[m,h]=(0,p.useState)(null),[b,j]=(0,p.useState)({title:"",description:"",status:"TODO",dueDate:""}),[y,w]=(0,p.useState)(!1),k=(0,p.useRef)(null),N=async e=>{e.preventDefault(),f(!0),h(null);try{let e=await fetch("/api/projects/".concat(t.id,"/tasks"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!e.ok){let t=(await e.json()).error||"Fout bij het aanmaken van de taak";throw Error(t)}a.push("/dashboard/projects/".concat(t.id)),x.oR.success("Taak succesvol aangemaakt!")}catch(e){h(e instanceof Error?e.message:"Er is een fout opgetreden"),x.oR.error(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{f(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Nieuwe Taak"}),(0,s.jsx)(n.$,{variant:"outline",onClick:()=>a.push("/dashboard/projects/".concat(t.id)),children:"Terug"})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Taak Details"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[m&&(0,s.jsx)(r.Fc,{variant:"destructive",children:(0,s.jsx)(r.TN,{children:m})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"title",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Titel"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(d.p,{id:"title",value:b.title,onChange:e=>j({...b,title:e.target.value}),required:!0}),(0,s.jsx)("button",{type:"button",style:{padding:"8px 12px",border:"1px solid grey",borderRadius:"4px"},onClick:y?()=>{k.current&&k.current.stop()}:()=>{if(!("webkitSpeechRecognition"in window))return void x.oR.error("Spraakherkenning wordt niet ondersteund in deze browser.");let e=new window.webkitSpeechRecognition;e.continuous=!1,e.interimResults=!1,e.lang="nl-NL",e.onstart=()=>{w(!0),x.oR.info("Luisteren gestart...")},e.onresult=e=>{let t=e.results[0][0].transcript;j(e=>({...e,title:t})),x.oR.success('Spraak herkend: "'.concat(t,'"'))},e.onerror=e=>{w(!1),h("Spraakherkenning fout: ".concat(e.error)),x.oR.error("Spraakherkenning fout: ".concat(e.error))},e.onend=()=>{w(!1),x.oR.info("Luisteren gestopt.")},k.current=e,e.start()},disabled:v,children:y?"Stop Opnemen":"Microfoon"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Beschrijving"}),(0,s.jsx)(l.T,{id:"description",value:b.description,onChange:e=>j({...b,description:e.target.value}),rows:4})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"status",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Status"}),(0,s.jsxs)(o.l6,{value:b.status,onValueChange:e=>j({...b,status:e}),children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"Selecteer status"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"TODO",children:"Te Doen"}),(0,s.jsx)(o.eb,{value:"IN_PROGRESS",children:"In Behandeling"}),(0,s.jsx)(o.eb,{value:"DONE",children:"Afgerond"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"dueDate",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Deadline"}),(0,s.jsx)(d.p,{id:"dueDate",type:"date",value:b.dueDate,onChange:e=>j({...b,dueDate:e.target.value})})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>a.push("/dashboard/projects/".concat(t.id)),children:"Annuleren"}),(0,s.jsx)(n.$,{type:"submit",disabled:v,children:v?"Aanmaken...":"Aanmaken"})]})]})})]})]})}},7452:(e,t,a)=>{Promise.resolve().then(a.bind(a,6896))},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>p,l6:()=>l,yv:()=>c});var s=a(5155);a(2115);var r=a(2918),n=a(6474),i=a(5196),d=a(7863),o=a(9434);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...d}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d,children:[i,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(g,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(v,{})]})})}function x(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(d.A,{className:"size-4"})})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,493,852,671,281,441,684,358],()=>t(7452)),_N_E=e.O()}]);