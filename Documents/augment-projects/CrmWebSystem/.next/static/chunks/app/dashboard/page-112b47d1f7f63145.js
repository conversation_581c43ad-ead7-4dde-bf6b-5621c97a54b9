(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155);t(2115);var a=t(9708),n=t(2085),i=t(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:n,asChild:o=!1,...c}=e,l=o?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:r})),...c})}},4879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(5155),a=t(5365),n=t(6126),i=t(285),d=t(6695),o=t(2108),c=t(5695),l=t(2115);function u(){var e;let r=(0,c.useRouter)(),{data:t}=(0,o.useSession)(),[u,x]=(0,l.useState)(null),[m,v]=(0,l.useState)(!0),[h,g]=(0,l.useState)(null);return((0,l.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/dashboard/stats");if(!e.ok)throw Error("Fout bij het ophalen van dashboard statistieken");let r=await e.json();x(r)}catch(e){g(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{v(!1)}})()},[]),m)?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):h||!u?(0,s.jsx)(a.Fc,{variant:"destructive",children:(0,s.jsx)(a.TN,{children:h||"Kon de dashboard statistieken niet laden"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold tracking-tight gradient-text",children:["Welkom terug, ",null==t||null==(e=t.user)?void 0:e.name]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Hier is een overzicht van je projecten en taken"})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(d.Zp,{className:"glow-accent border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Actieve Projecten"}),(0,s.jsx)(n.E,{variant:"default",className:"bg-primary/20 text-primary border-primary/30",children:u.activeProjects})]}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:u.activeProjects}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Projecten in uitvoering"})]})]}),(0,s.jsxs)(d.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Afgeronde Projecten"}),(0,s.jsx)(n.E,{variant:"secondary",className:"bg-secondary/20 text-secondary-foreground border-secondary/30",children:u.completedProjects})]}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:u.completedProjects}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Succesvol afgerond"})]})]}),(0,s.jsxs)(d.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Totaal Taken"}),(0,s.jsx)(n.E,{variant:"outline",className:"border-primary/30 text-primary",children:u.totalTasks})]}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:u.totalTasks}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alle taken"})]})]}),(0,s.jsxs)(d.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Afgeronde Taken"}),(0,s.jsx)(n.E,{variant:"secondary",className:"bg-secondary/20 text-secondary-foreground border-secondary/30",children:u.completedTasks})]}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:u.completedTasks}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Succesvol afgerond"})]})]})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Recente Projecten"})}),(0,s.jsx)(d.Wu,{children:0===u.recentProjects.length?(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Geen recente projecten gevonden"}):(0,s.jsx)("div",{className:"space-y-4",children:u.recentProjects.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e._count.tasks," ",1===e._count.tasks?"taak":"taken"]})]}),(0,s.jsx)(i.$,{variant:"ghost",onClick:()=>r.push("/dashboard/projects/".concat(e.id)),children:"Bekijken"})]},e.id))})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Snelle Acties"})}),(0,s.jsxs)(d.Wu,{className:"space-y-4",children:[(0,s.jsx)(i.$,{className:"w-full justify-start glow-accent hover:shadow-lg transition-all duration-300",onClick:()=>r.push("/dashboard/projects/new"),children:"Nieuw Project"}),(0,s.jsx)(i.$,{className:"w-full justify-start border-primary/30 hover:border-primary/50 transition-all duration-300",variant:"outline",onClick:()=>r.push("/dashboard/projects"),children:"Alle Projecten"})]})]})]})]})}},5365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>o});var s=t(5155);t(2115);var a=t(2085),n=t(9434);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),r),...a})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",r),...t})}},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(5155);t(2115);var a=t(9708),n=t(2085),i=t(9434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:t,asChild:n=!1,...o}=e,c=n?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(d({variant:t}),r),...o})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>l});var s=t(5155);t(2115);var a=t(9434);function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},6939:(e,r,t)=>{Promise.resolve().then(t.bind(t,4879))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[352,108,441,684,358],()=>r(6939)),_N_E=e.O()}]);