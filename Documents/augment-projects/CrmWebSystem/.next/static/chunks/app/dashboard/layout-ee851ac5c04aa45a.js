(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...d})}},1014:(e,t,a)=>{"use strict";a.d(t,{YE:()=>l,_A:()=>o,g1:()=>d});var s=a(5155),r=a(9434),n=a(6408),i=a(760);function o(e){let{children:t,className:a,delay:i=0,duration:o=.5}=e;return(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:o,delay:i},className:(0,r.cn)(a),children:t})}function l(e){let{children:t,className:a,delay:i=0,duration:o=.5}=e;return(0,s.jsx)(n.P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{duration:o,delay:i},className:(0,r.cn)(a),children:t})}function d(e){let{children:t}=e;return(0,s.jsx)(i.N,{mode:"wait",children:t})}},1394:(e,t,a)=>{"use strict";a.d(t,{BK:()=>o,eu:()=>i,q5:()=>l});var s=a(5155);a(2115);var r=a(4011),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},1506:(e,t,a)=>{Promise.resolve().then(a.bind(a,2939))},2346:(e,t,a)=>{"use strict";a.d(t,{w:()=>i});var s=a(5155);a(2115);var r=a(7489),n=a(9434);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...o}=e;return(0,s.jsx)(r.b,{"data-slot":"separator",decorative:i,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},2939:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ee});var s=a(5155),r=a(285),n=a(2346),i=a(3783),o=a(3504),l=a(381),d=a(4835),c=a(2108),u=a(6874),h=a.n(u),x=a(5695);let f=[{title:"Dashboard",href:"/dashboard",icon:i.A},{title:"Projecten",href:"/dashboard/projects",icon:o.A},{title:"Instellingen",href:"/dashboard/settings",icon:l.A}];function m(){let e=(0,x.usePathname)();return(0,s.jsxs)("nav",{className:"flex items-center space-x-4 lg:space-x-6",children:[f.map(t=>(0,s.jsx)(r.$,{variant:e===t.href?"default":"ghost",className:"flex items-center gap-2 transition-all duration-300 ".concat(e===t.href?"glow-accent bg-primary/20 text-primary border-primary/30":"hover:bg-primary/10 hover:text-primary"),asChild:!0,children:(0,s.jsxs)(h(),{href:t.href,children:[(0,s.jsx)(t.icon,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden md:inline-block",children:t.title})]})},t.href)),(0,s.jsx)(n.w,{orientation:"vertical",className:"h-6"}),(0,s.jsxs)(r.$,{variant:"ghost",className:"flex items-center gap-2 hover:bg-destructive/10 hover:text-destructive transition-all duration-300",onClick:()=>(0,c.signOut)(),children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden md:inline-block",children:"Uitloggen"})]})]})}var p=a(4783),v=a(2115),g=a(7655),b=a(9434);function j(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(g.bL,{"data-slot":"scroll-area",className:(0,b.cn)("relative",t),...r,children:[(0,s.jsx)(g.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:a}),(0,s.jsx)(w,{}),(0,s.jsx)(g.OK,{})]})}function w(e){let{className:t,orientation:a="vertical",...r}=e;return(0,s.jsx)(g.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,b.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...r,children:(0,s.jsx)(g.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var N=a(5452),y=a(4416);function k(e){let{...t}=e;return(0,s.jsx)(N.bL,{"data-slot":"sheet",...t})}function z(e){let{...t}=e;return(0,s.jsx)(N.l9,{"data-slot":"sheet-trigger",...t})}function C(e){let{...t}=e;return(0,s.jsx)(N.ZL,{"data-slot":"sheet-portal",...t})}function A(e){let{className:t,...a}=e;return(0,s.jsx)(N.hJ,{"data-slot":"sheet-overlay",className:(0,b.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function _(e){let{className:t,children:a,side:r="right",...n}=e;return(0,s.jsxs)(C,{children:[(0,s.jsx)(A,{}),(0,s.jsxs)(N.UC,{"data-slot":"sheet-content",className:(0,b.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[a,(0,s.jsxs)(N.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(y.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function P(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,b.cn)("flex flex-col gap-1.5 p-4",t),...a})}function $(e){let{className:t,...a}=e;return(0,s.jsx)(N.hE,{"data-slot":"sheet-title",className:(0,b.cn)("text-foreground font-semibold",t),...a})}function L(e){let{items:t}=e,a=(0,x.usePathname)(),[n,i]=v.useState(!1);return(0,s.jsxs)(k,{open:n,onOpenChange:i,children:[(0,s.jsx)(z,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden",children:[(0,s.jsx)(p.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"sr-only",children:"Menu"})]})}),(0,s.jsxs)(_,{side:"left",className:"pr-0",children:[(0,s.jsx)(P,{children:(0,s.jsx)($,{children:"Menu"})}),(0,s.jsx)(j,{className:"my-4 h-[calc(100vh-8rem)] pb-10",children:(0,s.jsx)("div",{className:"flex flex-col space-y-3",children:t.map(e=>(0,s.jsxs)(h(),{href:e.href,onClick:()=>i(!1),className:(0,b.cn)("flex items-center space-x-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",a===e.href?"bg-accent text-accent-foreground":"transparent"),children:[e.icon&&(0,s.jsx)(e.icon,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.title})]},e.href))})})]})]})}var O=a(7924),U=a(2523);function E(){let e=(0,x.useRouter)(),[t,a]=v.useState("");return(0,s.jsxs)("form",{onSubmit:a=>{a.preventDefault(),t.trim()&&e.push("/dashboard/search?q=".concat(encodeURIComponent(t.trim())))},className:"flex w-full max-w-sm items-center space-x-2",children:[(0,s.jsx)(U.p,{type:"search",placeholder:"Zoek projecten...",className:"h-9",value:t,onChange:e=>a(e.target.value)}),(0,s.jsxs)(r.$,{type:"submit",size:"icon",className:"h-9 w-9",children:[(0,s.jsx)(O.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Zoeken"})]})]})}var q=a(6408),D=a(6474),H=a(1007),M=a(1394),S=a(8698);function I(e){let{...t}=e;return(0,s.jsx)(S.bL,{"data-slot":"dropdown-menu",...t})}function Z(e){let{...t}=e;return(0,s.jsx)(S.l9,{"data-slot":"dropdown-menu-trigger",...t})}function K(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(S.ZL,{children:(0,s.jsx)(S.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,b.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function V(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(S.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,b.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function B(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(S.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,b.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function J(e){let{className:t,...a}=e;return(0,s.jsx)(S.wv,{"data-slot":"dropdown-menu-separator",className:(0,b.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function R(){var e;let{data:t}=(0,c.useSession)();if(!(null==t?void 0:t.user))return null;let a=null==(e=t.user.name)?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase();return(0,s.jsxs)(I,{children:[(0,s.jsx)(Z,{asChild:!0,children:(0,s.jsx)(q.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)(r.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:[(0,s.jsxs)(M.eu,{className:"h-8 w-8",children:[(0,s.jsx)(M.BK,{src:t.user.image||"",alt:t.user.name||""}),(0,s.jsx)(M.q5,{children:a})]}),(0,s.jsx)(D.A,{className:"ml-2 h-4 w-4"})]})})}),(0,s.jsxs)(K,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(B,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:t.user.name}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.user.email})]})}),(0,s.jsx)(J,{}),(0,s.jsx)(V,{asChild:!0,children:(0,s.jsx)(q.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start",onClick:()=>window.location.href="/dashboard/profile",children:[(0,s.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profiel"})]})})}),(0,s.jsx)(V,{asChild:!0,children:(0,s.jsx)(q.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start",onClick:()=>window.location.href="/dashboard/settings",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Instellingen"})]})})}),(0,s.jsx)(J,{}),(0,s.jsx)(V,{asChild:!0,children:(0,s.jsx)(q.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950",onClick:()=>(0,c.signOut)(),children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Uitloggen"})]})})})]})]})}var F=a(2098),Q=a(3509),T=a(1362);function W(){let{setTheme:e,theme:t}=(0,T.D)(),[a,n]=v.useState(!1);return(v.useEffect(()=>{n(!0)},[]),a)?(0,s.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>e("light"===t?"dark":"light"),className:"relative h-9 w-9 rounded-md","aria-label":"Wissel thema",children:(0,s.jsx)(q.P.div,{initial:!1,animate:{rotate:180*("light"!==t)},transition:{duration:.3},className:"absolute inset-0 flex items-center justify-center",children:"light"===t?(0,s.jsx)(F.A,{className:"h-5 w-5"}):(0,s.jsx)(Q.A,{className:"h-5 w-5"})})}):null}var X=a(1014),Y=a(6671);let G=[{title:"Dashboard",href:"/dashboard",icon:i.A},{title:"Projecten",href:"/dashboard/projects",icon:o.A},{title:"Instellingen",href:"/dashboard/settings",icon:l.A}];function ee(e){let{children:t}=e;return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-primary/20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center",children:[(0,s.jsx)(L,{items:G}),(0,s.jsx)(m,{}),(0,s.jsxs)("div",{className:"flex flex-1 items-center justify-between space-x-2 md:justify-end",children:[(0,s.jsx)("div",{className:"w-full flex-1 md:w-auto md:flex-none",children:(0,s.jsx)(E,{})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(W,{}),(0,s.jsx)(R,{})]})]})]})}),(0,s.jsx)("main",{className:"flex-1",children:(0,s.jsx)("div",{className:"container py-6",children:(0,s.jsx)(X.g1,{children:(0,s.jsx)(X._A,{children:t})})})}),(0,s.jsx)(Y.l$,{richColors:!0,position:"top-right"})]})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,493,874,852,299,671,396,208,441,684,358],()=>t(1506)),_N_E=e.O()}]);