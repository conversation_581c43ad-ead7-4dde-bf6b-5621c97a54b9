(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[377],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:i,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:a,size:i,className:t})),...o})}},1873:(e,t,a)=>{Promise.resolve().then(a.bind(a,6415))},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>d,TN:()=>l});var s=a(5155);a(2115);var r=a(2085),i=a(9434);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:a}),t),...r})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6415:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var s=a(5155),r=a(5365),i=a(285),n=a(6695),d=a(2523),l=a(9409),o=a(8539),c=a(2108),u=a(5695),p=a(2115);function v(e){let{params:t}=e,a=(0,u.useRouter)(),{data:v}=(0,c.useSession)(),[x,f]=(0,p.useState)(null),[g,h]=(0,p.useState)(!0),[m,b]=(0,p.useState)(!1),[j,y]=(0,p.useState)(null);(0,p.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/projects/".concat(t.id,"/tasks/").concat(t.taskId));if(!e.ok)throw Error("Taak niet gevonden");let a=await e.json();f(a)}catch(e){y(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{h(!1)}})()},[t.id,t.taskId]);let w=async e=>{if(e.preventDefault(),x){b(!0),y(null);try{if(!(await fetch("/api/projects/".concat(t.id,"/tasks/").concat(t.taskId),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(x)})).ok)throw Error("Fout bij het bijwerken van de taak");a.push("/dashboard/projects/".concat(t.id))}catch(e){y(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{b(!1)}}},k=async()=>{if(confirm("Weet je zeker dat je deze taak wilt verwijderen?")){b(!0),y(null);try{if(!(await fetch("/api/projects/".concat(t.id,"/tasks/").concat(t.taskId),{method:"DELETE"})).ok)throw Error("Fout bij het verwijderen van de taak");a.push("/dashboard/projects/".concat(t.id))}catch(e){y(e instanceof Error?e.message:"Er is een fout opgetreden"),b(!1)}}};return g?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):j||!x?(0,s.jsx)(r.Fc,{variant:"destructive",children:(0,s.jsx)(r.TN,{children:j||"Taak niet gevonden"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Taak Bewerken"}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>a.push("/dashboard/projects/".concat(t.id)),children:"Terug"})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Taak Details"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"title",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Titel"}),(0,s.jsx)(d.p,{id:"title",value:x.title,onChange:e=>f({...x,title:e.target.value}),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Beschrijving"}),(0,s.jsx)(o.T,{id:"description",value:x.description||"",onChange:e=>f({...x,description:e.target.value}),rows:4})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"status",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Status"}),(0,s.jsxs)(l.l6,{value:x.status,onValueChange:e=>f({...x,status:e}),children:[(0,s.jsx)(l.bq,{children:(0,s.jsx)(l.yv,{placeholder:"Selecteer status"})}),(0,s.jsxs)(l.gC,{children:[(0,s.jsx)(l.eb,{value:"TODO",children:"Te Doen"}),(0,s.jsx)(l.eb,{value:"IN_PROGRESS",children:"In Behandeling"}),(0,s.jsx)(l.eb,{value:"DONE",children:"Afgerond"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"dueDate",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Deadline"}),(0,s.jsx)(d.p,{id:"dueDate",type:"date",value:x.dueDate?new Date(x.dueDate).toISOString().split("T")[0]:"",onChange:e=>f({...x,dueDate:e.target.value?new Date(e.target.value):null})})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(i.$,{type:"button",variant:"destructive",onClick:k,disabled:m,children:"Verwijderen"}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a.push("/dashboard/projects/".concat(t.id)),children:"Annuleren"}),(0,s.jsx)(i.$,{type:"submit",disabled:m,children:m?"Opslaan...":"Opslaan"})]})]})]})})]})]})}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>c});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>v,gC:()=>p,l6:()=>o,yv:()=>c});var s=a(5155);a(2115);var r=a(2918),i=a(6474),n=a(5196),d=a(7863),l=a(9434);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...d}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.LM,{className:(0,l.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(f,{})]})})}function v(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(d.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,493,852,281,441,684,358],()=>t(1873)),_N_E=e.O()}]);