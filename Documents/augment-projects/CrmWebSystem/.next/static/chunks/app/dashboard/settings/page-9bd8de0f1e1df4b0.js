(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[631],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var i=a(5155);a(2115);var n=a(9708),s=a(2085),r=a(9434);let d=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...c}=e,o=l?n.DX:"button";return(0,i.jsx)(o,{"data-slot":"button",className:(0,r.cn)(d({variant:a,size:s,className:t})),...c})}},333:(e,t,a)=>{"use strict";a.d(t,{d:()=>r});var i=a(5155);a(2115);var n=a(4884),s=a(9434);function r(e){let{className:t,...a}=e;return(0,i.jsx)(n.bL,{"data-slot":"switch",className:(0,s.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,i.jsx)(n.zi,{"data-slot":"switch-thumb",className:(0,s.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},1014:(e,t,a)=>{"use strict";a.d(t,{YE:()=>l,_A:()=>d,g1:()=>c});var i=a(5155),n=a(9434),s=a(6408),r=a(760);function d(e){let{children:t,className:a,delay:r=0,duration:d=.5}=e;return(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:d,delay:r},className:(0,n.cn)(a),children:t})}function l(e){let{children:t,className:a,delay:r=0,duration:d=.5}=e;return(0,i.jsx)(s.P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{duration:d,delay:r},className:(0,n.cn)(a),children:t})}function c(e){let{children:t}=e;return(0,i.jsx)(r.N,{mode:"wait",children:t})}},2346:(e,t,a)=>{"use strict";a.d(t,{w:()=>r});var i=a(5155);a(2115);var n=a(7489),s=a(9434);function r(e){let{className:t,orientation:a="horizontal",decorative:r=!0,...d}=e;return(0,i.jsx)(n.b,{"data-slot":"separator",decorative:r,orientation:a,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...d})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var i=a(5155);a(2115);var n=a(9434);function s(e){let{className:t,type:a,...s}=e;return(0,i.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var i=a(5155);a(2115);var n=a(968),s=a(9434);function r(e){let{className:t,...a}=e;return(0,i.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5149:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var i=a(5155),n=a(5365),s=a(285),r=a(6695),d=a(2523),l=a(5057),c=a(2346),o=a(333),u=a(8539),x=a(1014),h=a(6408),g=a(2108),v=a(1362),m=a(2115),p=a(6671);function f(){let{data:e}=(0,g.useSession)(),{theme:t,setTheme:a}=(0,v.D)(),[f,b]=(0,m.useState)(!1),[j,y]=(0,m.useState)({name:"",email:"",bio:"",notifications:{email:!0,push:!1,marketing:!1}});(0,m.useEffect)(()=>{(null==e?void 0:e.user)&&y(t=>({...t,name:e.user.name||"",email:e.user.email||""}))},[e]);let k=async()=>{b(!0);try{await new Promise(e=>setTimeout(e,1e3)),p.oR.success("Instellingen succesvol opgeslagen!")}catch(e){p.oR.error("Er is iets misgegaan bij het opslaan van de instellingen.")}finally{b(!1)}};return(0,i.jsx)(x.g1,{children:(0,i.jsx)(x._A,{children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"Instellingen"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Beheer je account instellingen en voorkeuren."})]}),(0,i.jsx)(c.w,{}),(0,i.jsxs)("div",{className:"grid gap-6",children:[(0,i.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Profiel"}),(0,i.jsx)(r.BT,{children:"Update je profiel informatie en persoonlijke gegevens."})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid gap-2",children:[(0,i.jsx)(l.J,{htmlFor:"name",children:"Naam"}),(0,i.jsx)(d.p,{id:"name",value:j.name,onChange:e=>y(t=>({...t,name:e.target.value})),placeholder:"Je volledige naam"})]}),(0,i.jsxs)("div",{className:"grid gap-2",children:[(0,i.jsx)(l.J,{htmlFor:"email",children:"E-mail"}),(0,i.jsx)(d.p,{id:"email",type:"email",value:j.email,onChange:e=>y(t=>({...t,email:e.target.value})),placeholder:"<EMAIL>"})]}),(0,i.jsxs)("div",{className:"grid gap-2",children:[(0,i.jsx)(l.J,{htmlFor:"bio",children:"Bio"}),(0,i.jsx)(u.T,{id:"bio",value:j.bio,onChange:e=>y(t=>({...t,bio:e.target.value})),placeholder:"Vertel iets over jezelf...",rows:3})]})]})]})}),(0,i.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Uiterlijk"}),(0,i.jsx)(r.BT,{children:"Pas het thema en uiterlijk van de applicatie aan."})]}),(0,i.jsx)(r.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(l.J,{children:"Donker thema"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Schakel tussen licht en donker thema"})]}),(0,i.jsx)(o.d,{checked:"dark"===t,onCheckedChange:e=>a(e?"dark":"light")})]})})]})}),(0,i.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Notificaties"}),(0,i.jsx)(r.BT,{children:"Configureer hoe en wanneer je notificaties wilt ontvangen."})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(l.J,{children:"E-mail notificaties"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang updates via e-mail"})]}),(0,i.jsx)(o.d,{checked:j.notifications.email,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,email:e}}))})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(l.J,{children:"Push notificaties"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang push notificaties in je browser"})]}),(0,i.jsx)(o.d,{checked:j.notifications.push,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,push:e}}))})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(l.J,{children:"Marketing e-mails"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang nieuws en updates over nieuwe functies"})]}),(0,i.jsx)(o.d,{checked:j.notifications.marketing,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,marketing:e}}))})]})]})]})}),(0,i.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,i.jsxs)(r.Zp,{className:"border-destructive/50",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{className:"text-destructive",children:"Gevaarlijke Zone"}),(0,i.jsx)(r.BT,{children:"Permanente acties die niet ongedaan gemaakt kunnen worden."})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)(n.Fc,{variant:"destructive",children:(0,i.jsx)(n.TN,{children:"Het verwijderen van je account is permanent en kan niet ongedaan worden gemaakt. Alle je projecten en taken zullen verloren gaan."})}),(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(s.$,{variant:"destructive",size:"sm",children:"Account Verwijderen"})})]})]})}),(0,i.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"flex justify-end",children:(0,i.jsx)(s.$,{onClick:k,disabled:f,className:"glow-accent hover:shadow-lg transition-all duration-300",children:f?"Opslaan...":"Instellingen Opslaan"})})]})]})})})}},5347:(e,t,a)=>{Promise.resolve().then(a.bind(a,5149))},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>d,TN:()=>l});var i=a(5155);a(2115);var n=a(2085),s=a(9434);let r=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,...n}=e;return(0,i.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(r({variant:a}),t),...n})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>s,aR:()=>r,wL:()=>o});var i=a(5155);a(2115);var n=a(9434);function s(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function r(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var i=a(5155);a(2115);var n=a(9434);function s(e){let{className:t,...a}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var i=a(2596),n=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,i.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,299,671,424,441,684,358],()=>t(5347)),_N_E=e.O()}]);