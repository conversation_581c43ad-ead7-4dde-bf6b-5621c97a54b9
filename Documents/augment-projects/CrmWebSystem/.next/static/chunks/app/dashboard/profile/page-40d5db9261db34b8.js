(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[399],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...o})}},333:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var s=a(5155);a(2115);var r=a(4884),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var s=a(2115),r=a(3655),n=a(5155),i=s.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},1014:(e,t,a)=>{"use strict";a.d(t,{YE:()=>d,_A:()=>l,g1:()=>o});var s=a(5155),r=a(9434),n=a(6408),i=a(760);function l(e){let{children:t,className:a,delay:i=0,duration:l=.5}=e;return(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:l,delay:i},className:(0,r.cn)(a),children:t})}function d(e){let{children:t,className:a,delay:i=0,duration:l=.5}=e;return(0,s.jsx)(n.P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{duration:l,delay:i},className:(0,r.cn)(a),children:t})}function o(e){let{children:t}=e;return(0,s.jsx)(i.N,{mode:"wait",children:t})}},1394:(e,t,a)=>{"use strict";a.d(t,{BK:()=>l,eu:()=>i,q5:()=>d});var s=a(5155);a(2115);var r=a(4011),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},3316:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>K});var s=a(5155),r=a(6408),n=a(1007),i=a(9946);let l=(0,i.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),d=(0,i.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),o=(0,i.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var c=a(2108),u=a(2115),x=a(6671),m=a(1014),p=a(1394),f=a(285),h=a(6695),v=a(2523),b=a(5057),g=a(333),j=a(5185),y=a(6081),w=a(9196),N=a(8905),k=a(3655),R=a(4315),z=a(5845),C=a(1285),P="Tabs",[_,E]=(0,y.A)(P,[w.RG]),B=(0,w.RG)(),[T,D]=_(P),V=u.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:d,activationMode:o="automatic",...c}=e,u=(0,R.jH)(d),[x,m]=(0,z.i)({prop:r,onChange:n,defaultProp:null!=i?i:"",caller:P});return(0,s.jsx)(T,{scope:a,baseId:(0,C.B)(),value:x,onValueChange:m,orientation:l,dir:u,activationMode:o,children:(0,s.jsx)(k.sG.div,{dir:u,"data-orientation":l,...c,ref:t})})});V.displayName=P;var A="TabsList",I=u.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,i=D(A,a),l=B(a);return(0,s.jsx)(w.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,s.jsx)(k.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});I.displayName=A;var M="TabsTrigger",L=u.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...i}=e,l=D(M,a),d=B(a),o=S(l.baseId,r),c=q(l.baseId,r),u=r===l.value;return(0,s.jsx)(w.q7,{asChild:!0,...d,focusable:!n,active:u,children:(0,s.jsx)(k.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:o,...i,ref:t,onMouseDown:(0,j.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,j.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,j.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;u||n||!e||l.onValueChange(r)})})})});L.displayName=M;var Z="TabsContent",G=u.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,forceMount:n,children:i,...l}=e,d=D(Z,a),o=S(d.baseId,r),c=q(d.baseId,r),x=r===d.value,m=u.useRef(x);return u.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(N.C,{present:n||x,children:a=>{let{present:r}=a;return(0,s.jsx)(k.sG.div,{"data-state":x?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":o,hidden:!r,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})}})});function S(e,t){return"".concat(e,"-trigger-").concat(t)}function q(e,t){return"".concat(e,"-content-").concat(t)}G.displayName=Z;var F=a(9434);function J(e){let{className:t,...a}=e;return(0,s.jsx)(V,{"data-slot":"tabs",className:(0,F.cn)("flex flex-col gap-2",t),...a})}function O(e){let{className:t,...a}=e;return(0,s.jsx)(I,{"data-slot":"tabs-list",className:(0,F.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a})}function W(e){let{className:t,...a}=e;return(0,s.jsx)(L,{"data-slot":"tabs-trigger",className:(0,F.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function H(e){let{className:t,...a}=e;return(0,s.jsx)(G,{"data-slot":"tabs-content",className:(0,F.cn)("flex-1 outline-none",t),...a})}function K(){var e;let{data:t,update:a}=(0,c.useSession)(),[i,j]=(0,u.useState)(!1);if(!(null==t?void 0:t.user))return null;let y=null==(e=t.user.name)?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase(),w=async e=>{e.preventDefault(),j(!0);try{await new Promise(e=>setTimeout(e,1e3)),x.oR.success("Profiel succesvol bijgewerkt")}catch(e){x.oR.error("Er is iets misgegaan bij het bijwerken van je profiel")}finally{j(!1)}};return(0,s.jsx)(m.g1,{children:(0,s.jsx)(m._A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Profiel"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Beheer je account instellingen en voorkeuren"})]}),(0,s.jsxs)(J,{defaultValue:"account",className:"space-y-4",children:[(0,s.jsxs)(O,{children:[(0,s.jsx)(W,{value:"account",children:"Account"}),(0,s.jsx)(W,{value:"notifications",children:"Notificaties"}),(0,s.jsx)(W,{value:"appearance",children:"Weergave"})]}),(0,s.jsx)(H,{value:"account",className:"space-y-4",children:(0,s.jsxs)(h.Zp,{children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsx)(h.ZB,{children:"Profiel Informatie"}),(0,s.jsx)(h.BT,{children:"Update je persoonlijke informatie"})]}),(0,s.jsxs)("form",{onSubmit:w,children:[(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(p.eu,{className:"h-20 w-20",children:[(0,s.jsx)(p.BK,{src:t.user.image||"",alt:t.user.name||""}),(0,s.jsx)(p.q5,{className:"text-lg",children:y})]}),(0,s.jsx)(f.$,{variant:"outline",size:"sm",children:"Verander foto"})]}),(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(b.J,{htmlFor:"name",children:"Naam"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}),(0,s.jsx)(v.p,{id:"name",defaultValue:t.user.name||"",className:"rounded-l-none"})]})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(b.J,{htmlFor:"email",children:"Email"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,s.jsx)(l,{className:"h-4 w-4"})}),(0,s.jsx)(v.p,{id:"email",type:"email",defaultValue:t.user.email||"",className:"rounded-l-none"})]})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(b.J,{htmlFor:"password",children:"Wachtwoord"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,s.jsx)(d,{className:"h-4 w-4"})}),(0,s.jsx)(v.p,{id:"password",type:"password",placeholder:"••••••••",className:"rounded-l-none"})]})]})]})]}),(0,s.jsx)(h.wL,{children:(0,s.jsx)(r.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)(f.$,{type:"submit",disabled:i,children:[(0,s.jsx)(o,{className:"mr-2 h-4 w-4"}),i?"Bezig met opslaan...":"Opslaan"]})})})]})]})}),(0,s.jsx)(H,{value:"notifications",className:"space-y-4",children:(0,s.jsxs)(h.Zp,{children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsx)(h.ZB,{children:"Notificatie Instellingen"}),(0,s.jsx)(h.BT,{children:"Configureer hoe je notificaties wilt ontvangen"})]}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(b.J,{children:"Email Notificaties"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang updates over je projecten"})]}),(0,s.jsx)(g.d,{})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(b.J,{children:"Push Notificaties"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang meldingen in je browser"})]}),(0,s.jsx)(g.d,{})]})]})]})}),(0,s.jsx)(H,{value:"appearance",className:"space-y-4",children:(0,s.jsxs)(h.Zp,{children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsx)(h.ZB,{children:"Weergave Instellingen"}),(0,s.jsx)(h.BT,{children:"Pas de weergave van de applicatie aan"})]}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(b.J,{children:"Donker Modus"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Schakel tussen licht en donker thema"})]}),(0,s.jsx)(g.d,{})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(b.J,{children:"Taal"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Kies je voorkeurstaal"})]}),(0,s.jsxs)("select",{className:"h-9 rounded-md border border-input bg-background px-3 py-1 text-sm",children:[(0,s.jsx)("option",{value:"nl",children:"Nederlands"}),(0,s.jsx)("option",{value:"en",children:"English"})]})]})]})]})})]})]})})})}},4884:(e,t,a)=>{"use strict";a.d(t,{bL:()=>w,zi:()=>N});var s=a(2115),r=a(5185),n=a(6101),i=a(6081),l=a(5845),d=a(5503),o=a(1275),c=a(3655),u=a(5155),x="Switch",[m,p]=(0,i.A)(x),[f,h]=m(x),v=s.forwardRef((e,t)=>{let{__scopeSwitch:a,name:i,checked:d,defaultChecked:o,required:m,disabled:p,value:h="on",onCheckedChange:v,form:b,...g}=e,[w,N]=s.useState(null),k=(0,n.s)(t,e=>N(e)),R=s.useRef(!1),z=!w||b||!!w.closest("form"),[C,P]=(0,l.i)({prop:d,defaultProp:null!=o&&o,onChange:v,caller:x});return(0,u.jsxs)(f,{scope:a,checked:C,disabled:p,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":m,"data-state":y(C),"data-disabled":p?"":void 0,disabled:p,value:h,...g,ref:k,onClick:(0,r.m)(e.onClick,e=>{P(e=>!e),z&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),z&&(0,u.jsx)(j,{control:w,bubbles:!R.current,name:i,value:h,checked:C,required:m,disabled:p,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=x;var b="SwitchThumb",g=s.forwardRef((e,t)=>{let{__scopeSwitch:a,...s}=e,r=h(b,a);return(0,u.jsx)(c.sG.span,{"data-state":y(r.checked),"data-disabled":r.disabled?"":void 0,...s,ref:t})});g.displayName=b;var j=s.forwardRef((e,t)=>{let{__scopeSwitch:a,control:r,checked:i,bubbles:l=!0,...c}=e,x=s.useRef(null),m=(0,n.s)(x,t),p=(0,d.Z)(i),f=(0,o.X)(r);return s.useEffect(()=>{let e=x.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==i&&t){let a=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(a)}},[p,i,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:m,style:{...c.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var w=v,N=g},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(5155);a(2115);var r=a(968),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5503:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var s=a(2115);function r(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},7611:(e,t,a)=>{Promise.resolve().then(a.bind(a,3316))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,493,299,671,396,441,684,358],()=>t(7611)),_N_E=e.O()}]);