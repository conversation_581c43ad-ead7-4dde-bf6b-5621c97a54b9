(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>c});var a=t(5155);t(2115);var s=t(9708),i=t(2085),n=t(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:r,variant:t,size:i,asChild:c=!1,...d}=e,l=c?s.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:i,className:r})),...d})}},1014:(e,r,t)=>{"use strict";t.d(r,{YE:()=>c,_A:()=>o,g1:()=>d});var a=t(5155),s=t(9434),i=t(6408),n=t(760);function o(e){let{children:r,className:t,delay:n=0,duration:o=.5}=e;return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:o,delay:n},className:(0,s.cn)(t),children:r})}function c(e){let{children:r,className:t,delay:n=0,duration:o=.5}=e;return(0,a.jsx)(i.P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{duration:o,delay:n},className:(0,s.cn)(t),children:r})}function d(e){let{children:r}=e;return(0,a.jsx)(n.N,{mode:"wait",children:r})}},5365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>c});var a=t(5155);t(2115);var s=t(2085),i=t(9434);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),r),...s})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",r),...t})}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>c});var a=t(5155);t(2115);var s=t(9708),i=t(2085),n=t(9434);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:r,variant:t,asChild:i=!1,...c}=e,d=i?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),r),...c})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>l});var a=t(5155);t(2115);var s=t(9434);function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},6743:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var a=t(5155),s=t(6408);let i=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var n=t(5695),o=t(2115),c=t(5365),d=t(1014),l=t(6126),u=t(285),v=t(6695),h=t(9434);function g(e){let{className:r,...t}=e;return(0,a.jsx)("div",{className:(0,h.cn)("animate-spin",r),...t,children:(0,a.jsxs)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}function x(){let e=(0,n.useRouter)(),[r,t]=(0,o.useState)([]),[h,x]=(0,o.useState)(!0),[f,m]=(0,o.useState)(null);return((0,o.useEffect)(()=>{!async function(){try{let e=await fetch("/api/projects");if(!e.ok)throw Error("Er is iets misgegaan bij het ophalen van de projecten.");let r=await e.json();t(r)}catch(e){m(e instanceof Error?e.message:"Er is iets misgegaan.")}finally{x(!1)}}()},[]),h)?(0,a.jsx)("div",{className:"flex h-[50vh] items-center justify-center",children:(0,a.jsx)(g,{className:"h-8 w-8"})}):f?(0,a.jsx)(c.Fc,{variant:"destructive",children:(0,a.jsx)(c.TN,{children:f})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"Projecten"}),(0,a.jsx)(s.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(u.$,{onClick:()=>e.push("/dashboard/projects/new"),className:"glow-accent hover:shadow-lg transition-all duration-300",children:[(0,a.jsx)(i,{className:"mr-2 h-4 w-4"}),"Nieuw Project"]})})]}),(0,a.jsx)(d.g1,{children:(0,a.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3",children:r.map((r,t)=>(0,a.jsx)(d.YE,{delay:.1*t,children:(0,a.jsx)(s.P.div,{whileHover:{scale:1.02},transition:{duration:.2},children:(0,a.jsxs)(v.Zp,{className:"h-full border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg",children:[(0,a.jsxs)(v.aR,{children:[(0,a.jsx)(v.ZB,{className:"gradient-text",children:r.name}),(0,a.jsx)(v.BT,{children:r.description})]}),(0,a.jsx)(v.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.E,{variant:"ACTIVE"===r.status?"default":"COMPLETED"===r.status?"secondary":"outline",className:"ACTIVE"===r.status?"bg-primary/20 text-primary border-primary/30":"COMPLETED"===r.status?"bg-secondary/20 text-secondary-foreground border-secondary/30":"border-primary/30 text-primary",children:"ACTIVE"===r.status?"Actief":"COMPLETED"===r.status?"Afgerond":"On Hold"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[r._count.tasks," ",1===r._count.tasks?"taak":"taken"]})]})}),(0,a.jsx)(v.wL,{children:(0,a.jsx)(s.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"w-full",children:(0,a.jsx)(u.$,{variant:"outline",className:"w-full border-primary/30 hover:border-primary/50 hover:bg-primary/10 transition-all duration-300",onClick:()=>e.push("/dashboard/projects/".concat(r.id)),children:"Bekijk Details"})})})]})})},r.id))})})]})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(2596),s=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},9718:(e,r,t)=>{Promise.resolve().then(t.bind(t,6743))},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:l="",children:u,iconNode:v,...h}=e;return(0,a.createElement)("svg",{ref:r,...d,width:s,height:s,stroke:t,strokeWidth:n?24*Number(i)/Number(s):i,className:o("lucide",l),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...v.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:c,...d}=t;return(0,a.createElement)(l,{ref:i,iconNode:r,className:o("lucide-".concat(s(n(e))),"lucide-".concat(e),c),...d})});return t.displayName=n(e),t}}},e=>{var r=r=>e(e.s=r);e.O(0,[352,299,441,684,358],()=>r(9718)),_N_E=e.O()}]);