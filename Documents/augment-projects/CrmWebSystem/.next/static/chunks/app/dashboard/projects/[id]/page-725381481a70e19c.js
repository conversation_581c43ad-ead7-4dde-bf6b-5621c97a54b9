(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(5155);r(2115);var a=r(9708),n=r(2085),i=r(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...o}=e,l=c?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:n,className:t})),...o})}},4344:(e,t,r)=>{Promise.resolve().then(r.bind(r,6264))},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>c});var s=r(5155);r(2115);var a=r(2085),n=r(9434);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...a})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(5155);r(2115);var a=r(9708),n=r(2085),i=r(9434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,asChild:n=!1,...c}=e,o=n?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:r}),t),...c})}},6264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(5155),a=r(5365),n=r(6126),i=r(285),d=r(6695),c=r(2108),o=r(5695),l=r(2115);function u(e){let{params:t}=e,r=(0,o.useRouter)(),{data:u}=(0,c.useSession)(),[v,x]=(0,l.useState)(null),[g,h]=(0,l.useState)(!0),[f,m]=(0,l.useState)(null);return((0,l.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/projects/".concat(t.id));if(!e.ok)throw Error("Project niet gevonden");let r=await e.json();x(r)}catch(e){m(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{h(!1)}})()},[t.id]),g)?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):f||!v?(0,s.jsx)(a.Fc,{variant:"destructive",children:(0,s.jsx)(a.TN,{children:f||"Project niet gevonden"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:v.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Aangemaakt op"," ",new Date(v.createdAt).toLocaleDateString("nl-NL")]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>r.push("/dashboard/projects/".concat(v.id,"/edit")),children:"Bewerken"}),(0,s.jsx)(i.$,{onClick:()=>r.push("/dashboard/projects/".concat(v.id,"/tasks/new")),children:"Nieuwe Taak"})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Status"})}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(n.E,{variant:"ACTIVE"===v.status?"default":"COMPLETED"===v.status?"secondary":"outline",children:"ACTIVE"===v.status?"Actief":"COMPLETED"===v.status?"Afgerond":"Gearchiveerd"})}),v.description&&(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:v.description})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(d.ZB,{children:"Taken"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[v.tasks.length," taken"]})]})}),(0,s.jsx)(d.Wu,{children:0===v.tasks.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Geen taken gevonden"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(i.$,{variant:"link",onClick:()=>r.push("/dashboard/projects/".concat(v.id,"/tasks/new")),children:"Voeg je eerste taak toe"})})]}):(0,s.jsx)("div",{className:"space-y-4",children:v.tasks.map(e=>(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n.E,{variant:"DONE"===e.status?"default":"IN_PROGRESS"===e.status?"secondary":"outline",children:"DONE"===e.status?"Afgerond":"IN_PROGRESS"===e.status?"In Behandeling":"Te Doen"}),e.dueDate&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(e.dueDate).toLocaleDateString("nl-NL")}),(0,s.jsx)(i.$,{variant:"ghost",onClick:()=>r.push("/dashboard/projects/".concat(v.id,"/tasks/").concat(e.id)),children:"Bekijken"})]})]})})},e.id))})})]})]})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>l});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,441,684,358],()=>t(4344)),_N_E=e.O()}]);