(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},881:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>a});var r=n(5155),o=n(2108);function a(e){let{children:t}=e;return(0,r.jsx)(o.<PERSON>,{children:t})}n(1362)},1362:(e,t,n)=>{"use strict";n.d(t,{D:()=>s});var r=n(2115),o="(prefers-color-scheme: dark)",a=r.createContext(void 0),i={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(a))?e:i},l=null,d=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},c=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},4565:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9514:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,4565,23)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,881))}},e=>{var t=t=>e(e.s=t);e.O(0,[543,108,441,684,358],()=>t(9514)),_N_E=e.O()}]);