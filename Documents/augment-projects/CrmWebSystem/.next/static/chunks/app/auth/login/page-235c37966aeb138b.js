(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(5155);t(2115);var s=t(9708),n=t(2085),i=t(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:n,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:r})),...o})}},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(5155);t(2115);var s=t(9434);function n(e){let{className:r,type:t,...n}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},4177:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var a=t(5155),s=t(5365),n=t(285),i=t(6695),d=t(2523),l=t(2108),o=t(6874),c=t.n(o),u=t(5695),g=t(2115);function v(){let e=(0,u.useRouter)(),r=(0,u.useSearchParams)(),[t,o]=(0,g.useState)(null),[v,x]=(0,g.useState)(null),[f,m]=(0,g.useState)(!1);async function p(r){r.preventDefault(),o(null),x(null),m(!0);let t=new FormData(r.currentTarget),a=t.get("email"),s=t.get("password");try{let r=await (0,l.signIn)("credentials",{email:a,password:s,redirect:!1});if(null==r?void 0:r.error)return void o(r.error);e.push("/dashboard"),e.refresh()}catch(e){o("Er is iets misgegaan. Probeer het later opnieuw.")}finally{m(!1)}}return(0,g.useEffect)(()=>{"true"===r.get("verified")&&x("Je email adres is geverifieerd. Je kunt nu inloggen.")},[r]),(0,a.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,a.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(i.aR,{className:"space-y-1",children:[(0,a.jsx)(i.ZB,{className:"text-2xl text-center",children:"Inloggen"}),(0,a.jsx)(i.BT,{className:"text-center",children:"Voer je gegevens in om in te loggen"})]}),(0,a.jsxs)("form",{onSubmit:p,children:[(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[t&&(0,a.jsx)(s.Fc,{variant:"destructive",children:(0,a.jsx)(s.TN,{children:t})}),v&&(0,a.jsx)(s.Fc,{children:(0,a.jsx)(s.TN,{children:v})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(d.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",required:!0})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(d.p,{id:"password",name:"password",type:"password",placeholder:"••••••••",required:!0})})]}),(0,a.jsxs)(i.wL,{className:"flex flex-col space-y-4",children:[(0,a.jsx)(n.$,{type:"submit",className:"w-full",disabled:f,children:f?"Bezig met inloggen...":"Inloggen"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 text-center text-sm text-muted-foreground",children:[(0,a.jsx)(c(),{href:"/auth/forgot-password",className:"text-primary hover:underline",children:"Wachtwoord vergeten?"}),(0,a.jsxs)("p",{children:["Nog geen account?"," ",(0,a.jsx)(c(),{href:"/auth/register",className:"text-primary hover:underline",children:"Registreer hier"})]})]})]})]})]})})}},5365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>l});var a=t(5155);t(2115);var s=t(2085),n=t(9434);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),r),...s})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",r),...t})}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var a=t(5155);t(2115);var s=t(9434);function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},9043:(e,r,t)=>{Promise.resolve().then(t.bind(t,4177))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[352,108,874,441,684,358],()=>r(9043)),_N_E=e.O()}]);