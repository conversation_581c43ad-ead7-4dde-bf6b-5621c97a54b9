(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[89],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(5155);r(2115);var a=r(9708),n=r(2085),i=r(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,l=o?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:n,className:t})),...c})}},2141:(e,t,r)=>{Promise.resolve().then(r.bind(r,5311))},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(5155),a=r(5365),n=r(285),i=r(6695),d=r(2523),o=r(6874),c=r.n(o),l=r(5695),u=r(2115);function x(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)().get("token"),[r,o]=(0,u.useState)(null),[x,g]=(0,u.useState)(null),[f,h]=(0,u.useState)(!1);async function v(r){r.preventDefault(),o(null),g(null),h(!0);let s=new FormData(r.currentTarget),a=s.get("password");if(a!==s.get("confirmPassword")){o("Wachtwoorden komen niet overeen"),h(!1);return}try{let r=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:t,password:a})}),s=await r.json();if(!r.ok)throw Error(s.message||"Er is iets misgegaan bij het resetten van je wachtwoord");g("Je wachtwoord is succesvol gewijzigd"),setTimeout(()=>{e.push("/auth/login")},2e3)}catch(e){o(e.message)}finally{h(!1)}}return t?(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.aR,{className:"space-y-1",children:[(0,s.jsx)(i.ZB,{className:"text-2xl text-center",children:"Wachtwoord resetten"}),(0,s.jsx)(i.BT,{className:"text-center",children:"Voer je nieuwe wachtwoord in"})]}),(0,s.jsxs)("form",{onSubmit:v,children:[(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[r&&(0,s.jsx)(a.Fc,{variant:"destructive",children:(0,s.jsx)(a.TN,{children:r})}),x&&(0,s.jsx)(a.Fc,{children:(0,s.jsx)(a.TN,{children:x})}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(d.p,{id:"password",name:"password",type:"password",placeholder:"Nieuw wachtwoord",required:!0})}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(d.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Bevestig nieuw wachtwoord",required:!0})})]}),(0,s.jsxs)(i.wL,{className:"flex flex-col space-y-4",children:[(0,s.jsx)(n.$,{type:"submit",className:"w-full",disabled:f,children:f?"Bezig met resetten...":"Wachtwoord resetten"}),(0,s.jsxs)("p",{className:"text-sm text-center text-muted-foreground",children:["Terug naar"," ",(0,s.jsx)(c(),{href:"/auth/login",className:"text-primary hover:underline",children:"inloggen"})]})]})]})]})}):(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.aR,{className:"space-y-1",children:[(0,s.jsx)(i.ZB,{className:"text-2xl text-center",children:"Ongeldige link"}),(0,s.jsx)(i.BT,{className:"text-center",children:"Deze wachtwoord reset link is ongeldig of verlopen"})]}),(0,s.jsx)(i.wL,{children:(0,s.jsx)(n.$,{className:"w-full",onClick:()=>e.push("/auth/forgot-password"),children:"Nieuwe reset link aanvragen"})})]})})}},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>o});var s=r(5155);r(2115);var a=r(2085),n=r(9434);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...a})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>l});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,874,441,684,358],()=>t(2141)),_N_E=e.O()}]);