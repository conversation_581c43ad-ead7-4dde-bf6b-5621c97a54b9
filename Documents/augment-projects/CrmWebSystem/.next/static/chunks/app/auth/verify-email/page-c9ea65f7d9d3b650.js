(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[270],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(5155);t(2115);var s=t(9708),n=t(2085),i=t(9434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:n,asChild:d=!1,...l}=e,o=d?s.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(c({variant:t,size:n,className:r})),...l})}},1392:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(5155),s=t(285),n=t(6695),i=t(5695),c=t(2115);function d(){let e=(0,i.useRouter)(),r=(0,i.useSearchParams)().get("token"),[t,d]=(0,c.useState)(null),[l,o]=(0,c.useState)(!0);return((0,c.useEffect)(()=>{!async function(){if(!r){d("Ongeldige verificatie link"),o(!1);return}try{let t=await fetch("/api/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:r})}),a=await t.json();if(!t.ok)throw Error(a.message||"Er is iets misgegaan bij het verifi\xebren van je email");setTimeout(()=>{e.push("/auth/login?verified=true")},2e3)}catch(e){d(e.message)}finally{o(!1)}}()},[r,e]),l)?(0,a.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,a.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(n.aR,{className:"space-y-1",children:[(0,a.jsx)(n.ZB,{className:"text-2xl text-center",children:"Email verifi\xebren"}),(0,a.jsx)(n.BT,{className:"text-center",children:"Bezig met het verifi\xebren van je email adres..."})]})})}):t?(0,a.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,a.jsxs)(n.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(n.aR,{className:"space-y-1",children:[(0,a.jsx)(n.ZB,{className:"text-2xl text-center",children:"Verificatie mislukt"}),(0,a.jsx)(n.BT,{className:"text-center",children:t})]}),(0,a.jsx)(n.wL,{className:"flex flex-col space-y-4",children:(0,a.jsx)(s.$,{className:"w-full",onClick:()=>e.push("/auth/login"),children:"Terug naar inloggen"})})]})}):(0,a.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,a.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(n.aR,{className:"space-y-1",children:[(0,a.jsx)(n.ZB,{className:"text-2xl text-center",children:"Email geverifieerd"}),(0,a.jsx)(n.BT,{className:"text-center",children:"Je email adres is succesvol geverifieerd. Je wordt doorgestuurd naar de login pagina..."})]})})})}},3520:(e,r,t)=>{Promise.resolve().then(t.bind(t,1392))},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>i,wL:()=>o});var a=t(5155);t(2115);var s=t(9434);function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[352,441,684,358],()=>r(3520)),_N_E=e.O()}]);