(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:n,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:n,className:t})),...o})}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>l});var a=r(5155);r(2115);var s=r(2085),n=r(9434);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...s})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},6195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(5155),s=r(5365),n=r(285),i=r(6695),d=r(2523),l=r(6874),o=r.n(l),c=r(2115);function u(){let[e,t]=(0,c.useState)(null),[r,l]=(0,c.useState)(null),[u,g]=(0,c.useState)(!1);async function v(e){e.preventDefault(),t(null),l(null),g(!0);let r=new FormData(e.currentTarget).get("email");try{let e=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r})}),t=await e.json();if(!e.ok)throw Error(t.message||"Er is iets misgegaan bij het aanvragen van de reset link");l("Als er een account bestaat met dit email adres, ontvang je een reset link")}catch(e){t(e.message)}finally{g(!1)}}return(0,a.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,a.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(i.aR,{className:"space-y-1",children:[(0,a.jsx)(i.ZB,{className:"text-2xl text-center",children:"Wachtwoord vergeten"}),(0,a.jsx)(i.BT,{className:"text-center",children:"Voer je email adres in om een reset link te ontvangen"})]}),(0,a.jsxs)("form",{onSubmit:v,children:[(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[e&&(0,a.jsx)(s.Fc,{variant:"destructive",children:(0,a.jsx)(s.TN,{children:e})}),r&&(0,a.jsx)(s.Fc,{children:(0,a.jsx)(s.TN,{children:r})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(d.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",required:!0})})]}),(0,a.jsxs)(i.wL,{className:"flex flex-col space-y-4",children:[(0,a.jsx)(n.$,{type:"submit",className:"w-full",disabled:u,children:u?"Bezig met versturen...":"Reset link aanvragen"}),(0,a.jsxs)("p",{className:"text-sm text-center text-muted-foreground",children:["Terug naar"," ",(0,a.jsx)(o(),{href:"/auth/login",className:"text-primary hover:underline",children:"inloggen"})]})]})]})]})})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},9353:(e,t,r)=>{Promise.resolve().then(r.bind(r,6195))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,874,441,684,358],()=>t(9353)),_N_E=e.O()}]);