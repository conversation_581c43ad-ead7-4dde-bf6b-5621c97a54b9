{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/refine.ts"], "sourcesContent": ["import { DataProvider } from '@refinedev/core';\n\n// Custom data provider for our CRM system\nexport const dataProvider: DataProvider = {\n  getList: async ({ resource, pagination, filters, sorters, meta }) => {\n    const url = new URL(`/api/${resource}`, window.location.origin);\n    \n    // Add pagination\n    if (pagination) {\n      url.searchParams.append('page', String(pagination.current || 1));\n      url.searchParams.append('limit', String(pagination.pageSize || 10));\n    }\n    \n    // Add filters\n    if (filters) {\n      filters.forEach((filter) => {\n        if (filter.operator === 'eq' && filter.value) {\n          url.searchParams.append(filter.field, String(filter.value));\n        }\n      });\n    }\n    \n    // Add sorting\n    if (sorters && sorters.length > 0) {\n      const sorter = sorters[0];\n      url.searchParams.append('sortBy', sorter.field);\n      url.searchParams.append('sortOrder', sorter.order || 'asc');\n    }\n\n    const response = await fetch(url.toString());\n    const data = await response.json();\n\n    return {\n      data: data.data || data,\n      total: data.total || data.length || 0,\n    };\n  },\n\n  getOne: async ({ resource, id }) => {\n    const response = await fetch(`/api/${resource}/${id}`);\n    const data = await response.json();\n    \n    return {\n      data,\n    };\n  },\n\n  create: async ({ resource, variables }) => {\n    const response = await fetch(`/api/${resource}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(variables),\n    });\n    \n    const data = await response.json();\n    \n    return {\n      data,\n    };\n  },\n\n  update: async ({ resource, id, variables }) => {\n    const response = await fetch(`/api/${resource}/${id}`, {\n      method: 'PATCH',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(variables),\n    });\n    \n    const data = await response.json();\n    \n    return {\n      data,\n    };\n  },\n\n  deleteOne: async ({ resource, id }) => {\n    const response = await fetch(`/api/${resource}/${id}`, {\n      method: 'DELETE',\n    });\n    \n    const data = await response.json();\n    \n    return {\n      data,\n    };\n  },\n\n  getApiUrl: () => '/api',\n};\n\n// Resource definitions for our CRM\nexport const resources = [\n  {\n    name: 'projects',\n    list: '/dashboard/projects',\n    create: '/dashboard/projects/new',\n    edit: '/dashboard/projects/:id/edit',\n    show: '/dashboard/projects/:id',\n    meta: {\n      canDelete: true,\n    },\n  },\n  {\n    name: 'tasks',\n    list: '/dashboard/tasks',\n    create: '/dashboard/tasks/new',\n    edit: '/dashboard/tasks/:id/edit',\n    show: '/dashboard/tasks/:id',\n    meta: {\n      canDelete: true,\n    },\n  },\n];\n"], "names": [], "mappings": ";;;;AAGO,MAAM,eAA6B;IACxC,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE;QAC9D,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,QAAQ,CAAC,MAAM;QAE9D,iBAAiB;QACjB,IAAI,YAAY;YACd,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ,OAAO,WAAW,OAAO,IAAI;YAC7D,IAAI,YAAY,CAAC,MAAM,CAAC,SAAS,OAAO,WAAW,QAAQ,IAAI;QACjE;QAEA,cAAc;QACd,IAAI,SAAS;YACX,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,OAAO,QAAQ,KAAK,QAAQ,OAAO,KAAK,EAAE;oBAC5C,IAAI,YAAY,CAAC,MAAM,CAAC,OAAO,KAAK,EAAE,OAAO,OAAO,KAAK;gBAC3D;YACF;QACF;QAEA,cAAc;QACd,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,IAAI,YAAY,CAAC,MAAM,CAAC,UAAU,OAAO,KAAK;YAC9C,IAAI,YAAY,CAAC,MAAM,CAAC,aAAa,OAAO,KAAK,IAAI;QACvD;QAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ;QACzC,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,MAAM,KAAK,IAAI,IAAI;YACnB,OAAO,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI;QACtC;IACF;IAEA,QAAQ,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;QAC7B,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,IAAI;QACrD,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL;QACF;IACF;IAEA,QAAQ,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE;QACpC,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE;YAC/C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL;QACF;IACF;IAEA,QAAQ,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE;QACxC,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL;QACF;IACF;IAEA,WAAW,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;QAChC,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE;YACrD,QAAQ;QACV;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL;QACF;IACF;IAEA,WAAW,IAAM;AACnB;AAGO,MAAM,YAAY;IACvB;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;YACJ,WAAW;QACb;IACF;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;YACJ,WAAW;QACb;IACF;CACD", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/providers/RefineProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { Refine } from '@refinedev/core';\nimport routerProvider from '@refinedev/nextjs-router';\nimport { dataProvider, resources } from '@/lib/refine';\nimport { ReactNode } from 'react';\n\ninterface RefineProviderProps {\n  children: ReactNode;\n}\n\nexport function RefineProvider({ children }: RefineProviderProps) {\n  return (\n    <Refine\n      dataProvider={dataProvider}\n      routerProvider={routerProvider}\n      resources={resources}\n      options={{\n        syncWithLocation: true,\n        warnWhenUnsavedChanges: true,\n        useNewQueryKeys: true,\n        projectId: 'crm-web-system',\n      }}\n    >\n      {children}\n    </Refine>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,6LAAC,wJAAA,CAAA,SAAM;QACL,cAAc,uHAAA,CAAA,eAAY;QAC1B,gBAAgB,oKAAA,CAAA,UAAc;QAC9B,WAAW,uHAAA,CAAA,YAAS;QACpB,SAAS;YACP,kBAAkB;YAClB,wBAAwB;YACxB,iBAAiB;YACjB,WAAW;QACb;kBAEC;;;;;;AAGP;KAhBgB", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { RefineProvider } from '@/components/providers/RefineProvider';\nimport { SessionProvider } from 'next-auth/react';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      <RefineProvider>\n        {children}\n      </RefineProvider>\n    </SessionProvider>\n  );\n}\n\nexport function ThemeProvider({ children, ...props }: React.ComponentProps<typeof NextThemesProvider>) {\n  return (\n    <NextThemesProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      enableSystem\n      disableTransitionOnChange\n      {...props}\n    >\n      {children}\n    </NextThemesProvider>\n  );\n} "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,6LAAC,iJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,oJAAA,CAAA,iBAAc;sBACZ;;;;;;;;;;;AAIT;KARgB;AAUT,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAwD;IACnG,qBACE,6LAAC,mJAAA,CAAA,gBAAkB;QACjB,WAAU;QACV,cAAa;QACb,YAAY;QACZ,yBAAyB;QACxB,GAAG,KAAK;kBAER;;;;;;AAGP;MAZgB", "debugId": null}}]}