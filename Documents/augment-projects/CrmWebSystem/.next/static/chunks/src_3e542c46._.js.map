{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/%5Bid%5D/tasks/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useRef, useState } from 'react';\nimport { toast } from 'sonner';\n\ninterface TaskFormData {\n  title: string;\n  description: string;\n  status: 'TODO' | 'IN_PROGRESS' | 'DONE';\n  dueDate: string;\n}\n\nexport default function NewTaskPage({\n  params,\n}: {\n  params: { id: string };\n}) {\n  const router = useRouter();\n  const { data: session } = useSession();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState<TaskFormData>({\n    title: '',\n    description: '',\n    status: 'TODO',\n    dueDate: '',\n  });\n\n  // State en ref voor spraakherkenning\n  const [isListening, setIsListening] = useState(false);\n  const recognitionRef = useRef<SpeechRecognition | null>(null);\n\n  const startListening = () => {\n    if (!('webkitSpeechRecognition' in window)) {\n      toast.error(\n        'Spraakherkenning wordt niet ondersteund in deze browser.'\n      );\n      return;\n    }\n\n    const SpeechRecognition = (window as any).webkitSpeechRecognition;\n    const recognition = new SpeechRecognition();\n    recognition.continuous = false; // Luister één keer\n    recognition.interimResults = false; // Geef alleen definitieve resultaten\n    recognition.lang = 'nl-NL'; // Taal instellen op Nederlands\n\n    recognition.onstart = () => {\n      setIsListening(true);\n      toast.info('Luisteren gestart...');\n    };\n\n    recognition.onresult = (event: SpeechRecognitionEvent) => {\n      const transcript = event.results[0][0].transcript;\n      setFormData((prev) => ({\n        ...prev,\n        title: transcript, // Zet de getranscribeerde tekst als titel\n      }));\n      toast.success(`Spraak herkend: \"${transcript}\"`);\n    };\n\n    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {\n      setIsListening(false);\n      setError(`Spraakherkenning fout: ${event.error}`);\n      toast.error(`Spraakherkenning fout: ${event.error}`);\n    };\n\n    recognition.onend = () => {\n      setIsListening(false);\n      toast.info('Luisteren gestopt.');\n    };\n\n    recognitionRef.current = recognition;\n    recognition.start();\n  };\n\n  const stopListening = () => {\n    if (recognitionRef.current) {\n      recognitionRef.current.stop();\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      const response = await fetch(`/api/projects/${params.id}/tasks`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (!response.ok) {\n        // Probeer de foutmelding van de server te lezen\n        const errorData = await response.json();\n        const errorMessage = errorData.error || 'Fout bij het aanmaken van de taak';\n        throw new Error(errorMessage);\n      }\n\n      router.push(`/dashboard/projects/${params.id}`);\n      toast.success('Taak succesvol aangemaakt!');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Er is een fout opgetreden');\n      toast.error(err instanceof Error ? err.message : 'Er is een fout opgetreden');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-2xl font-semibold tracking-tight\">\n          Nieuwe Taak\n        </h1>\n        <Button\n          variant=\"outline\"\n          onClick={() => router.push(`/dashboard/projects/${params.id}`)}\n        >\n          Terug\n        </Button>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Taak Details</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"title\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Titel\n              </label>\n              <div className=\"flex space-x-2\">\n                <Input\n                  id=\"title\"\n                  value={formData.title}\n                  onChange={(e) =>\n                    setFormData({ ...formData, title: e.target.value })\n                  }\n                  required\n                />\n                <button\n                  type=\"button\"\n                  style={{ padding: '8px 12px', border: '1px solid grey', borderRadius: '4px' }}\n                  onClick={isListening ? stopListening : startListening}\n                  disabled={isSubmitting}\n                >\n                  {isListening ? (\n                    'Stop Opnemen'\n                  ) : (\n                    'Microfoon'\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"description\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Beschrijving\n              </label>\n              <Textarea\n                id=\"description\"\n                value={formData.description}\n                onChange={(e) =>\n                  setFormData({ ...formData, description: e.target.value })\n                }\n                rows={4}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"status\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Status\n              </label>\n              <Select\n                value={formData.status}\n                onValueChange={(value: 'TODO' | 'IN_PROGRESS' | 'DONE') =>\n                  setFormData({ ...formData, status: value })\n                }\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Selecteer status\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"TODO\">Te Doen</SelectItem>\n                  <SelectItem value=\"IN_PROGRESS\">In Behandeling</SelectItem>\n                  <SelectItem value=\"DONE\">Afgerond</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"dueDate\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Deadline\n              </label>\n              <Input\n                id=\"dueDate\"\n                type=\"date\"\n                value={formData.dueDate}\n                onChange={(e) =>\n                  setFormData({ ...formData, dueDate: e.target.value })\n                }\n              />\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => router.push(`/dashboard/projects/${params.id}`)}\n              >\n                Annuleren\n              </Button>\n              <Button type=\"submit\" disabled={isSubmitting}>\n                {isSubmitting ? 'Aanmaken...' : 'Aanmaken'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAoBe,SAAS,YAAY,EAClC,MAAM,EAGP;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;IACX;IAEA,qCAAqC;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA4B;IAExD,MAAM,iBAAiB;QACrB,IAAI,CAAC,CAAC,6BAA6B,MAAM,GAAG;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;YAEF;QACF;QAEA,MAAM,oBAAoB,AAAC,OAAe,uBAAuB;QACjE,MAAM,cAAc,IAAI;QACxB,YAAY,UAAU,GAAG,OAAO,mBAAmB;QACnD,YAAY,cAAc,GAAG,OAAO,qCAAqC;QACzE,YAAY,IAAI,GAAG,SAAS,+BAA+B;QAE3D,YAAY,OAAO,GAAG;YACpB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;QAEA,YAAY,QAAQ,GAAG,CAAC;YACtB,MAAM,aAAa,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;YACjD,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,OAAO;gBACT,CAAC;YACD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;QACjD;QAEA,YAAY,OAAO,GAAG,CAAC;YACrB,eAAe;YACf,SAAS,CAAC,uBAAuB,EAAE,MAAM,KAAK,EAAE;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,KAAK,EAAE;QACrD;QAEA,YAAY,KAAK,GAAG;YAClB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;QAEA,eAAe,OAAO,GAAG;QACzB,YAAY,KAAK;IACnB;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,IAAI;QAC7B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,gDAAgD;gBAChD,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,eAAe,UAAU,KAAK,IAAI;gBACxC,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,EAAE,EAAE;YAC9C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG;QACnD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,EAAE,EAAE;kCAC9D;;;;;;;;;;;;0BAKH,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;8CAIvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IACT,YAAY;4DAAE,GAAG,QAAQ;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAEnD,QAAQ;;;;;;8DAEV,6LAAC;oDACC,MAAK;oDACL,OAAO;wDAAE,SAAS;wDAAY,QAAQ;wDAAkB,cAAc;oDAAM;oDAC5E,SAAS,cAAc,gBAAgB;oDACvC,UAAU;8DAET,cACC,iBAEA;;;;;;;;;;;;;;;;;;8CAMR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IACT,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAEzD,MAAM;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO,SAAS,MAAM;4CACtB,eAAe,CAAC,QACd,YAAY;oDAAE,GAAG,QAAQ;oDAAE,QAAQ;gDAAM;;8DAG3C,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IACT,YAAY;oDAAE,GAAG,QAAQ;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAKzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,EAAE,EAAE;sDAC9D;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;sDAC7B,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAxOwB;;QAKP,qIAAA,CAAA,YAAS;QACE,iJAAA,CAAA,aAAU;;;KANd", "debugId": null}}]}