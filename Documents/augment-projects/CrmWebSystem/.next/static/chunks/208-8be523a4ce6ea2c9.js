"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[208],{381:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1362:(e,r,t)=>{t.d(r,{D:()=>i});var n=t(2115),o="(prefers-color-scheme: dark)",l=n.createContext(void 0),a={setTheme:e=>{},themes:[]},i=()=>{var e;return null!=(e=n.useContext(l))?e:a},s=null,u=(e,r)=>{let t;try{t=localStorage.getItem(e)||void 0}catch(e){}return t||r},d=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},c=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},2098:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},3504:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("folder-kanban",[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z",key:"1fr9dc"}],["path",{d:"M8 10v4",key:"tgpxqk"}],["path",{d:"M12 10v2",key:"hh53o1"}],["path",{d:"M16 10v6",key:"1d6xys"}]])},3509:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3783:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},4416:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5452:(e,r,t)=>{t.d(r,{UC:()=>er,ZL:()=>$,bL:()=>J,bm:()=>en,hE:()=>et,hJ:()=>ee,l9:()=>Q});var n=t(2115),o=t(5185),l=t(6101),a=t(6081),i=t(1285),s=t(5845),u=t(9178),d=t(7900),c=t(4378),p=t(8905),f=t(3655),h=t(2293),v=t(3795),m=t(8168),w=t(9708),g=t(5155),y="Dialog",[x,b]=(0,a.A)(y),[C,R]=x(y),j=e=>{let{__scopeDialog:r,children:t,open:o,defaultOpen:l,onOpenChange:a,modal:u=!0}=e,d=n.useRef(null),c=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:y});return(0,g.jsx)(C,{scope:r,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:t})};j.displayName=y;var D="DialogTrigger",S=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,a=R(D,t),i=(0,l.s)(r,a.triggerRef);return(0,g.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":B(a.open),...n,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});S.displayName=D;var E="DialogPortal",[k,M]=x(E,{forceMount:void 0}),_=e=>{let{__scopeDialog:r,forceMount:t,children:o,container:l}=e,a=R(E,r);return(0,g.jsx)(k,{scope:r,forceMount:t,children:n.Children.map(o,e=>(0,g.jsx)(p.C,{present:t||a.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};_.displayName=E;var T="DialogOverlay",P=n.forwardRef((e,r)=>{let t=M(T,e.__scopeDialog),{forceMount:n=t.forceMount,...o}=e,l=R(T,e.__scopeDialog);return l.modal?(0,g.jsx)(p.C,{present:n||l.open,children:(0,g.jsx)(L,{...o,ref:r})}):null});P.displayName=T;var A=(0,w.TL)("DialogOverlay.RemoveScroll"),L=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R(T,t);return(0,g.jsx)(v.A,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(f.sG.div,{"data-state":B(o.open),...n,ref:r,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",N=n.forwardRef((e,r)=>{let t=M(I,e.__scopeDialog),{forceMount:n=t.forceMount,...o}=e,l=R(I,e.__scopeDialog);return(0,g.jsx)(p.C,{present:n||l.open,children:l.modal?(0,g.jsx)(O,{...o,ref:r}):(0,g.jsx)(F,{...o,ref:r})})});N.displayName=I;var O=n.forwardRef((e,r)=>{let t=R(I,e.__scopeDialog),a=n.useRef(null),i=(0,l.s)(r,t.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,m.Eq)(e)},[]),(0,g.jsx)(G,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),null==(r=t.triggerRef.current)||r.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;(2===r.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,r)=>{let t=R(I,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,g.jsx)(G,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var n,a;null==(n=e.onCloseAutoFocus)||n.call(e,r),r.defaultPrevented||(o.current||null==(a=t.triggerRef.current)||a.focus(),r.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:r=>{var n,a;null==(n=e.onInteractOutside)||n.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(l.current=!0));let i=r.target;(null==(a=t.triggerRef.current)?void 0:a.contains(i))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&l.current&&r.preventDefault()}})}),G=n.forwardRef((e,r)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=R(I,t),p=n.useRef(null),f=(0,l.s)(r,p);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,g.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":B(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Y,{titleId:c.titleId}),(0,g.jsx)(Z,{contentRef:p,descriptionId:c.descriptionId})]})]})}),z="DialogTitle",H=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R(z,t);return(0,g.jsx)(f.sG.h2,{id:o.titleId,...n,ref:r})});H.displayName=z;var U="DialogDescription";n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R(U,t);return(0,g.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:r})}).displayName=U;var K="DialogClose",W=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,l=R(K,t);return(0,g.jsx)(f.sG.button,{type:"button",...n,ref:r,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function B(e){return e?"open":"closed"}W.displayName=K;var V="DialogTitleWarning",[X,q]=(0,a.q)(V,{contentName:I,titleName:z,docsSlug:"dialog"}),Y=e=>{let{titleId:r}=e,t=q(V),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return n.useEffect(()=>{r&&(document.getElementById(r)||console.error(o))},[o,r]),null},Z=e=>{let{contentRef:r,descriptionId:t}=e,o=q("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=r.current)?void 0:e.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(l))},[l,r,t]),null},J=j,Q=S,$=_,ee=P,er=N,et=H,en=W},7489:(e,r,t)=>{t.d(r,{b:()=>u});var n=t(2115),o=t(3655),l=t(5155),a="horizontal",i=["horizontal","vertical"],s=n.forwardRef((e,r)=>{var t;let{decorative:n,orientation:s=a,...u}=e,d=(t=s,i.includes(t))?s:a;return(0,l.jsx)(o.sG.div,{"data-orientation":d,...n?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:r})});s.displayName="Separator";var u=s},7655:(e,r,t)=>{t.d(r,{LM:()=>q,OK:()=>Y,VM:()=>R,bL:()=>X,lr:()=>L});var n=t(2115),o=t(3655),l=t(8905),a=t(6081),i=t(6101),s=t(9033),u=t(4315),d=t(2712),c=t(9367),p=t(5185),f=t(5155),h="ScrollArea",[v,m]=(0,a.A)(h),[w,g]=v(h),y=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:l="hover",dir:a,scrollHideDelay:s=600,...d}=e,[c,p]=n.useState(null),[h,v]=n.useState(null),[m,g]=n.useState(null),[y,x]=n.useState(null),[b,C]=n.useState(null),[R,j]=n.useState(0),[D,S]=n.useState(0),[E,k]=n.useState(!1),[M,_]=n.useState(!1),T=(0,i.s)(r,e=>p(e)),P=(0,u.jH)(a);return(0,f.jsx)(w,{scope:t,type:l,dir:P,scrollHideDelay:s,scrollArea:c,viewport:h,onViewportChange:v,content:m,onContentChange:g,scrollbarX:y,onScrollbarXChange:x,scrollbarXEnabled:E,onScrollbarXEnabledChange:k,scrollbarY:b,onScrollbarYChange:C,scrollbarYEnabled:M,onScrollbarYEnabledChange:_,onCornerWidthChange:j,onCornerHeightChange:S,children:(0,f.jsx)(o.sG.div,{dir:P,...d,ref:T,style:{position:"relative","--radix-scroll-area-corner-width":R+"px","--radix-scroll-area-corner-height":D+"px",...e.style}})})});y.displayName=h;var x="ScrollAreaViewport",b=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:l,nonce:a,...s}=e,u=g(x,t),d=n.useRef(null),c=(0,i.s)(r,d,u.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});b.displayName=x;var C="ScrollAreaScrollbar",R=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=g(C,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?a(!0):i(!0),()=>{s?a(!1):i(!1)}),[s,a,i]),"hover"===l.type?(0,f.jsx)(j,{...o,ref:r,forceMount:t}):"scroll"===l.type?(0,f.jsx)(D,{...o,ref:r,forceMount:t}):"auto"===l.type?(0,f.jsx)(S,{...o,ref:r,forceMount:t}):"always"===l.type?(0,f.jsx)(E,{...o,ref:r}):null});R.displayName=C;var j=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,a=g(C,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=a.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},n=()=>{r=window.setTimeout(()=>s(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(l.C,{present:t||i,children:(0,f.jsx)(S,{"data-state":i?"visible":"hidden",...o,ref:r})})}),D=n.forwardRef((e,r)=>{var t,o;let{forceMount:a,...i}=e,s=g(C,e.__scopeScrollArea),u="horizontal"===e.orientation,d=B(()=>h("SCROLL_END"),100),[c,h]=(t="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,r)=>{let t=o[e][r];return null!=t?t:e},t));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,h]),n.useEffect(()=>{let e=s.viewport,r=u?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(h("SCROLL"),d()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,u,h,d]),(0,f.jsx)(l.C,{present:a||"hidden"!==c,children:(0,f.jsx)(E,{"data-state":"hidden"===c?"hidden":"visible",...i,ref:r,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),S=n.forwardRef((e,r)=>{let t=g(C,e.__scopeScrollArea),{forceMount:o,...a}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,d=B(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(u?e:r)}},10);return V(t.viewport,d),V(t.content,d),(0,f.jsx)(l.C,{present:o||i,children:(0,f.jsx)(E,{"data-state":i?"visible":"hidden",...a,ref:r})})}),E=n.forwardRef((e,r)=>{let{orientation:t="vertical",...o}=e,l=g(C,e.__scopeScrollArea),a=n.useRef(null),i=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(s.viewport,s.content),c={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function p(e,r){return function(e,r,t){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=H(t),l=r||o/2,a=t.scrollbar.paddingStart+l,i=t.scrollbar.size-t.scrollbar.paddingEnd-(o-l),s=t.content-t.viewport;return K([a,i],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,i.current,s,r)}return"horizontal"===t?(0,f.jsx)(k,{...c,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=U(l.viewport.scrollLeft,s,l.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,f.jsx)(M,{...c,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=U(l.viewport.scrollTop,s);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),k=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,a=g(C,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,i.s)(r,d,a.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,f.jsx)(P,{"data-orientation":"horizontal",...l,ref:c,sizes:t,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":H(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(a.viewport){let n=a.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:G(s.paddingLeft),paddingEnd:G(s.paddingRight)}})}})}),M=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,a=g(C,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,i.s)(r,d,a.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,f.jsx)(P,{"data-orientation":"vertical",...l,ref:c,sizes:t,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":H(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(a.viewport){let n=a.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:G(s.paddingTop),paddingEnd:G(s.paddingBottom)}})}})}),[_,T]=v(C),P=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:l,hasThumb:a,onThumbChange:u,onThumbPointerUp:d,onThumbPointerDown:c,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:w,...y}=e,x=g(C,t),[b,R]=n.useState(null),j=(0,i.s)(r,e=>R(e)),D=n.useRef(null),S=n.useRef(""),E=x.viewport,k=l.content-l.viewport,M=(0,s.c)(m),T=(0,s.c)(h),P=B(w,10);function A(e){D.current&&v({x:e.clientX-D.current.left,y:e.clientY-D.current.top})}return n.useEffect(()=>{let e=e=>{let r=e.target;(null==b?void 0:b.contains(r))&&M(e,k)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[E,b,k,M]),n.useEffect(T,[l,T]),V(b,P),V(x.content,P),(0,f.jsx)(_,{scope:t,scrollbar:b,hasThumb:a,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:T,onThumbPointerDown:(0,s.c)(c),children:(0,f.jsx)(o.sG.div,{...y,ref:j,style:{position:"absolute",...y.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),D.current=b.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:(0,p.m)(e.onPointerMove,A),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=S.current,x.viewport&&(x.viewport.style.scrollBehavior=""),D.current=null})})})}),A="ScrollAreaThumb",L=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,o=T(A,e.__scopeScrollArea);return(0,f.jsx)(l.C,{present:t||o.hasThumb,children:(0,f.jsx)(I,{ref:r,...n})})}),I=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:l,...a}=e,s=g(A,t),u=T(A,t),{onThumbPositionChange:d}=u,c=(0,i.s)(r,e=>u.onThumbChange(e)),h=n.useRef(void 0),v=B(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{v(),h.current||(h.current=W(e,d),d())};return d(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,v,d]),(0,f.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;u.onThumbPointerDown({x:t,y:n})}),onPointerUp:(0,p.m)(e.onPointerUp,u.onThumbPointerUp)})});L.displayName=A;var N="ScrollAreaCorner",O=n.forwardRef((e,r)=>{let t=g(N,e.__scopeScrollArea),n=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&n?(0,f.jsx)(F,{...e,ref:r}):null});O.displayName=N;var F=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,a=g(N,t),[i,s]=n.useState(0),[u,d]=n.useState(0),c=!!(i&&u);return V(a.scrollbarX,()=>{var e;let r=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(r),d(r)}),V(a.scrollbarY,()=>{var e;let r=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(r),s(r)}),c?(0,f.jsx)(o.sG.div,{...l,ref:r,style:{width:i,height:u,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function G(e){return e?parseInt(e,10):0}function z(e,r){let t=e/r;return isNaN(t)?0:t}function H(e){let r=z(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function U(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=H(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-o,a=r.content-r.viewport,i=(0,c.q)(e,"ltr"===t?[0,a]:[-1*a,0]);return K([0,a],[0,l-n])(i)}function K(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var W=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},a=t.left!==l.left,i=t.top!==l.top;(a||i)&&r(),t=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function B(e,r){let t=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(t,r)},[t,r])}function V(e,r){let t=(0,s.c)(r);(0,d.N)(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}var X=y,q=b,Y=O},7924:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8698:(e,r,t)=>{t.d(r,{UC:()=>eq,q7:()=>eZ,JU:()=>eY,ZL:()=>eX,bL:()=>eB,wv:()=>eJ,l9:()=>eV});var n=t(2115),o=t(5185),l=t(6101),a=t(6081),i=t(5845),s=t(3655),u=t(7328),d=t(4315),c=t(9178),p=t(2293),f=t(7900),h=t(1285),v=t(5152),m=t(4378),w=t(8905),g=t(9196),y=t(9708),x=t(9033),b=t(8168),C=t(3795),R=t(5155),j=["Enter"," "],D=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...D],E={ltr:[...j,"ArrowRight"],rtl:[...j,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},M="Menu",[_,T,P]=(0,u.N)(M),[A,L]=(0,a.A)(M,[P,v.Bk,g.RG]),I=(0,v.Bk)(),N=(0,g.RG)(),[O,F]=A(M),[G,z]=A(M),H=e=>{let{__scopeMenu:r,open:t=!1,children:o,dir:l,onOpenChange:a,modal:i=!0}=e,s=I(r),[u,c]=n.useState(null),p=n.useRef(!1),f=(0,x.c)(a),h=(0,d.jH)(l);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,R.jsx)(v.bL,{...s,children:(0,R.jsx)(O,{scope:r,open:t,onOpenChange:f,content:u,onContentChange:c,children:(0,R.jsx)(G,{scope:r,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:h,modal:i,children:o})})})};H.displayName=M;var U=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=I(t);return(0,R.jsx)(v.Mz,{...o,...n,ref:r})});U.displayName="MenuAnchor";var K="MenuPortal",[W,B]=A(K,{forceMount:void 0}),V=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:o}=e,l=F(K,r);return(0,R.jsx)(W,{scope:r,forceMount:t,children:(0,R.jsx)(w.C,{present:t||l.open,children:(0,R.jsx)(m.Z,{asChild:!0,container:o,children:n})})})};V.displayName=K;var X="MenuContent",[q,Y]=A(X),Z=n.forwardRef((e,r)=>{let t=B(X,e.__scopeMenu),{forceMount:n=t.forceMount,...o}=e,l=F(X,e.__scopeMenu),a=z(X,e.__scopeMenu);return(0,R.jsx)(_.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(w.C,{present:n||l.open,children:(0,R.jsx)(_.Slot,{scope:e.__scopeMenu,children:a.modal?(0,R.jsx)(J,{...o,ref:r}):(0,R.jsx)(Q,{...o,ref:r})})})})}),J=n.forwardRef((e,r)=>{let t=F(X,e.__scopeMenu),a=n.useRef(null),i=(0,l.s)(r,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,b.Eq)(e)},[]),(0,R.jsx)(ee,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),Q=n.forwardRef((e,r)=>{let t=F(X,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,r)=>{let{__scopeMenu:t,loop:a=!1,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:j,...E}=e,k=F(X,t),M=z(X,t),_=I(t),P=N(t),A=T(t),[L,O]=n.useState(null),G=n.useRef(null),H=(0,l.s)(r,G,k.onContentChange),U=n.useRef(0),K=n.useRef(""),W=n.useRef(0),B=n.useRef(null),V=n.useRef("right"),Y=n.useRef(0),Z=j?C.A:n.Fragment,J=e=>{var r,t;let n=K.current+e,o=A().filter(e=>!e.disabled),l=document.activeElement,a=null==(r=o.find(e=>e.ref.current===l))?void 0:r.textValue,i=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,l=t?e.indexOf(t):-1,a=(n=Math.max(l,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(a=a.filter(e=>e!==t));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==t?i:void 0}(o.map(e=>e.textValue),n,a),s=null==(t=o.find(e=>e.textValue===i))?void 0:t.ref.current;!function e(r){K.current=r,window.clearTimeout(U.current),""!==r&&(U.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};n.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,p.Oh)();let Q=n.useCallback(e=>{var r,t;return V.current===(null==(r=B.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,l=r.length-1;e<r.length;l=e++){let a=r[e],i=r[l],s=a.x,u=a.y,d=i.x,c=i.y;u>n!=c>n&&t<(d-s)*(n-u)/(c-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(t=B.current)?void 0:t.area)},[]);return(0,R.jsx)(q,{scope:t,searchRef:K,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{var r;Q(e)||(null==(r=G.current)||r.focus(),O(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:W,onPointerGraceIntentChange:n.useCallback(e=>{B.current=e},[]),children:(0,R.jsx)(Z,{...j?{as:$,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(s,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,R.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:m,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,R.jsx)(g.bL,{asChild:!0,...P,dir:M.dir,orientation:"vertical",loop:a,currentTabStopId:L,onCurrentTabStopIdChange:O,onEntryFocus:(0,o.m)(h,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(k.open),"data-radix-menu-content":"",dir:M.dir,..._,...E,ref:H,style:{outline:"none",...E.style},onKeyDown:(0,o.m)(E.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&J(e.key));let o=G.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let l=A().filter(e=>!e.disabled).map(e=>e.ref.current);D.includes(e.key)&&l.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(l)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),K.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eM(e=>{let r=e.target,t=Y.current!==e.clientX;e.currentTarget.contains(r)&&t&&(V.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Z.displayName=X;var er=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,R.jsx)(s.sG.div,{role:"group",...n,ref:r})});er.displayName="MenuGroup";var et=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,R.jsx)(s.sG.div,{...n,ref:r})});et.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",el=n.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:a,...i}=e,u=n.useRef(null),d=z(en,e.__scopeMenu),c=Y(en,e.__scopeMenu),p=(0,l.s)(r,u),f=n.useRef(!1);return(0,R.jsx)(ea,{...i,ref:p,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!t&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==a?void 0:a(e),{once:!0}),(0,s.hO)(e,r),r.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:r=>{var t;null==(t=e.onPointerDown)||t.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;t||r&&" "===e.key||j.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=en;var ea=n.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:a=!1,textValue:i,...u}=e,d=Y(en,t),c=N(t),p=n.useRef(null),f=(0,l.s)(r,p),[h,v]=n.useState(!1),[m,w]=n.useState("");return n.useEffect(()=>{let e=p.current;if(e){var r;w((null!=(r=e.textContent)?r:"").trim())}},[u.children]),(0,R.jsx)(_.ItemSlot,{scope:t,disabled:a,textValue:null!=i?i:m,children:(0,R.jsx)(g.q7,{asChild:!0,...c,focusable:!a,children:(0,R.jsx)(s.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eM(e=>{a?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eM(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),ei=n.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...l}=e;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:t,children:(0,R.jsx)(el,{role:"menuitemcheckbox","aria-checked":eE(t)?"mixed":t,...l,ref:r,"data-state":ek(t),onSelect:(0,o.m)(l.onSelect,()=>null==n?void 0:n(!!eE(t)||!t),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[eu,ed]=A(es,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,r)=>{let{value:t,onValueChange:n,...o}=e,l=(0,x.c)(n);return(0,R.jsx)(eu,{scope:e.__scopeMenu,value:t,onValueChange:l,children:(0,R.jsx)(er,{...o,ref:r})})});ec.displayName=es;var ep="MenuRadioItem",ef=n.forwardRef((e,r)=>{let{value:t,...n}=e,l=ed(ep,e.__scopeMenu),a=t===l.value;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:a,children:(0,R.jsx)(el,{role:"menuitemradio","aria-checked":a,...n,ref:r,"data-state":ek(a),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=l.onValueChange)?void 0:e.call(l,t)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var eh="MenuItemIndicator",[ev,em]=A(eh,{checked:!1}),ew=n.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...o}=e,l=em(eh,t);return(0,R.jsx)(w.C,{present:n||eE(l.checked)||!0===l.checked,children:(0,R.jsx)(s.sG.span,{...o,ref:r,"data-state":ek(l.checked)})})});ew.displayName=eh;var eg=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,R.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});eg.displayName="MenuSeparator";var ey=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=I(t);return(0,R.jsx)(v.i3,{...o,...n,ref:r})});ey.displayName="MenuArrow";var[ex,eb]=A("MenuSub"),eC="MenuSubTrigger",eR=n.forwardRef((e,r)=>{let t=F(eC,e.__scopeMenu),a=z(eC,e.__scopeMenu),i=eb(eC,e.__scopeMenu),s=Y(eC,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=s,p={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,R.jsx)(U,{asChild:!0,...p,children:(0,R.jsx)(ea,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":i.contentId,"data-state":eS(t.open),...e,ref:(0,l.t)(r,i.onTriggerChange),onClick:r=>{var n;null==(n=e.onClick)||n.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eM(r=>{s.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{t.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eM(e=>{var r,n;f();let o=null==(r=t.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(n=t.content)?void 0:n.dataset.side,l="right"===r,a=o[l?"left":"right"],i=o[l?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:a,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:a,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==r.key)&&E[a.dir].includes(r.key)){var o;t.onOpenChange(!0),null==(o=t.content)||o.focus(),r.preventDefault()}})})})});eR.displayName=eC;var ej="MenuSubContent",eD=n.forwardRef((e,r)=>{let t=B(X,e.__scopeMenu),{forceMount:a=t.forceMount,...i}=e,s=F(X,e.__scopeMenu),u=z(X,e.__scopeMenu),d=eb(ej,e.__scopeMenu),c=n.useRef(null),p=(0,l.s)(r,c);return(0,R.jsx)(_.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(w.C,{present:a||s.open,children:(0,R.jsx)(_.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;u.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=k[u.dir].includes(e.key);if(r&&t){var n;s.onOpenChange(!1),null==(n=d.trigger)||n.focus(),e.preventDefault()}})})})})})});function eS(e){return e?"open":"closed"}function eE(e){return"indeterminate"===e}function ek(e){return eE(e)?"indeterminate":e?"checked":"unchecked"}function eM(e){return r=>"mouse"===r.pointerType?e(r):void 0}eD.displayName=ej;var e_="DropdownMenu",[eT,eP]=(0,a.A)(e_,[L]),eA=L(),[eL,eI]=eT(e_),eN=e=>{let{__scopeDropdownMenu:r,children:t,dir:o,open:l,defaultOpen:a,onOpenChange:s,modal:u=!0}=e,d=eA(r),c=n.useRef(null),[p,f]=(0,i.i)({prop:l,defaultProp:null!=a&&a,onChange:s,caller:e_});return(0,R.jsx)(eL,{scope:r,triggerId:(0,h.B)(),triggerRef:c,contentId:(0,h.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,R.jsx)(H,{...d,open:p,onOpenChange:f,dir:o,modal:u,children:t})})};eN.displayName=e_;var eO="DropdownMenuTrigger",eF=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...a}=e,i=eI(eO,t),u=eA(t);return(0,R.jsx)(U,{asChild:!0,...u,children:(0,R.jsx)(s.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,l.t)(r,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eO;var eG=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eA(r);return(0,R.jsx)(V,{...n,...t})};eG.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eH=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...l}=e,a=eI(ez,t),i=eA(t),s=n.useRef(!1);return(0,R.jsx)(Z,{id:a.contentId,"aria-labelledby":a.triggerId,...i,...l,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;s.current||null==(r=a.triggerRef.current)||r.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!a.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eH.displayName=ez,n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(er,{...o,...n,ref:r})}).displayName="DropdownMenuGroup";var eU=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(et,{...o,...n,ref:r})});eU.displayName="DropdownMenuLabel";var eK=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(el,{...o,...n,ref:r})});eK.displayName="DropdownMenuItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(ei,{...o,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(ec,{...o,...n,ref:r})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(ef,{...o,...n,ref:r})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(ew,{...o,...n,ref:r})}).displayName="DropdownMenuItemIndicator";var eW=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(eg,{...o,...n,ref:r})});eW.displayName="DropdownMenuSeparator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(ey,{...o,...n,ref:r})}).displayName="DropdownMenuArrow",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(eR,{...o,...n,ref:r})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eA(t);return(0,R.jsx)(eD,{...o,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eB=eN,eV=eF,eX=eG,eq=eH,eY=eU,eZ=eK,eJ=eW}}]);