{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/interactive-mcp.ts"], "sourcesContent": ["// Interactive MCP integration for enhanced user interaction\n// This service provides interactive capabilities for our CRM system\n\nexport interface InteractiveMCPOptions {\n  timeout?: number;\n  enableNotifications?: boolean;\n  enableIntensiveChat?: boolean;\n}\n\nexport class InteractiveMCPService {\n  private options: InteractiveMCPOptions;\n  private isAvailable: boolean = false;\n\n  constructor(options: InteractiveMCPOptions = {}) {\n    this.options = {\n      timeout: 30,\n      enableNotifications: true,\n      enableIntensiveChat: true,\n      ...options,\n    };\n    \n    this.checkAvailability();\n  }\n\n  private checkAvailability() {\n    // Check if we're in a browser environment and if MCP is available\n    this.isAvailable = typeof window !== 'undefined' && \n                      typeof navigator !== 'undefined';\n  }\n\n  /**\n   * Request user input with optional predefined choices\n   */\n  async requestUserInput(\n    question: string, \n    options?: string[], \n    timeout?: number\n  ): Promise<string | null> {\n    if (!this.isAvailable) {\n      console.warn('Interactive MCP not available, falling back to prompt');\n      return window.prompt(question) || null;\n    }\n\n    try {\n      // In a real implementation, this would use the MCP protocol\n      // For now, we'll simulate with a promise-based prompt\n      return new Promise((resolve) => {\n        const userResponse = window.prompt(\n          options \n            ? `${question}\\n\\nOptions: ${options.join(', ')}`\n            : question\n        );\n        resolve(userResponse);\n      });\n    } catch (error) {\n      console.error('Error requesting user input:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Send a completion notification to the user\n   */\n  async sendNotification(\n    title: string, \n    message: string, \n    type: 'info' | 'success' | 'warning' | 'error' = 'info'\n  ): Promise<void> {\n    if (!this.options.enableNotifications) return;\n\n    try {\n      // Check if browser notifications are supported\n      if ('Notification' in window) {\n        // Request permission if not already granted\n        if (Notification.permission === 'default') {\n          await Notification.requestPermission();\n        }\n\n        if (Notification.permission === 'granted') {\n          new Notification(title, {\n            body: message,\n            icon: '/favicon.ico',\n            tag: 'crm-notification',\n          });\n        }\n      }\n\n      // Also log to console as fallback\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n    } catch (error) {\n      console.error('Error sending notification:', error);\n    }\n  }\n\n  /**\n   * Start an intensive chat session for complex interactions\n   */\n  async startIntensiveChat(sessionId: string): Promise<boolean> {\n    if (!this.options.enableIntensiveChat) return false;\n\n    try {\n      console.log(`Starting intensive chat session: ${sessionId}`);\n      // In a real implementation, this would initialize the MCP intensive chat\n      return true;\n    } catch (error) {\n      console.error('Error starting intensive chat:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Ask a question within an active intensive chat session\n   */\n  async askIntensiveChat(\n    sessionId: string, \n    question: string\n  ): Promise<string | null> {\n    if (!this.options.enableIntensiveChat) return null;\n\n    try {\n      console.log(`Asking in session ${sessionId}: ${question}`);\n      // In a real implementation, this would use the MCP intensive chat\n      return await this.requestUserInput(question);\n    } catch (error) {\n      console.error('Error asking intensive chat question:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Stop an active intensive chat session\n   */\n  async stopIntensiveChat(sessionId: string): Promise<void> {\n    if (!this.options.enableIntensiveChat) return;\n\n    try {\n      console.log(`Stopping intensive chat session: ${sessionId}`);\n      // In a real implementation, this would close the MCP intensive chat\n    } catch (error) {\n      console.error('Error stopping intensive chat:', error);\n    }\n  }\n\n  /**\n   * Confirm an action with the user\n   */\n  async confirmAction(\n    action: string, \n    details?: string\n  ): Promise<boolean> {\n    const question = details \n      ? `Are you sure you want to ${action}?\\n\\n${details}`\n      : `Are you sure you want to ${action}?`;\n    \n    const response = await this.requestUserInput(question, ['Yes', 'No']);\n    return response?.toLowerCase() === 'yes' || response?.toLowerCase() === 'y';\n  }\n\n  /**\n   * Get user preference with multiple options\n   */\n  async getUserPreference(\n    question: string, \n    options: string[]\n  ): Promise<string | null> {\n    return await this.requestUserInput(question, options);\n  }\n}\n\n// Create a singleton instance\nexport const interactiveMCP = new InteractiveMCPService();\n\n// Utility functions for common interactions\nexport const mcpUtils = {\n  confirmDelete: (itemName: string) => \n    interactiveMCP.confirmAction(`delete ${itemName}`, 'This action cannot be undone.'),\n  \n  confirmSave: (itemName: string) => \n    interactiveMCP.confirmAction(`save ${itemName}`),\n  \n  notifySuccess: (message: string) => \n    interactiveMCP.sendNotification('Success', message, 'success'),\n  \n  notifyError: (message: string) => \n    interactiveMCP.sendNotification('Error', message, 'error'),\n  \n  askForInput: (prompt: string) => \n    interactiveMCP.requestUserInput(prompt),\n};\n"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,oEAAoE;;;;;;AAQ7D,MAAM;IACH,QAA+B;IAC/B,cAAuB,MAAM;IAErC,YAAY,UAAiC,CAAC,CAAC,CAAE;QAC/C,IAAI,CAAC,OAAO,GAAG;YACb,SAAS;YACT,qBAAqB;YACrB,qBAAqB;YACrB,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,kEAAkE;QAClE,IAAI,CAAC,WAAW,GAAG,aAAkB,eACnB,OAAO,cAAc;IACzC;IAEA;;GAEC,GACD,MAAM,iBACJ,QAAgB,EAChB,OAAkB,EAClB,OAAgB,EACQ;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,QAAQ,IAAI,CAAC;YACb,OAAO,OAAO,MAAM,CAAC,aAAa;QACpC;QAEA,IAAI;YACF,4DAA4D;YAC5D,sDAAsD;YACtD,OAAO,IAAI,QAAQ,CAAC;gBAClB,MAAM,eAAe,OAAO,MAAM,CAChC,UACI,GAAG,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC,OAAO,GAC/C;gBAEN,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,iBACJ,KAAa,EACb,OAAe,EACf,OAAiD,MAAM,EACxC;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAEvC,IAAI;YACF,+CAA+C;YAC/C,IAAI,kBAAkB,QAAQ;gBAC5B,4CAA4C;gBAC5C,IAAI,aAAa,UAAU,KAAK,WAAW;oBACzC,MAAM,aAAa,iBAAiB;gBACtC;gBAEA,IAAI,aAAa,UAAU,KAAK,WAAW;oBACzC,IAAI,aAAa,OAAO;wBACtB,MAAM;wBACN,MAAM;wBACN,KAAK;oBACP;gBACF;YACF;YAEA,kCAAkC;YAClC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,SAAS;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,SAAiB,EAAoB;QAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,OAAO;QAE9C,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;YAC3D,yEAAyE;YACzE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,iBACJ,SAAiB,EACjB,QAAgB,EACQ;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,OAAO;QAE9C,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,EAAE,EAAE,UAAU;YACzD,kEAAkE;YAClE,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,SAAiB,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAEvC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;QAC3D,oEAAoE;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,MAAc,EACd,OAAgB,EACE;QAClB,MAAM,WAAW,UACb,CAAC,yBAAyB,EAAE,OAAO,KAAK,EAAE,SAAS,GACnD,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;QAEzC,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU;YAAC;YAAO;SAAK;QACpE,OAAO,UAAU,kBAAkB,SAAS,UAAU,kBAAkB;IAC1E;IAEA;;GAEC,GACD,MAAM,kBACJ,QAAgB,EAChB,OAAiB,EACO;QACxB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU;IAC/C;AACF;AAGO,MAAM,iBAAiB,IAAI;AAG3B,MAAM,WAAW;IACtB,eAAe,CAAC,WACd,eAAe,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE;IAErD,aAAa,CAAC,WACZ,eAAe,aAAa,CAAC,CAAC,KAAK,EAAE,UAAU;IAEjD,eAAe,CAAC,UACd,eAAe,gBAAgB,CAAC,WAAW,SAAS;IAEtD,aAAa,CAAC,UACZ,eAAe,gBAAgB,CAAC,SAAS,SAAS;IAEpD,aAAa,CAAC,SACZ,eAAe,gBAAgB,CAAC;AACpC", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/components/forms/EnhancedProjectForm.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { interactiveMCP, mcpUtils } from '@/lib/interactive-mcp';\nimport { motion } from 'framer-motion';\nimport { Lightbulb, Save, Sparkles } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\ninterface ProjectFormData {\n  name: string;\n  description: string;\n  status: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';\n}\n\ninterface EnhancedProjectFormProps {\n  initialData?: Partial<ProjectFormData>;\n  onSubmit?: (data: ProjectFormData) => Promise<void>;\n  isEditing?: boolean;\n}\n\nexport function EnhancedProjectForm({ \n  initialData, \n  onSubmit, \n  isEditing = false \n}: EnhancedProjectFormProps) {\n  const router = useRouter();\n  const [formData, setFormData] = useState<ProjectFormData>({\n    name: initialData?.name || '',\n    description: initialData?.description || '',\n    status: initialData?.status || 'ACTIVE',\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);\n\n  const handleInputChange = (field: keyof ProjectFormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const generateAISuggestions = async () => {\n    if (!formData.name) {\n      await mcpUtils.notifyError('Voer eerst een projectnaam in om suggesties te krijgen');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      // Simulate AI suggestions based on project name\n      const suggestions = [\n        `Implementeer een moderne ${formData.name} met React en TypeScript`,\n        `Ontwikkel een responsive design voor ${formData.name}`,\n        `Integreer database functionaliteit voor ${formData.name}`,\n        `Voeg authenticatie toe aan ${formData.name}`,\n        `Optimaliseer performance van ${formData.name}`,\n      ];\n\n      setAiSuggestions(suggestions);\n      await mcpUtils.notifySuccess('AI suggesties gegenereerd!');\n    } catch (error) {\n      await mcpUtils.notifyError('Fout bij het genereren van suggesties');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const selectSuggestion = async (suggestion: string) => {\n    const confirmed = await interactiveMCP.confirmAction(\n      'deze suggestie gebruiken',\n      `Suggestie: \"${suggestion}\"`\n    );\n\n    if (confirmed) {\n      setFormData(prev => ({ \n        ...prev, \n        description: prev.description \n          ? `${prev.description}\\n\\n${suggestion}`\n          : suggestion\n      }));\n      await mcpUtils.notifySuccess('Suggestie toegevoegd aan beschrijving');\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.name.trim()) {\n      await mcpUtils.notifyError('Projectnaam is verplicht');\n      return;\n    }\n\n    const confirmed = await mcpUtils.confirmSave(\n      isEditing ? 'project wijzigingen' : 'nieuw project'\n    );\n\n    if (!confirmed) return;\n\n    setIsLoading(true);\n    try {\n      if (onSubmit) {\n        await onSubmit(formData);\n      } else {\n        // Default API call\n        const response = await fetch('/api/projects', {\n          method: isEditing ? 'PATCH' : 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify(formData),\n        });\n\n        if (!response.ok) throw new Error('Failed to save project');\n      }\n\n      await mcpUtils.notifySuccess(\n        isEditing ? 'Project succesvol bijgewerkt!' : 'Project succesvol aangemaakt!'\n      );\n      \n      router.push('/dashboard/projects');\n    } catch (error) {\n      await mcpUtils.notifyError('Fout bij het opslaan van het project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"max-w-4xl mx-auto space-y-6\"\n    >\n      <Card className=\"border-primary/20\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Sparkles className=\"h-5 w-5\" />\n            {isEditing ? 'Project Bewerken' : 'Nieuw Project Aanmaken'}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid gap-6 md:grid-cols-2\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"name\">Projectnaam *</Label>\n                <Input\n                  id=\"name\"\n                  value={formData.name}\n                  onChange={(e) => handleInputChange('name', e.target.value)}\n                  placeholder=\"Voer projectnaam in...\"\n                  required\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"status\">Status</Label>\n                <Select\n                  value={formData.status}\n                  onValueChange={(value: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED') => \n                    handleInputChange('status', value)\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"ACTIVE\">Actief</SelectItem>\n                    <SelectItem value=\"COMPLETED\">Voltooid</SelectItem>\n                    <SelectItem value=\"ARCHIVED\">Gearchiveerd</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <Label htmlFor=\"description\">Beschrijving</Label>\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={generateAISuggestions}\n                  disabled={isLoading || !formData.name}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Lightbulb className=\"h-4 w-4\" />\n                  AI Suggesties\n                </Button>\n              </div>\n              <Textarea\n                id=\"description\"\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                placeholder=\"Beschrijf je project...\"\n                rows={6}\n              />\n            </div>\n\n            <div className=\"flex gap-4\">\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"flex items-center gap-2\"\n              >\n                <Save className=\"h-4 w-4\" />\n                {isLoading ? 'Opslaan...' : isEditing ? 'Bijwerken' : 'Aanmaken'}\n              </Button>\n              \n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => router.back()}\n              >\n                Annuleren\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* AI Suggestions Panel */}\n      {aiSuggestions.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          transition={{ duration: 0.3 }}\n        >\n          <Card className=\"border-primary/20\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2 text-lg\">\n                <Lightbulb className=\"h-5 w-5\" />\n                AI Suggesties\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {aiSuggestions.map((suggestion, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"p-3 rounded-lg border border-border hover:border-primary/30 transition-colors cursor-pointer\"\n                    onClick={() => selectSuggestion(suggestion)}\n                  >\n                    <p className=\"text-sm\">{suggestion}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;;;;;;;AA0BO,SAAS,oBAAoB,EAClC,WAAW,EACX,QAAQ,EACR,YAAY,KAAK,EACQ;;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM,aAAa,QAAQ;QAC3B,aAAa,aAAa,eAAe;QACzC,QAAQ,aAAa,UAAU;IACjC;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM,mIAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC3B;QACF;QAEA,aAAa;QACb,IAAI;YACF,gDAAgD;YAChD,MAAM,cAAc;gBAClB,CAAC,yBAAyB,EAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC;gBACnE,CAAC,qCAAqC,EAAE,SAAS,IAAI,EAAE;gBACvD,CAAC,wCAAwC,EAAE,SAAS,IAAI,EAAE;gBAC1D,CAAC,2BAA2B,EAAE,SAAS,IAAI,EAAE;gBAC7C,CAAC,6BAA6B,EAAE,SAAS,IAAI,EAAE;aAChD;YAED,iBAAiB;YACjB,MAAM,mIAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;QAC/B,EAAE,OAAO,OAAO;YACd,MAAM,mIAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,YAAY,MAAM,mIAAA,CAAA,iBAAc,CAAC,aAAa,CAClD,4BACA,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAG9B,IAAI,WAAW;YACb,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,aAAa,KAAK,WAAW,GACzB,GAAG,KAAK,WAAW,CAAC,IAAI,EAAE,YAAY,GACtC;gBACN,CAAC;YACD,MAAM,mIAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;QAC/B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,MAAM,mIAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC3B;QACF;QAEA,MAAM,YAAY,MAAM,mIAAA,CAAA,WAAQ,CAAC,WAAW,CAC1C,YAAY,wBAAwB;QAGtC,IAAI,CAAC,WAAW;QAEhB,aAAa;QACb,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,SAAS;YACjB,OAAO;gBACL,mBAAmB;gBACnB,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ,YAAY,UAAU;oBAC9B,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YACpC;YAEA,MAAM,mIAAA,CAAA,WAAQ,CAAC,aAAa,CAC1B,YAAY,kCAAkC;YAGhD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM,mIAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAEV,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,YAAY,qBAAqB;;;;;;;;;;;;kCAGtC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,MAAM;oDACtB,eAAe,CAAC,QACd,kBAAkB,UAAU;;sEAG9B,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,UAAU,aAAa,CAAC,SAAS,IAAI;oDACrC,WAAU;;sEAEV,6LAAC,+MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAIrC,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;4CACZ,MAAM;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,YAAY,eAAe,YAAY,cAAc;;;;;;;sDAGxD,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,OAAO,IAAI;sDAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,cAAc,MAAM,GAAG,mBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIrC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,YAAY,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,WAAU;wCACV,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,6LAAC;4CAAE,WAAU;sDAAW;;;;;;uCAPnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBzB;GAvOgB;;QAKC,qIAAA,CAAA,YAAS;;;KALV", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { EnhancedProjectForm } from '@/components/forms/EnhancedProjectForm';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { ArrowLeft } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\n\nexport default function NewProjectPage() {\n  const router = useRouter();\n\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 space-y-6\">\n      <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => router.back()}\n          className=\"flex items-center gap-2\"\n        >\n          <ArrowLeft className=\"h-4 w-4\" />\n          Terug\n        </Button>\n        <h1 className=\"text-3xl font-bold tracking-tight gradient-text\">\n          Nieuw Project Aanmaken\n        </h1>\n      </div>\n\n      <EnhancedProjectForm />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGnC,6LAAC;wBAAG,WAAU;kCAAkD;;;;;;;;;;;;0BAKlE,6LAAC,qJAAA,CAAA,sBAAmB;;;;;;;;;;;AAG1B;GAvBwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}