"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[493],{1275:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(2115),o=r(2712);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},1285:(e,t,r)=>{r.d(t,{B:()=>a});var n,o=r(2115),i=r(2712),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(2115),o=globalThis?.document?n.useLayoutEffect:()=>{}},3655:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>u});var n=r(2115),o=r(7650),i=r(9708),l=r(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4315:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(2115);r(5155);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5845:(e,t,r)=>{r.d(t,{i:()=>u});var n,o=r(2115),i=r(2712),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),u=o.useRef(t);return l(()=>{u.current=t},[t]),o.useEffect(()=>{i.current!==r&&(u.current?.(r),i.current=r)},[r,i]),[r,n,u]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[c,e,u,a])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>l,q:()=>i});var n=r(2115),o=r(5155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,l=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),u=r.length;r=[...r,i];let a=t=>{let{scope:r,children:i,...a}=t,c=r?.[e]?.[u]||l,s=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:i})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[u]||l,c=n.useContext(a);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},7328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function i(e,t,r){var o=n(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}r.d(t,{N:()=>d});var l,u=r(2115),a=r(6081),c=r(6101),s=r(9708),f=r(5155);function d(e){let t=e+"CollectionProvider",[r,n]=(0,a.A)(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=u.useRef(null),i=u.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};l.displayName=t;let d=e+"CollectionSlot",m=(0,s.TL)(d),p=u.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=i(d,r),l=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(m,{ref:l,children:n})});p.displayName=d;let v=e+"CollectionItemSlot",h="data-radix-collection-item",w=(0,s.TL)(v),y=u.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,l=u.useRef(null),a=(0,c.s)(t,l),s=i(v,r);return u.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,f.jsx)(w,{...{[h]:""},ref:a,children:n})});return y.displayName=v,[{Provider:l,Slot:p,ItemSlot:y},function(t){let r=i(e+"CollectionConsumer",t);return u.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var m=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},9033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(2115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},9946:(e,t,r)=>{r.d(t,{A:()=>f});var n=r(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:s="",children:f,iconNode:d,...m}=e;return(0,n.createElement)("svg",{ref:t,...c,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:u("lucide",s),...!f&&!a(m)&&{"aria-hidden":"true"},...m},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])}),f=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:a,...c}=r;return(0,n.createElement)(s,{ref:i,iconNode:t,className:u("lucide-".concat(o(l(e))),"lucide-".concat(e),a),...c})});return r.displayName=l(e),r}}}]);