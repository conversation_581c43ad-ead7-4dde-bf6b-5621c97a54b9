{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "3bd7fae55b8b4b2ebd9253c55b616ef9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e120bdc575736a99169aeb7de05734b8defb317db1c840eebca2d45f50cc483", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "41ad4d7951ff26ceee8e5b092c44722dbfb4fd3ac7e90a0417880cc9452d29de"}}}, "instrumentation": null, "functions": {}}