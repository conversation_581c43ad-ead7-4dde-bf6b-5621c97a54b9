{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "8febf317deae386a6855f25ddc4c72fc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "70da1ddc8bacde830950c692ea138b6d710c9ea1090cd70bdb88ff55587d76f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7f83c9ef25aaa44635754a4db9661ebaff995c9836c5a3df27d3c766f1f2771d"}}}, "instrumentation": null, "functions": {}}