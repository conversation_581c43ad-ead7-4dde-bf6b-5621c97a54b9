{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "dba419e3708007d3f4e7bbc90d2b24f6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1a0cee11e23e213617a3fdcfda89d643f0550671d2db51588629622b33af13f2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "89ad625df32ad17c015ef637cae9046f10ab3ee8e03a07fb1fb6c3184a631bf2"}}}, "instrumentation": null, "functions": {}}