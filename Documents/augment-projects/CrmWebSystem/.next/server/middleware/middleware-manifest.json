{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "0ff40e6eaec291510df58b4113ec3d4c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7befa507053558d6b0dbc1edbd8a55b20f82c8ec0d6f611c7c0161b0938f82e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c51627b2e91282882fee4ca3587ac829fc93ac953913b22a7dc0c4e6581d217f"}}}, "instrumentation": null, "functions": {}}