{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\";\nimport { NextResponse } from \"next/server\";\n\nexport default withAuth(\n  function middleware(req) {\n    return NextResponse.next();\n  },\n  {\n    callbacks: {\n      authorized: ({ token }) => !!token,\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\"/dashboard/:path*\"],\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC,CAAC;IAC/B;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QAAC;KAAoB;AAChC"}}]}