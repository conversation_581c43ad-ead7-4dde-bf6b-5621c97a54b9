{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "WfNPardEGv8E8L72kzRAA", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "5029a794cf639961376d6649ec59e89e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "223d92106e766d4876f7adb5416412519cd49e4a5cac7fedf5ac07e160a057e1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9c9fdb8d4950362805a8a69621581ef4e9666d7dc1f2dd4ece96a05d5edd7093"}}}, "functions": {}, "sortedMiddleware": ["/"]}