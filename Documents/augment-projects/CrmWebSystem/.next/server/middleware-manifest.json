{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "77383fbb7b3e091eaf0ce6ecf3ebee0f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "efe0f723a6a283190253249dd4fd40f0a276bdcec556f8afc8ef5c697f462336", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bb30d78a3c06bc48208059d4ad0c3e709ae9de6e809b292517270310b08abac9"}}}, "sortedMiddleware": ["/"], "functions": {}}