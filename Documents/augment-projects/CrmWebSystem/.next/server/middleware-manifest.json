{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5ZNk74bnCjYlMaRviLwIWPz0RJf+23rSJVkxFvsJT5k=", "__NEXT_PREVIEW_MODE_ID": "967e65d108c8f64cce29c2865ab628f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c0af8357f87c486c9ae4bb4742130fee58ec2b42cf3df46aa5f2ae8af34f7e4c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4decf636f9dcac161be4abd7ff1498c4ec7cc413b150838b97f056710b06ac57"}}}, "sortedMiddleware": ["/"], "functions": {}}