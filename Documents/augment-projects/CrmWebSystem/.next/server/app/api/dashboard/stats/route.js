(()=>{var e={};e.id=694,e.ids=[694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(31183),a=t(85663);let o={session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,t(13581).A)({name:"Sign in",credentials:{email:{label:"Email",type:"email",placeholder:"<EMAIL>"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e.password)return null;let r=await s.z.user.findUnique({where:{email:e.email}});return r&&r.password&&await (0,a.UD)(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role||"USER"}:null}})],callbacks:{session:({session:e,token:r})=>({...e,user:{...e.user,id:r.id,role:r.role}}),jwt:({token:e,user:r})=>r?{...e,id:r.id,role:r.role}:e}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var a=t(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var n=a?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(s,o,n):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(35426));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82332:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>b,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(96559),o=t(48088),i=t(37719),n=t(19854),u=t(32190),p=t(12909),l=t(31183);async function c(){try{let e=await (0,n.getServerSession)(p.N);if(!e?.user?.id)return u.NextResponse.json({error:"Niet geautoriseerd"},{status:401});let r=e.user.id,[t,s,a,o,i]=await Promise.all([l.z.project.count({where:{userId:r,status:"ACTIVE"}}),l.z.project.count({where:{userId:r,status:"COMPLETED"}}),l.z.task.count({where:{userId:r}}),l.z.task.count({where:{userId:r,status:"DONE"}}),l.z.project.findMany({where:{userId:r},include:{_count:{select:{tasks:!0}}},orderBy:{updatedAt:"desc"},take:5})]);return u.NextResponse.json({activeProjects:t,completedProjects:s,totalTasks:a,completedTasks:o,recentProjects:i})}catch(e){return console.error("Dashboard stats error:",e),u.NextResponse.json({error:"Fout bij het ophalen van dashboard statistieken"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/dashboard/stats/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:b}=d;function m(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,112],()=>t(82332));module.exports=s})();