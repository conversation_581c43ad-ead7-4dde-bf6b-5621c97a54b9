(()=>{var e={};e.id=532,e.ids=[532],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11391:(e,t,s)=>{"use strict";let n,r,i;s.r(t),s.d(t,{patchFetch:()=>nd,routeModule:()=>nl,serverHooks:()=>nh,workAsyncStorage:()=>nc,workUnitAsyncStorage:()=>nu});var a,o,l,c,u,h,d,f,p,m,g,y,w,_,b,v,x,S,A,O,$,k,I,R,P,j,E,N,T,C,M,D,L,q,B,U,W,F,J,X,z,H,K,V,G,Y,Q,Z,ee,et,es,en,er,ei,ea,eo,el,ec,eu,eh,ed,ef,ep,em,eg,ey,ew,e_,eb,ev,ex={};s.r(ex),s.d(ex,{POST:()=>no});var eS=s(96559),eA=s(48088),eO=s(37719),e$=s(12909),ek=s(31183),eI=s(19854),eR=s(32190);function eP(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function ej(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}let eE=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eE=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eN(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eT=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eC extends Error{}class eM extends eC{constructor(e,t,s,n){super(`${eM.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eL({message:s,cause:eT(t)});let r=t?.error;return 400===e?new eB(e,r,s,n):401===e?new eU(e,r,s,n):403===e?new eW(e,r,s,n):404===e?new eF(e,r,s,n):409===e?new eJ(e,r,s,n):422===e?new eX(e,r,s,n):429===e?new ez(e,r,s,n):e>=500?new eH(e,r,s,n):new eM(e,r,s,n)}}class eD extends eM{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eL extends eM{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eq extends eL{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eB extends eM{}class eU extends eM{}class eW extends eM{}class eF extends eM{}class eJ extends eM{}class eX extends eM{}class ez extends eM{}class eH extends eM{}class eK extends eC{constructor(){super("Could not parse response content as the length limit was reached")}}class eV extends eC{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let eG=/^[a-z][a-z0-9+.-]*:/i,eY=e=>eG.test(e);function eQ(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let eZ=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eC(`${e} must be an integer`);if(t<0)throw new eC(`${e} must be a positive integer`);return t},e0=e=>{try{return JSON.parse(e)}catch(e){return}},e1=e=>new Promise(t=>setTimeout(t,e)),e2={off:0,error:200,warn:300,info:400,debug:500},e3=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(e2,e))return e;e9(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(e2))}`)}};function e4(){}function e5(e,t,s){return!t||e2[e]>e2[s]?e4:t[e].bind(t)}let e8={error:e4,warn:e4,info:e4,debug:e4},e6=new WeakMap;function e9(e){let t=e.logger,s=e.logLevel??"off";if(!t)return e8;let n=e6.get(t);if(n&&n[0]===s)return n[1];let r={error:e5("error",t,s),warn:e5("warn",t,s),info:e5("info",t,s),debug:e5("debug",t,s)};return e6.set(t,[s,r]),r}let e7=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),te="5.3.0",tt=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,ts=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tr(Deno.build.os),"X-Stainless-Arch":tn(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tr(globalThis.process.platform??"unknown"),"X-Stainless-Arch":tn(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tn=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tr=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",ti=()=>n??(n=ts());function ta(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function to(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return ta({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function tl(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tc(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tu=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),th="RFC3986",td={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},tf=(Object.prototype.hasOwnProperty,Array.isArray),tp=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tm(e,t){if(tf(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let tg=Object.prototype.hasOwnProperty,ty={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tw=Array.isArray,t_=Array.prototype.push,tb=function(e,t){t_.apply(e,tw(t)?t:[t])},tv=Date.prototype.toISOString,tx={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=tp[n];continue}if(n<2048){s[s.length]=tp[192|n>>6]+tp[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=tp[224|n>>12]+tp[128|n>>6&63]+tp[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=tp[240|n>>18]+tp[128|n>>12&63]+tp[128|n>>6&63]+tp[128|63&n]}a+=s.join("")}return a},encodeValuesOnly:!1,format:th,formatter:td[th],indices:!1,serializeDate:e=>tv.call(e),skipNulls:!1,strictNullHandling:!1},tS={};function tA(e){let t;return(r??(r=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tO(e){let t;return(i??(i=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class t${constructor(){a.set(this,void 0),o.set(this,void 0),eP(this,a,new Uint8Array,"f"),eP(this,o,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tA(e):e;eP(this,a,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([ej(this,a,"f"),s]),"f");let n=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(ej(this,a,"f"),ej(this,o,"f")));){if(t.carriage&&null==ej(this,o,"f")){eP(this,o,t.index,"f");continue}if(null!=ej(this,o,"f")&&(t.index!==ej(this,o,"f")+1||t.carriage)){n.push(tO(ej(this,a,"f").subarray(0,ej(this,o,"f")-1))),eP(this,a,ej(this,a,"f").subarray(ej(this,o,"f")),"f"),eP(this,o,null,"f");continue}let e=null!==ej(this,o,"f")?t.preceding-1:t.preceding,s=tO(ej(this,a,"f").subarray(0,e));n.push(s),eP(this,a,ej(this,a,"f").subarray(t.index),"f"),eP(this,o,null,"f")}return n}flush(){return ej(this,a,"f").length?this.decode("\n"):[]}}a=new WeakMap,o=new WeakMap,t$.NEWLINE_CHARS=new Set(["\n","\r"]),t$.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class tk{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*n(){if(s)throw new eC("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(let s of tI(e,t))if(!n){if(s.data.startsWith("[DONE]")){n=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new eM(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new eM(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}n=!0}catch(e){if(eN(e))return;throw e}finally{n||t.abort()}}return new tk(n,t)}static fromReadableStream(e,t){let s=!1;async function*n(){let t=new t$;for await(let s of tl(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tk(async function*(){if(s)throw new eC("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eN(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tk(()=>n(e),this.controller),new tk(()=>n(t),this.controller)]}toReadableStream(){let e,t=this;return ta({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tA(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tI(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eC("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eC("Attempted to iterate over a response with no body")}let s=new tP,n=new t$;for await(let t of tR(tl(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tR(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tA(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tP{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tj(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(e9(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):tk.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?tE(await s.json(),s):await s.text()})();return e9(e).debug(`[${n}] response parsed`,e7({retryOfRequestLogID:r,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function tE(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tN extends Promise{constructor(e,t,s=tj){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,l.set(this,void 0),eP(this,l,e,"f")}_thenUnwrap(e){return new tN(ej(this,l,"f"),this.responsePromise,async(t,s)=>tE(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(ej(this,l,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}l=new WeakMap;class tT{constructor(e,t,s,n){c.set(this,void 0),eP(this,c,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eC("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await ej(this,c,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(c=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tC extends tN{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tj(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tM extends tT{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tD extends tT{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tL=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tq(e,t,s){return tL(),new File(e,t??"unknown_file",s)}function tB(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tU=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tW=async(e,t)=>({...e,body:await tJ(e.body,t)}),tF=new WeakMap,tJ=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tF.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tF.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tK(s,e,t))),s},tX=e=>e instanceof Blob&&"name"in e,tz=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tU(e)||tX(e)),tH=e=>{if(tz(e))return!0;if(Array.isArray(e))return e.some(tH);if(e&&"object"==typeof e){for(let t in e)if(tH(e[t]))return!0}return!1},tK=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tq([await s.blob()],tB(s)));else if(tU(s))e.append(t,tq([await new Response(to(s)).blob()],tB(s)));else if(tX(s))e.append(t,s,tB(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tK(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>tK(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tV=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tG=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tV(e),tY=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tQ(e,t,s){if(tL(),tG(e=await e))return e instanceof File?e:tq([await e.arrayBuffer()],e.name);if(tY(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tq(await tZ(n),t,s)}let n=await tZ(e);if(t||(t=tB(e)),!s?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tq(n,t,s)}async function tZ(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tV(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tU(e))for await(let s of e)t.push(...await tZ(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class t0{constructor(e){this._client=e}}function t1(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t2=((e=t1)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,i=t.reduce((t,n,i)=>(/[?#]/.test(n)&&(r=!0),t+n+(i===s.length?"":(r?encodeURIComponent:e)(String(s[i])))),""),a=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(a));)o.push({start:n.index,length:n[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new eC(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(t1);class t3 extends t0{list(e,t={},s){return this._client.getAPIList(t2`/chat/completions/${e}/messages`,tD,{query:t,...s})}}let t4=e=>e?.role==="assistant",t5=e=>e?.role==="tool";class t8{constructor(){u.add(this),this.controller=new AbortController,h.set(this,void 0),d.set(this,()=>{}),f.set(this,()=>{}),p.set(this,void 0),m.set(this,()=>{}),g.set(this,()=>{}),y.set(this,{}),w.set(this,!1),_.set(this,!1),b.set(this,!1),v.set(this,!1),eP(this,h,new Promise((e,t)=>{eP(this,d,e,"f"),eP(this,f,t,"f")}),"f"),eP(this,p,new Promise((e,t)=>{eP(this,m,e,"f"),eP(this,g,t,"f")}),"f"),ej(this,h,"f").catch(()=>{}),ej(this,p,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},ej(this,u,"m",x).bind(this))},0)}_connected(){this.ended||(ej(this,d,"f").call(this),this._emit("connect"))}get ended(){return ej(this,w,"f")}get errored(){return ej(this,_,"f")}get aborted(){return ej(this,b,"f")}abort(){this.controller.abort()}on(e,t){return(ej(this,y,"f")[e]||(ej(this,y,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=ej(this,y,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(ej(this,y,"f")[e]||(ej(this,y,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{eP(this,v,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){eP(this,v,!0,"f"),await ej(this,p,"f")}_emit(e,...t){if(ej(this,w,"f"))return;"end"===e&&(eP(this,w,!0,"f"),ej(this,m,"f").call(this));let s=ej(this,y,"f")[e];if(s&&(ej(this,y,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];ej(this,v,"f")||s?.length||Promise.reject(e),ej(this,f,"f").call(this,e),ej(this,g,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];ej(this,v,"f")||s?.length||Promise.reject(e),ej(this,f,"f").call(this,e),ej(this,g,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function t6(e){return e?.$brand==="auto-parseable-response-format"}function t9(e){return e?.$brand==="auto-parseable-tool"}function t7(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new eK;if("content_filter"===e.finish_reason)throw new eV;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:t9(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function se(e){return!!t6(e.response_format)||(e.tools?.some(e=>t9(e)||"function"===e.type&&!0===e.function.strict)??!1)}h=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,u=new WeakSet,x=function(e){if(eP(this,_,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eD),e instanceof eD)return eP(this,b,!0,"f"),this._emit("abort",e);if(e instanceof eC)return this._emit("error",e);if(e instanceof Error){let t=new eC(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eC(String(e)))};class st extends t8{constructor(){super(...arguments),S.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),t5(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(t4(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eC("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),ej(this,S,"m",A).call(this)}async finalMessage(){return await this.done(),ej(this,S,"m",O).call(this)}async finalFunctionToolCall(){return await this.done(),ej(this,S,"m",$).call(this)}async finalFunctionToolCallResult(){return await this.done(),ej(this,S,"m",k).call(this)}async totalUsage(){return await this.done(),ej(this,S,"m",I).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=ej(this,S,"m",O).call(this);t&&this._emit("finalMessage",t);let s=ej(this,S,"m",A).call(this);s&&this._emit("finalContent",s);let n=ej(this,S,"m",$).call(this);n&&this._emit("finalFunctionToolCall",n);let r=ej(this,S,"m",k).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",ej(this,S,"m",I).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),ej(this,S,"m",R).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(t7(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:i,...a}=t,o="string"!=typeof r&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(t9(e)){if(!e.$callback)throw new eC("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:r,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new eC("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:i}=e.function,a=u[r];if(a){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=ej(this,S,"m",P).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}S=new WeakSet,A=function(){return ej(this,S,"m",O).call(this).content??null},O=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(t4(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eC("stream ended without producing a ChatCompletionMessage with role=assistant")},$=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t4(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},k=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t5(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},I=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},R=function(e){if(null!=e.n&&e.n>1)throw new eC("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},P=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class ss extends st{static runTools(e,t,s){let n=new ss,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),t4(e)&&e.content&&this._emit("content",e.content)}}let sn={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class sr extends Error{}class si extends Error{}let sa=(e,t)=>{let s=e.length,n=0,r=e=>{throw new sr(`${e} at position ${n}`)},i=e=>{throw new si(`${e} at position ${n}`)},a=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||sn.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||sn.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||sn.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||sn.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||sn.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||sn.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let a=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(sn.STR&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let i={};try{for(;"}"!==e[n];){if(h(),n>=s&&sn.OBJ&t)return i;let r=o();h(),n++;try{let e=a();Object.defineProperty(i,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(sn.OBJ&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(sn.OBJ&t)return i;r("Expected '}' at end of object")}return n++,i},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(a()),h(),","===e[n]&&n++}catch(e){if(sn.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&sn.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(sn.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||sn.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(s){"-"===e.substring(a,n)&&sn.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return a()},so=e=>(function(e,t=sn.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return sa(e.trim(),t)})(e,sn.ALL^sn.NUM);class sl extends st{constructor(e){super(),j.add(this),E.set(this,void 0),N.set(this,void 0),T.set(this,void 0),eP(this,E,e,"f"),eP(this,N,[],"f")}get currentChatCompletionSnapshot(){return ej(this,T,"f")}static fromReadableStream(e){let t=new sl(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new sl(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),ej(this,j,"m",C).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))ej(this,j,"m",D).call(this,e);if(r.controller.signal?.aborted)throw new eD;return this._addChatCompletion(ej(this,j,"m",B).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),ej(this,j,"m",C).call(this),this._connected();let r=tk.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(ej(this,j,"m",B).call(this)),ej(this,j,"m",D).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new eD;return this._addChatCompletion(ej(this,j,"m",B).call(this))}[(E=new WeakMap,N=new WeakMap,T=new WeakMap,j=new WeakSet,C=function(){this.ended||eP(this,T,void 0,"f")},M=function(e){let t=ej(this,N,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},ej(this,N,"f")[e.index]=t),t},D=function(e){if(this.ended)return;let t=ej(this,j,"m",W).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=ej(this,j,"m",M).call(this,e);for(let t of(e.finish_reason&&(ej(this,j,"m",q).call(this,e),null!=n.current_tool_call_index&&ej(this,j,"m",L).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(ej(this,j,"m",q).call(this,e),null!=n.current_tool_call_index&&ej(this,j,"m",L).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},L=function(e,t){if(ej(this,j,"m",M).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=ej(this,E,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:t9(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},q=function(e){let t=ej(this,j,"m",M).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=ej(this,j,"m",U).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},B=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");let e=ej(this,T,"f");if(!e)throw new eC("request ended without sending any chunks");return eP(this,T,void 0,"f"),eP(this,N,[],"f"),function(e,t){var s;let{id:n,choices:r,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...i})=>{if(!s)throw new eC(`missing finish_reason for choice ${n}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eC(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eC(`missing function_call.arguments for choice ${n}`);if(!l)throw new eC(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...i,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==a)throw new eC(`missing choices[${n}].tool_calls[${s}].id
${sc(e)}`);if(null==i)throw new eC(`missing choices[${n}].tool_calls[${s}].type
${sc(e)}`);if(null==c)throw new eC(`missing choices[${n}].tool_calls[${s}].function.name
${sc(e)}`);if(null==l)throw new eC(`missing choices[${n}].tool_calls[${s}].function.arguments
${sc(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&se(t)?t7(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,ej(this,E,"f"))},U=function(){let e=ej(this,E,"f")?.response_format;return t6(e)?e:null},W=function(e){var t,s,n,r;let i=ej(this,T,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=eP(this,T,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,ej(this,E,"f")&&se(ej(this,E,"f")))){if("length"===l)throw new eK;if("content_filter"===l)throw new eV}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:f,role:p,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&ej(this,j,"m",U).call(this)&&(e.message.parsed=so(e.message.content))),m)for(let{index:t,id:s,type:n,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,a),s&&(o.id=s),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return t9(s)||s?.function.strict||!1}(ej(this,E,"f"),o)&&(o.function.parsed_arguments=so(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tk(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sc(e){return JSON.stringify(e)}class su extends sl{static fromReadableStream(e){let t=new su(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new su(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class sh extends t0{constructor(){super(...arguments),this.messages=new t3(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(t2`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(t2`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tD,{query:e,...t})}delete(e,t){return this._client.delete(t2`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eC(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eC(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>t7(t,e))}runTools(e,t){return e.stream?su.runTools(this._client,e,t):ss.runTools(this._client,e,t)}stream(e,t){return sl.createChatCompletion(this._client,e,t)}}sh.Messages=t3;class sd extends t0{constructor(){super(...arguments),this.completions=new sh(this._client)}}sd.Completions=sh;let sf=Symbol("brand.privateNullableHeaders"),sp=Array.isArray,sm=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,i]of function*(e){let t;if(!e)return;if(sf in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():sp(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=sp(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===i?(t.delete(r),s.add(n)):(t.append(r,i),s.delete(n))}}return{[sf]:!0,values:t,nulls:s}};class sg extends t0{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sm([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sy extends t0{create(e,t){return this._client.post("/audio/transcriptions",tW({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sw extends t0{create(e,t){return this._client.post("/audio/translations",tW({body:e,...t,__metadata:{model:e.model}},this._client))}}class s_ extends t0{constructor(){super(...arguments),this.transcriptions=new sy(this._client),this.translations=new sw(this._client),this.speech=new sg(this._client)}}s_.Transcriptions=sy,s_.Translations=sw,s_.Speech=sg;class sb extends t0{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(t2`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tD,{query:e,...t})}cancel(e,t){return this._client.post(t2`/batches/${e}/cancel`,t)}}class sv extends t0{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t2`/assistants/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t2`/assistants/${e}`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tD,{query:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t2`/assistants/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sx extends t0{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sS extends t0{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sA extends t0{constructor(){super(...arguments),this.sessions=new sx(this._client),this.transcriptionSessions=new sS(this._client)}}sA.Sessions=sx,sA.TranscriptionSessions=sS;class sO extends t0{create(e,t,s){return this._client.post(t2`/threads/${e}/messages`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t2`/threads/${n}/messages/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t2`/threads/${n}/messages/${e}`,{body:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t2`/threads/${e}/messages`,tD,{query:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(t2`/threads/${n}/messages/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s$ extends t0{retrieve(e,t,s){let{thread_id:n,run_id:r,...i}=t;return this._client.get(t2`/threads/${n}/runs/${r}/steps/${e}`,{query:i,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(t2`/threads/${n}/runs/${e}/steps`,tD,{query:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sk=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},sI=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sR extends t8{constructor(){super(...arguments),F.add(this),X.set(this,[]),z.set(this,{}),H.set(this,{}),K.set(this,void 0),V.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),Q.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0)}[(X=new WeakMap,z=new WeakMap,H=new WeakMap,K=new WeakMap,V=new WeakMap,G=new WeakMap,Y=new WeakMap,Q=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,F=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new J;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tk.fromReadableStream(e,this.controller);for await(let e of n)ej(this,F,"m",en).call(this,e);if(n.controller.signal?.aborted)throw new eD;return this._addRun(ej(this,F,"m",er).call(this))}toReadableStream(){return new tk(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))ej(this,F,"m",en).call(this,e);if(a.controller.signal?.aborted)throw new eD;return this._addRun(ej(this,F,"m",er).call(this))}static createThreadAssistantStream(e,t,s){let n=new J;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return ej(this,ee,"f")}currentRun(){return ej(this,et,"f")}currentMessageSnapshot(){return ej(this,K,"f")}currentRunStepSnapshot(){return ej(this,es,"f")}async finalRunSteps(){return await this.done(),Object.values(ej(this,z,"f"))}async finalMessages(){return await this.done(),Object.values(ej(this,H,"f"))}async finalRun(){if(await this.done(),!ej(this,V,"f"))throw Error("Final run was not received.");return ej(this,V,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},i=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))ej(this,F,"m",en).call(this,e);if(i.controller.signal?.aborted)throw new eD;return this._addRun(ej(this,F,"m",er).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))ej(this,F,"m",en).call(this,e);if(a.controller.signal?.aborted)throw new eD;return this._addRun(ej(this,F,"m",er).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(eQ(t)&&eQ(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!eQ(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}J=sR,en=function(e){if(!this.ended)switch(eP(this,ee,e,"f"),ej(this,F,"m",eo).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":ej(this,F,"m",eh).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ej(this,F,"m",ea).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":ej(this,F,"m",ei).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},er=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");if(!ej(this,V,"f"))throw Error("Final run has not been received");return ej(this,V,"f")},ei=function(e){let[t,s]=ej(this,F,"m",ec).call(this,e,ej(this,K,"f"));for(let e of(eP(this,K,t,"f"),ej(this,H,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=ej(this,G,"f")){if(ej(this,Y,"f"))switch(ej(this,Y,"f").type){case"text":this._emit("textDone",ej(this,Y,"f").text,ej(this,K,"f"));break;case"image_file":this._emit("imageFileDone",ej(this,Y,"f").image_file,ej(this,K,"f"))}eP(this,G,s.index,"f")}eP(this,Y,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==ej(this,G,"f")){let t=e.data.content[ej(this,G,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,ej(this,K,"f"));break;case"text":this._emit("textDone",t.text,ej(this,K,"f"))}}ej(this,K,"f")&&this._emit("messageDone",e.data),eP(this,K,void 0,"f")}},ea=function(e){let t=ej(this,F,"m",el).call(this,e);switch(eP(this,es,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==ej(this,Q,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(ej(this,Z,"f")&&this._emit("toolCallDone",ej(this,Z,"f")),eP(this,Q,e.index,"f"),eP(this,Z,t.step_details.tool_calls[e.index],"f"),ej(this,Z,"f")&&this._emit("toolCallCreated",ej(this,Z,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eP(this,es,void 0,"f"),"tool_calls"==e.data.step_details.type&&ej(this,Z,"f")&&(this._emit("toolCallDone",ej(this,Z,"f")),eP(this,Z,void 0,"f")),this._emit("runStepDone",e.data,t)}},eo=function(e){ej(this,X,"f").push(e),this._emit("event",e)},el=function(e){switch(e.event){case"thread.run.step.created":return ej(this,z,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=ej(this,z,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=J.accumulateDelta(t,s.delta);ej(this,z,"f")[e.data.id]=n}return ej(this,z,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":ej(this,z,"f")[e.data.id]=e.data}if(ej(this,z,"f")[e.data.id])return ej(this,z,"f")[e.data.id];throw Error("No snapshot available")},ec=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=ej(this,F,"m",eu).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eu=function(e,t){return J.accumulateDelta(t,e)},eh=function(e){switch(eP(this,et,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":eP(this,V,e.data,"f"),ej(this,Z,"f")&&(this._emit("toolCallDone",ej(this,Z,"f")),eP(this,Z,void 0,"f"))}};class sP extends t0{constructor(){super(...arguments),this.steps=new s$(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(t2`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t2`/threads/${n}/runs/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t2`/threads/${n}/runs/${e}`,{body:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t2`/threads/${e}/runs`,tD,{query:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(t2`/threads/${n}/runs/${e}/cancel`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sR.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=sm([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e1(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sR.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t2`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sR.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sP.Steps=s$;class sj extends t0{constructor(){super(...arguments),this.runs=new sP(this._client),this.messages=new sO(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t2`/threads/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t2`/threads/${e}`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(t2`/threads/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sR.createThreadAssistantStream(e,this._client.beta.threads,t)}}sj.Runs=sP,sj.Messages=sO;class sE extends t0{constructor(){super(...arguments),this.realtime=new sA(this._client),this.assistants=new sv(this._client),this.threads=new sj(this._client)}}sE.Realtime=sA,sE.Assistants=sv,sE.Threads=sj;class sN extends t0{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sT extends t0{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t2`/containers/${n}/files/${e}/content`,{...s,headers:sm([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sC extends t0{constructor(){super(...arguments),this.content=new sT(this._client)}create(e,t,s){return this._client.post(t2`/containers/${e}/files`,tW({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t2`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t2`/containers/${e}/files`,tD,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(t2`/containers/${n}/files/${e}`,{...s,headers:sm([{Accept:"*/*"},s?.headers])})}}sC.Content=sT;class sM extends t0{constructor(){super(...arguments),this.files=new sC(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(t2`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tD,{query:e,...t})}delete(e,t){return this._client.delete(t2`/containers/${e}`,{...t,headers:sm([{Accept:"*/*"},t?.headers])})}}sM.Files=sC;class sD extends t0{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&e9(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(e9(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sk(t)}),e)))}}class sL extends t0{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(t2`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(t2`/evals/${n}/runs/${e}/output_items`,tD,{query:r,...s})}}class sq extends t0{constructor(){super(...arguments),this.outputItems=new sL(this._client)}create(e,t,s){return this._client.post(t2`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(t2`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t2`/evals/${e}/runs`,tD,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(t2`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(t2`/evals/${n}/runs/${e}`,s)}}sq.OutputItems=sL;class sB extends t0{constructor(){super(...arguments),this.runs=new sq(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(t2`/evals/${e}`,t)}update(e,t,s){return this._client.post(t2`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tD,{query:e,...t})}delete(e,t){return this._client.delete(t2`/evals/${e}`,t)}}sB.Runs=sq;class sU extends t0{create(e,t){return this._client.post("/files",tW({body:e,...t},this._client))}retrieve(e,t){return this._client.get(t2`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tD,{query:e,...t})}delete(e,t){return this._client.delete(t2`/files/${e}`,t)}content(e,t){return this._client.get(t2`/files/${e}/content`,{...t,headers:sm([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await e1(t),i=await this.retrieve(e),Date.now()-r>s)throw new eq({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sW extends t0{}class sF extends t0{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sJ extends t0{constructor(){super(...arguments),this.graders=new sF(this._client)}}sJ.Graders=sF;class sX extends t0{create(e,t,s){return this._client.getAPIList(t2`/fine_tuning/checkpoints/${e}/permissions`,tM,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(t2`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(t2`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class sz extends t0{constructor(){super(...arguments),this.permissions=new sX(this._client)}}sz.Permissions=sX;class sH extends t0{list(e,t={},s){return this._client.getAPIList(t2`/fine_tuning/jobs/${e}/checkpoints`,tD,{query:t,...s})}}class sK extends t0{constructor(){super(...arguments),this.checkpoints=new sH(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(t2`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tD,{query:e,...t})}cancel(e,t){return this._client.post(t2`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(t2`/fine_tuning/jobs/${e}/events`,tD,{query:t,...s})}pause(e,t){return this._client.post(t2`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t2`/fine_tuning/jobs/${e}/resume`,t)}}sK.Checkpoints=sH;class sV extends t0{constructor(){super(...arguments),this.methods=new sW(this._client),this.jobs=new sK(this._client),this.checkpoints=new sz(this._client),this.alpha=new sJ(this._client)}}sV.Methods=sW,sV.Jobs=sK,sV.Checkpoints=sz,sV.Alpha=sJ;class sG extends t0{}class sY extends t0{constructor(){super(...arguments),this.graderModels=new sG(this._client)}}sY.GraderModels=sG;class sQ extends t0{createVariation(e,t){return this._client.post("/images/variations",tW({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tW({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class sZ extends t0{retrieve(e,t){return this._client.get(t2`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tM,e)}delete(e,t){return this._client.delete(t2`/models/${e}`,t)}}class s0 extends t0{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function s1(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||s2(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function s2(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s3 extends t8{constructor(e){super(),ed.add(this),ef.set(this,void 0),ep.set(this,void 0),em.set(this,void 0),eP(this,ef,e,"f")}static createResponse(e,t,s){let n=new s3(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),ej(this,ed,"m",eg).call(this);let i=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))ej(this,ed,"m",ey).call(this,r,i);if(n.controller.signal?.aborted)throw new eD;return ej(this,ed,"m",ew).call(this)}[(ef=new WeakMap,ep=new WeakMap,em=new WeakMap,ed=new WeakSet,eg=function(){this.ended||eP(this,ep,void 0,"f")},ey=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=ej(this,ed,"m",e_).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eC(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eC(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eC(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eC(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},ew=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");let e=ej(this,ep,"f");if(!e)throw new eC("request ended without sending any events");eP(this,ep,void 0,"f");let t=function(e,t){var s;return t&&(s=t,t6(s.text?.format))?s1(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,ej(this,ef,"f"));return eP(this,em,t,"f"),t},e_=function(e){let t=ej(this,ep,"f");if(!t){if("response.created"!==e.type)throw new eC(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return eP(this,ep,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eC(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eC(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":eP(this,ep,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=ej(this,em,"f");if(!e)throw new eC("stream ended without producing a ChatCompletion");return e}}class s4 extends t0{list(e,t={},s){return this._client.getAPIList(t2`/responses/${e}/input_items`,tD,{query:t,...s})}}class s5 extends t0{constructor(){super(...arguments),this.inputItems=new s4(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s2(e),e))}retrieve(e,t={},s){return this._client.get(t2`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(t2`/responses/${e}`,{...t,headers:sm([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>s1(t,e))}stream(e,t){return s3.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t2`/responses/${e}/cancel`,t)}}s5.InputItems=s4;class s8 extends t0{create(e,t,s){return this._client.post(t2`/uploads/${e}/parts`,tW({body:t,...s},this._client))}}class s6 extends t0{constructor(){super(...arguments),this.parts=new s8(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(t2`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(t2`/uploads/${e}/complete`,{body:t,...s})}}s6.Parts=s8;let s9=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class s7 extends t0{create(e,t,s){return this._client.post(t2`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t2`/vector_stores/${n}/file_batches/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(t2`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(t2`/vector_stores/${n}/file_batches/${e}/files`,tD,{query:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=sm([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e1(a);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(a).map(l);return await s9(c),await this.createAndPoll(e,{file_ids:o})}}class ne extends t0{create(e,t,s){return this._client.post(t2`/vector_stores/${e}/files`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t2`/vector_stores/${n}/files/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(t2`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t2`/vector_stores/${e}/files`,tD,{query:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(t2`/vector_stores/${n}/files/${e}`,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=sm([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),i=r.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e1(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(t2`/vector_stores/${n}/files/${e}/content`,tM,{...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class nt extends t0{constructor(){super(...arguments),this.files=new ne(this._client),this.fileBatches=new s7(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t2`/vector_stores/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t2`/vector_stores/${e}`,{body:t,...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tD,{query:e,...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t2`/vector_stores/${e}`,{...t,headers:sm([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(t2`/vector_stores/${e}/search`,tM,{body:t,method:"post",...s,headers:sm([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}nt.Files=ne,nt.FileBatches=s7;class ns{constructor({baseURL:e=sI("OPENAI_BASE_URL"),apiKey:t=sI("OPENAI_API_KEY"),organization:s=sI("OPENAI_ORG_ID")??null,project:n=sI("OPENAI_PROJECT_ID")??null,...r}={}){if(ev.set(this,void 0),this.completions=new sN(this),this.chat=new sd(this),this.embeddings=new sD(this),this.files=new sU(this),this.images=new sQ(this),this.audio=new s_(this),this.moderations=new s0(this),this.models=new sZ(this),this.fineTuning=new sV(this),this.graders=new sY(this),this.vectorStores=new nt(this),this.beta=new sE(this),this.batches=new sb(this),this.uploads=new s6(this),this.responses=new s5(this),this.evals=new sB(this),this.containers=new sM(this),void 0===t)throw new eC("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:s,project:n,...r,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&tt())throw new eC("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=i.baseURL,this.timeout=i.timeout??eb.DEFAULT_TIMEOUT,this.logger=i.logger??console;let a="warn";this.logLevel=a,this.logLevel=e3(i.logLevel,"ClientOptions.logLevel",this)??e3(sI("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??a,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),eP(this,ev,tu,"f"),this._options=i,this.apiKey=t,this.organization=s,this.project=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return sm([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n,r=e,i=function(e=tx){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||tx.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=th;if(void 0!==e.format){if(!tg.call(td,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=td[n],i=tx.filter;if(("function"==typeof e.filter||tw(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in ty?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tx.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tx.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tx.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tx.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tx.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tx.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tx.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tx.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tx.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tx.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tx.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tx.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tx.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):tw(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof r||null===r)return"";let o=ty[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(r)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===r[t]||tb(a,function e(t,s,n,r,i,a,o,l,c,u,h,d,f,p,m,g,y,w){var _,b;let v,x=t,S=w,A=0,O=!1;for(;void 0!==(S=S.get(tS))&&!O;){let e=S.get(t);if(A+=1,void 0!==e)if(e===A)throw RangeError("Cyclic object value");else O=!0;void 0===S.get(tS)&&(A=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=f?.(x):"comma"===n&&tw(x)&&(x=tm(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(a)return c&&!g?c(s,tx.encoder,y,"key",p):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,tx.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,tx.encoder,y,"value",p))]}return[m?.(s)+"="+m?.(String(x))]}let $=[];if(void 0===x)return $;if("comma"===n&&tw(x))g&&c&&(x=tm(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(tw(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let k=l?String(s).replace(/\./g,"%2E"):String(s),I=r&&tw(x)&&1===x.length?k+"[]":k;if(i&&tw(x)&&0===x.length)return I+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,O=tw(x)?"function"==typeof n?n(I,S):I:I+(d?"."+S:"["+S+"]");w.set(t,A);let k=new WeakMap;k.set(tS,w),tb($,e(b,O,n,r,i,a,o,l,"comma"===n&&g&&tw(x)?null:c,u,h,d,f,p,m,g,y,k))}return $}(r[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${te}`}defaultIdempotencyKey(){return`stainless-node-retry-${eE()}`}makeStatusError(e,t,s,n){return eM.generate(e,t,s,n)}buildURL(e,t){let s=new URL(eY(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(n)&&(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tN(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:i,url:a,timeout:o}=this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(i,{url:a,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(e9(this).debug(`[${l}] sending request`,e7({retryOfRequestLogID:s,method:n.method,url:a,options:n,headers:i.headers})),n.signal?.aborted)throw new eD;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eT),f=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new eD;let r=eN(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return e9(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),e9(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,e7({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),this.retryRequest(n,t,s??l);if(e9(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),e9(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,e7({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),r)throw new eq;throw new eL({cause:d})}let p=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${p}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${f-u}ms`;if(!d.ok){let e=this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tc(d.body),e9(this).info(`${m} - ${e}`),e9(this).debug(`[${l}] response error (${e})`,e7({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";e9(this).info(`${m} - ${r}`);let i=await d.text().catch(e=>eT(e).message),a=e0(i),o=a?void 0:i;throw e9(this).debug(`[${l}] response error (${r})`,e7({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return e9(this).info(m),e9(this).debug(`[${l}] response start`,e7({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tC(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:i,...a}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(r=e)}let a=n?.get("retry-after");if(a&&!r){let e=parseFloat(a);r=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await e1(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:i}=s,a=this.buildURL(r,i);"timeout"in s&&eZ("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:n,bodyHeaders:o,retryCount:t});return{req:{method:n,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:a,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let r={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),r[this.idempotencyHeader]=e.idempotencyKey);let i=sm([r,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...ti(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sm([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:to(e)}:ej(this,ev,"f").call(this,{body:e,headers:s})}}async function nn({prompt:e,model:t,maxTokens:s=100,temperature:n=.7}){if(!process.env.JAN_AI_API_URL)return{text:"",success:!1,error:"JAN_AI_API_URL is niet geconfigureerd in de omgevingsvariabelen"};try{let r=new ns({baseURL:process.env.JAN_AI_API_URL,apiKey:"sk-no-key-required"}),i=await r.chat.completions.create({model:t,messages:[{role:"user",content:e}],max_tokens:s,temperature:n});if(!i.choices?.[0]?.message?.content)return{text:"",success:!1,error:"Geen tekst gegenereerd."};return{text:i.choices[0].message.content,success:!0}}catch(e){return console.error("Fout bij het genereren van tekst met Jan.ai:",e),{text:"",success:!1,error:e.message||"Onbekende fout bij het genereren van tekst."}}}eb=ns,ev=new WeakMap,ns.OpenAI=eb,ns.DEFAULT_TIMEOUT=6e5,ns.OpenAIError=eC,ns.APIError=eM,ns.APIConnectionError=eL,ns.APIConnectionTimeoutError=eq,ns.APIUserAbortError=eD,ns.NotFoundError=eF,ns.ConflictError=eJ,ns.RateLimitError=ez,ns.BadRequestError=eB,ns.AuthenticationError=eU,ns.InternalServerError=eH,ns.PermissionDeniedError=eW,ns.UnprocessableEntityError=eX,ns.toFile=tQ,ns.Completions=sN,ns.Chat=sd,ns.Embeddings=sD,ns.Files=sU,ns.Images=sQ,ns.Audio=s_,ns.Moderations=s0,ns.Models=sZ,ns.FineTuning=sV,ns.Graders=sY,ns.VectorStores=nt,ns.Beta=sE,ns.Batches=sb,ns.Uploads=s6,ns.Responses=s5,ns.Evals=sB,ns.Containers=sM;var nr=s(45697);let ni=nr.z.object({text:nr.z.string().min(1,"Tekst is verplicht"),projectId:nr.z.string().min(1,"Project ID is verplicht"),currentTaskData:nr.z.object({title:nr.z.string().optional(),description:nr.z.string().optional(),status:nr.z.enum(["TODO","IN_PROGRESS","DONE"]).optional(),dueDate:nr.z.string().optional()}).optional()}),na=nr.z.object({action:nr.z.enum(["create_task","ask_question","unclear_intent"]),task:nr.z.object({title:nr.z.string().min(1,"Titel is verplicht").optional(),description:nr.z.string().optional(),status:nr.z.enum(["TODO","IN_PROGRESS","DONE"]).optional(),dueDate:nr.z.string().optional()}).optional(),question:nr.z.string().optional(),error:nr.z.string().optional()});async function no(e){try{let t,s=await (0,eI.getServerSession)(e$.N);if(!s?.user)return new eR.NextResponse("Unauthorized",{status:401});let n=await e.json(),{text:r,projectId:i,currentTaskData:a}=ni.parse(n);if(!await ek.z.project.findUnique({where:{id:i,userId:s.user.id}}))return new eR.NextResponse("Project niet gevonden",{status:404});let o=`De gebruiker zei: "${r}".`;a&&(o+=` De huidige (gedeeltelijke) taakgegevens zijn: ${JSON.stringify(a)}.`),o+=` Jouw taak is om de intentie van de gebruiker te bepalen. Als de gebruiker een taak wil aanmaken, extraheer dan de 'title', 'description', 'status' (moet 'TODO', 'IN_PROGRESS' of 'DONE' zijn) en 'dueDate' (formaat YYYY-MM-DD). Als niet alle informatie voor een taak compleet is, stel dan een vraag om de ontbrekende informatie te verkrijgen. Als de intentie onduidelijk is, geef dit dan aan. 
Reageer in JSON-formaat.
Voorbeeld reactie als alle info voor een taak aanwezig is:
{"action": "create_task", "task": {"title": "Titel van de taak", "description": "Beschrijving", "status": "TODO", "dueDate": "2024-12-31"}}
Voorbeeld reactie als info ontbreekt:
{"action": "ask_question", "question": "Wat is de titel van de taak?", "task": {"title": "Deel van titel"}}
Voorbeeld reactie als intentie onduidelijk is:
{"action": "unclear_intent", "question": "Ik begrijp je intentie niet. Kun je duidelijker zijn?"}`;let l=await nn({prompt:o,model:"llama-3-8b-instruct",maxTokens:300,temperature:.5});if(!l.success)return new eR.NextResponse(JSON.stringify({error:l.error||"AI-generatie mislukt"}),{status:500});try{t=na.parse(JSON.parse(l.text))}catch(e){return console.error("Fout bij parsen AI-antwoord:",e,"Raw AI text:",l.text),new eR.NextResponse(JSON.stringify({error:"AI antwoord kon niet worden geparsed."}),{status:500})}if("ask_question"===t.action||"unclear_intent"===t.action)return eR.NextResponse.json({action:t.action,question:t.question,task:t.task});if("create_task"===t.action&&t.task){let{title:e,description:n,status:r,dueDate:o}=t.task,l={...a,title:e||a?.title,description:n||a?.description,status:r||a?.status,dueDate:o||a?.dueDate};if(!l.title||!l.status)return eR.NextResponse.json({action:"ask_question",question:"De taak is nog niet compleet. Zorg voor een titel en status.",task:l},{status:400});let c=await ek.z.task.create({data:{title:l.title,description:l.description,status:l.status,dueDate:l.dueDate&&!isNaN(new Date(l.dueDate).getTime())?new Date(l.dueDate):null,projectId:i,userId:s.user.id}});return eR.NextResponse.json({action:"task_created",task:c})}return new eR.NextResponse(JSON.stringify({error:"Onverwachte AI respons"}),{status:500})}catch(e){if(e instanceof nr.z.ZodError)return new eR.NextResponse(JSON.stringify(e.issues),{status:422});return console.error("Fout in AI conversatie route:",e),new eR.NextResponse(JSON.stringify({error:"Er is een fout opgetreden bij AI conversatie"}),{status:500})}}let nl=new eS.AppRouteRouteModule({definition:{kind:eA.RouteKind.APP_ROUTE,page:"/api/ai/converse/route",pathname:"/api/ai/converse",filename:"route",bundlePath:"app/api/ai/converse/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/ai/converse/route.ts",nextConfigOutput:"",userland:ex}),{workAsyncStorage:nc,workUnitAsyncStorage:nu,serverHooks:nh}=nl;function nd(){return(0,eO.patchFetch)({workAsyncStorage:nc,workUnitAsyncStorage:nu})}},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>i});var n=s(31183),r=s(85663);let i={session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,s(13581).A)({name:"Sign in",credentials:{email:{label:"Email",type:"email",placeholder:"<EMAIL>"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&t.password&&await (0,r.UD)(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role||"USER"}:null}})],callbacks:{session:({session:e,token:t})=>({...e,user:{...e.user,id:t.id,role:t.role}}),jwt:({token:e,user:t})=>t?{...e,id:t.id,role:t.role}:e}}},19854:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var r=s(12269);Object.keys(r).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===r[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return r[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=a(t);if(s&&s.has(e))return s.get(e);var n={__proto__:null},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=r?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}return n.default=e,s&&s.set(e,n),n}(s(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(a=function(e){return e?s:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,s)=>{"use strict";s.d(t,{z:()=>r});var n=s(96330);let r=globalThis.prisma??new n.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[243,580,663,697,112],()=>s(11391));module.exports=n})();