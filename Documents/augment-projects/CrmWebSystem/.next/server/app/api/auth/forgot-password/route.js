(()=>{var e={};e.id=556,e.ids=[556],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},21111:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});let s=t(49526).createTransport({host:process.env.SMTP_HOST,port:Number(process.env.SMTP_PORT),secure:"true"===process.env.SMTP_SECURE,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASSWORD}});async function o({to:e,subject:r,text:t,html:o}){let a={from:process.env.SMTP_FROM,to:e,subject:r,text:t,html:o};try{await s.sendMail(a)}catch(e){throw console.error("Error sending email:",e),Error("Failed to send email")}}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=globalThis.prisma??new s.PrismaClient({log:["query"]})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93435:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>v,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>x});var o=t(96559),a=t(48088),i=t(37719),n=t(21111),u=t(31183),p=t(55511),c=t(32190),d=t(45697);let l=d.z.object({email:d.z.string().email("Ongeldig email adres")});async function x(e){try{let r=await e.json(),{email:t}=l.parse(r);if(!await u.z.user.findUnique({where:{email:t}}))return c.NextResponse.json({message:"Als er een account bestaat met dit email adres, ontvang je een reset link"},{status:200});let s=(0,p.randomBytes)(32).toString("hex"),o=new Date(Date.now()+36e5);await u.z.user.update({where:{email:t},data:{resetToken:s,resetTokenExpiry:o}});let a=`${process.env.NEXTAUTH_URL}/auth/reset-password?token=${s}`;return await (0,n.Z)({to:t,subject:"Wachtwoord resetten",text:`Klik op de volgende link om je wachtwoord te resetten: ${a}`,html:`
        <p>Klik op de volgende link om je wachtwoord te resetten:</p>
        <p><a href="${a}">${a}</a></p>
        <p>Deze link is 1 uur geldig.</p>
      `}),c.NextResponse.json({message:"Als er een account bestaat met dit email adres, ontvang je een reset link"},{status:200})}catch(e){if(e instanceof d.z.ZodError)return c.NextResponse.json({message:e.errors[0].message},{status:400});return c.NextResponse.json({message:"Er is iets misgegaan bij het aanvragen van de reset link"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/forgot-password/route",pathname:"/api/auth/forgot-password",filename:"route",bundlePath:"app/api/auth/forgot-password/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/forgot-password/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:v}=m;function w(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,697,526],()=>t(93435));module.exports=s})();