(()=>{var e={};e.id=14,e.ids=[14],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67153:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>w,POST:()=>w,authOptions:()=>h});var i=t(96559),a=t(48088),n=t(37719),u=t(31183),o=t(96330);function c(e){let r={};for(let t in e)void 0!==e[t]&&(r[t]=e[t]);return{data:r}}var d=t(85663),l=t(35426),p=t(13581);let h={adapter:function(e){return{createUser:({id:r,...t})=>e.user.create(c(t)),getUser:r=>e.user.findUnique({where:{id:r}}),getUserByEmail:r=>e.user.findUnique({where:{email:r}}),async getUserByAccount(r){let t=await e.account.findUnique({where:{provider_providerAccountId:r},include:{user:!0}});return t?.user??null},updateUser:({id:r,...t})=>e.user.update({where:{id:r},...c(t)}),deleteUser:r=>e.user.delete({where:{id:r}}),linkAccount:r=>e.account.create({data:r}),unlinkAccount:r=>e.account.delete({where:{provider_providerAccountId:r}}),async getSessionAndUser(r){let t=await e.session.findUnique({where:{sessionToken:r},include:{user:!0}});if(!t)return null;let{user:s,...i}=t;return{user:s,session:i}},createSession:r=>e.session.create(c(r)),updateSession:r=>e.session.update({where:{sessionToken:r.sessionToken},...c(r)}),deleteSession:r=>e.session.delete({where:{sessionToken:r}}),async createVerificationToken(r){let t=await e.verificationToken.create(c(r));return"id"in t&&t.id&&delete t.id,t},async useVerificationToken(r){try{let t=await e.verificationToken.delete({where:{identifier_token:r}});return"id"in t&&t.id&&delete t.id,t}catch(e){if(e instanceof o.Prisma.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(r,t)=>e.account.findFirst({where:{providerAccountId:r,provider:t}}),createAuthenticator:async r=>e.authenticator.create(c(r)),getAuthenticator:async r=>e.authenticator.findUnique({where:{credentialID:r}}),listAuthenticatorsByUserId:async r=>e.authenticator.findMany({where:{userId:r}}),updateAuthenticatorCounter:async(r,t)=>e.authenticator.update({where:{credentialID:r},data:{counter:t}})}}(u.z),session:{strategy:"jwt"},pages:{signIn:"/auth/login"},providers:[(0,p.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Wachtwoord",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email en wachtwoord zijn verplicht");let r=await u.z.user.findUnique({where:{email:e.email}});if(!r||!r.password)throw Error("Gebruiker niet gevonden");if(!await (0,d.UD)(e.password,r.password))throw Error("Ongeldig wachtwoord");return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}})],callbacks:{session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r}){let t=await u.z.user.findFirst({where:{email:e.email}});return t?{id:t.id,name:t.name,email:t.email,picture:t.image,role:t.role}:(r&&(e.id=r?.id),e)}}},w=(0,l.default)(h),m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:g}=m;function y(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,663,112],()=>t(67153));module.exports=s})();