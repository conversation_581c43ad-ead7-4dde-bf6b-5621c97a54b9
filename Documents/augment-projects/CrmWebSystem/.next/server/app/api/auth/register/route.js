(()=>{var e={};e.id=612,e.ids=[612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11249:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>g,serverHooks:()=>q,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>x});var i=t(96559),a=t(48088),o=t(37719),n=t(21111),u=t(31183),p=t(85663),c=t(55511),l=t(32190),m=t(45697);let d=m.z.object({name:m.z.string().min(2,"Naam moet minimaal 2 karakters bevatten"),email:m.z.string().email("Ongeldig email adres"),password:m.z.string().min(6,"Wachtwoord moet minimaal 6 karakters bevatten")});async function x(e){try{let r=await e.json(),{name:t,email:s,password:i}=d.parse(r);if(await u.z.user.findUnique({where:{email:s}}))return l.NextResponse.json({message:"Gebruiker met dit email adres bestaat al"},{status:400});let a=await (0,p.tW)(i,12),o=(0,c.randomBytes)(32).toString("hex");Date.now();let m=await u.z.user.create({data:{name:t,email:s,password:a}}),x=`${process.env.NEXTAUTH_URL}/auth/verify-email?token=${o}`;return await (0,n.Z)({to:s,subject:"Verifieer je email adres",text:`Klik op de volgende link om je email adres te verifi\xebren: ${x}`,html:`
        <p>Welkom bij CRM Web System!</p>
        <p>Klik op de volgende link om je email adres te verifi\xebren:</p>
        <p><a href="${x}">${x}</a></p>
        <p>Deze link is 24 uur geldig.</p>
      `}),l.NextResponse.json({message:"Registratie succesvol. Controleer je email voor verificatie.",user:{name:m.name,email:m.email}},{status:201})}catch(e){if(e instanceof m.z.ZodError)return l.NextResponse.json({message:e.errors[0].message},{status:400});return l.NextResponse.json({message:"Er is iets misgegaan bij het registreren"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:h,serverHooks:q}=g;function j(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:h})}},14985:e=>{"use strict";e.exports=require("dns")},21111:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});let s=t(49526).createTransport({host:process.env.SMTP_HOST,port:Number(process.env.SMTP_PORT),secure:"true"===process.env.SMTP_SECURE,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASSWORD}});async function i({to:e,subject:r,text:t,html:i}){let a={from:process.env.SMTP_FROM,to:e,subject:r,text:t,html:i};try{await s.sendMail(a)}catch(e){throw console.error("Error sending email:",e),Error("Failed to send email")}}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient({log:["query"]})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,697,526],()=>t(11249));module.exports=s})();