(()=>{var e={};e.id=478,e.ids=[478],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(96559),n=r(48088),o=r(37719),i=r(31183),u=r(85663),p=r(32190),d=r(45697);let c=d.z.object({token:d.z.string(),password:d.z.string().min(6,"Wachtwoord moet minimaal 6 karakters bevatten")});async function l(e){try{let t=await e.json(),{token:r,password:s}=c.parse(t),a=await i.z.user.findFirst({where:{resetToken:r,resetTokenExpiry:{gt:new Date}}});if(!a)return p.NextResponse.json({message:"Ongeldige of verlopen reset link"},{status:400});let n=await (0,u.tW)(s,12);return await i.z.user.update({where:{id:a.id},data:{password:n,resetToken:null,resetTokenExpiry:null}}),p.NextResponse.json({message:"Wachtwoord succesvol gewijzigd"},{status:200})}catch(e){if(e instanceof d.z.ZodError)return p.NextResponse.json({message:e.errors[0].message},{status:400});return p.NextResponse.json({message:"Er is iets misgegaan bij het resetten van je wachtwoord"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/reset-password/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:x}=g;function h(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,580,663,697],()=>r(15889));module.exports=s})();