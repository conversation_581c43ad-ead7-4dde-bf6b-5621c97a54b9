(()=>{var e={};e.id=887,e.ids=[887],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49884:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{POST:()=>l});var i=t(96559),a=t(48088),n=t(37719),o=t(31183),u=t(32190),p=t(45697);let c=p.z.object({token:p.z.string()});async function l(e){try{let r=await e.json(),{token:t}=c.parse(r),s=await o.z.user.findFirst({where:{verificationToken:t,verificationTokenExpiry:{gt:new Date}}});if(!s)return u.NextResponse.json({message:"Ongeldige of verlopen verificatie link"},{status:400});return await o.z.user.update({where:{id:s.id},data:{emailVerified:new Date,verificationToken:null,verificationTokenExpiry:null}}),u.NextResponse.json({message:"Email succesvol geverifieerd"},{status:200})}catch(e){if(e instanceof p.z.ZodError)return u.NextResponse.json({message:e.errors[0].message},{status:400});return u.NextResponse.json({message:"Er is iets misgegaan bij het verifi\xebren van je email"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/verify-email/route",pathname:"/api/auth/verify-email",filename:"route",bundlePath:"app/api/auth/verify-email/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/verify-email/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:g}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,697],()=>t(49884));module.exports=s})();