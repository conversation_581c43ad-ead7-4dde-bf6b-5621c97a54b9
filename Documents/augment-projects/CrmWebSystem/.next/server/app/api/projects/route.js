(()=>{var e={};e.id=64,e.ids=[64],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>i});var s=t(31183),n=t(85663);let i={session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,t(13581).A)({name:"Sign in",credentials:{email:{label:"Email",type:"email",placeholder:"<EMAIL>"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e.password)return null;let r=await s.z.user.findUnique({where:{email:e.email}});return r&&r.password&&await (0,n.UD)(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role||"USER"}:null}})],callbacks:{session:({session:e,token:r})=>({...e,user:{...e.user,id:r.id,role:r.role}}),jwt:({token:e,user:r})=>r?{...e,id:r.id,role:r.role}:e}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=n?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84241:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>y,POST:()=>f});var n=t(96559),i=t(48088),a=t(37719),o=t(12909),u=t(31183),p=t(19854),c=t(32190),l=t(45697);let d=l.z.object({name:l.z.string().min(1,"Projectnaam is verplicht").max(100),description:l.z.string().max(500).optional()});async function f(e){try{let r=await (0,p.getServerSession)(o.N);if(!r?.user)return new c.NextResponse("Unauthorized",{status:401});let t=await e.json(),s=d.parse(t),n=await u.z.project.create({data:{name:s.name,description:s.description,userId:r.user.id}});return c.NextResponse.json(n)}catch(e){if(e instanceof l.z.ZodError)return new c.NextResponse(JSON.stringify(e.issues),{status:422});return new c.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}async function y(){try{let e=await (0,p.getServerSession)(o.N);if(!e?.user)return new c.NextResponse("Unauthorized",{status:401});let r=await u.z.project.findMany({where:{userId:e.user.id},include:{_count:{select:{tasks:!0}}},orderBy:{createdAt:"desc"}});return c.NextResponse.json(r)}catch(e){return new c.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:g}=m;function j(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,697,112],()=>t(84241));module.exports=s})();