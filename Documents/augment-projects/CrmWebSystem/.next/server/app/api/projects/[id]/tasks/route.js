(()=>{var e={};e.id=829,e.ids=[829],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(31183),i=r(85663);let n={session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,r(13581).A)({name:"Sign in",credentials:{email:{label:"Email",type:"email",placeholder:"<EMAIL>"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e.password)return null;let t=await s.z.user.findUnique({where:{email:e.email}});return t&&t.password&&await (0,i.UD)(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role||"USER"}:null}})],callbacks:{session:({session:e,token:t})=>({...e,user:{...e.user,id:t.id,role:t.role}}),jwt:({token:e,user:t})=>t?{...e,id:t.id,role:t.role}:e}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var i=r(12269);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=i?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(s,n,o):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84463:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>w,POST:()=>f});var i=r(96559),n=r(48088),a=r(37719),o=r(12909),u=r(31183),p=r(19854),d=r(32190),c=r(45697);let l=c.z.object({title:c.z.string().min(1,"Titel is verplicht").max(100),description:c.z.string().max(500).optional(),status:c.z.enum(["TODO","IN_PROGRESS","DONE"]),dueDate:c.z.string().optional()});async function f(e,{params:t}){try{let r=await (0,p.getServerSession)(o.N);if(!r?.user)return new d.NextResponse("Unauthorized",{status:401});if(!await u.z.project.findUnique({where:{id:t.id,userId:r.user.id}}))return new d.NextResponse("Project niet gevonden",{status:404});let s=await e.json(),i=l.parse(s),n=await u.z.task.create({data:{title:i.title,description:i.description,status:i.status,dueDate:i.dueDate&&!isNaN(new Date(i.dueDate).getTime())?new Date(i.dueDate):null,projectId:t.id,userId:r.user.id}});return d.NextResponse.json(n)}catch(e){if(e instanceof c.z.ZodError)return new d.NextResponse(JSON.stringify(e.issues),{status:422});return new d.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}async function w(e,{params:t}){try{let e=await (0,p.getServerSession)(o.N);if(!e?.user)return new d.NextResponse("Unauthorized",{status:401});if(!await u.z.project.findUnique({where:{id:t.id,userId:e.user.id}}))return new d.NextResponse("Project niet gevonden",{status:404});let r=await u.z.task.findMany({where:{projectId:t.id},orderBy:{createdAt:"desc"}});return d.NextResponse.json(r)}catch(e){return new d.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/projects/[id]/tasks/route",pathname:"/api/projects/[id]/tasks",filename:"route",bundlePath:"app/api/projects/[id]/tasks/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/tasks/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:g,serverHooks:m}=x;function j(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:g})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,580,663,697,112],()=>r(84463));module.exports=s})();