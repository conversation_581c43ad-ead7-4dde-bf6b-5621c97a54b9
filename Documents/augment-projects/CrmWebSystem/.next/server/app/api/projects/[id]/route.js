(()=>{var e={};e.id=514,e.ids=[514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>i});var s=t(31183),n=t(85663);let i={session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,t(13581).A)({name:"Sign in",credentials:{email:{label:"Email",type:"email",placeholder:"<EMAIL>"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e.password)return null;let r=await s.z.user.findUnique({where:{email:e.email}});return r&&r.password&&await (0,n.UD)(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role||"USER"}:null}})],callbacks:{session:({session:e,token:r})=>({...e,user:{...e.user,id:r.id,role:r.role}}),jwt:({token:e,user:r})=>r?{...e,id:r.id,role:r.role}:e}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var u=n?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(s,i,u):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient({log:["query"]})},33872:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>f,GET:()=>l,PATCH:()=>d});var n=t(96559),i=t(48088),o=t(37719),u=t(12909),a=t(31183),p=t(19854),c=t(32190);async function l(e,{params:r}){try{let e=await (0,p.getServerSession)(u.N);if(!e?.user)return new c.NextResponse("Unauthorized",{status:401});let t=await a.z.project.findUnique({where:{id:r.id,userId:e.user.id},include:{tasks:{orderBy:{createdAt:"desc"}}}});if(!t)return new c.NextResponse("Project niet gevonden",{status:404});return c.NextResponse.json(t)}catch(e){return new c.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}async function d(e,{params:r}){try{let t=await (0,p.getServerSession)(u.N);if(!t?.user)return new c.NextResponse("Unauthorized",{status:401});let{name:s,description:n,status:i}=await e.json(),o=await a.z.project.update({where:{id:r.id,userId:t.user.id},data:{name:s,description:n,status:i}});return c.NextResponse.json(o)}catch(e){return new c.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}async function f(e,{params:r}){try{let e=await (0,p.getServerSession)(u.N);if(!e?.user)return new c.NextResponse("Unauthorized",{status:401});return await a.z.project.delete({where:{id:r.id,userId:e.user.id}}),new c.NextResponse(null,{status:204})}catch(e){return new c.NextResponse(JSON.stringify({error:"Er is een fout opgetreden"}),{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/[id]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:x,serverHooks:g}=w;function j(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,112],()=>t(33872));module.exports=s})();