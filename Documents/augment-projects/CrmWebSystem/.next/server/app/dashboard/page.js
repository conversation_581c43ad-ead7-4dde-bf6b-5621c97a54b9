(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9533:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55757:(e,r,t)=>{Promise.resolve().then(t.bind(t,58061))},58061:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),a=t(91821),n=t(96834),i=t(29523),o=t(44493),d=t(82136),l=t(16189),c=t(43210);function u(){let e=(0,l.useRouter)(),{data:r}=(0,d.useSession)(),[t,u]=(0,c.useState)(null),[m,x]=(0,c.useState)(!0),[p,h]=(0,c.useState)(null);return m?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):p||!t?(0,s.jsx)(a.Fc,{variant:"destructive",children:(0,s.jsx)(a.TN,{children:p||"Kon de dashboard statistieken niet laden"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold tracking-tight gradient-text",children:["Welkom terug, ",r?.user?.name]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Hier is een overzicht van je projecten en taken"})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(o.Zp,{className:"glow-accent border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Actieve Projecten"}),(0,s.jsx)(n.E,{variant:"default",className:"bg-primary/20 text-primary border-primary/30",children:t.activeProjects})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:t.activeProjects}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Projecten in uitvoering"})]})]}),(0,s.jsxs)(o.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Afgeronde Projecten"}),(0,s.jsx)(n.E,{variant:"secondary",className:"bg-secondary/20 text-secondary-foreground border-secondary/30",children:t.completedProjects})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:t.completedProjects}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Succesvol afgerond"})]})]}),(0,s.jsxs)(o.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Totaal Taken"}),(0,s.jsx)(n.E,{variant:"outline",className:"border-primary/30 text-primary",children:t.totalTasks})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:t.totalTasks}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alle taken"})]})]}),(0,s.jsxs)(o.Zp,{className:"border-primary/20 hover:border-primary/40 transition-all duration-300",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:"Afgeronde Taken"}),(0,s.jsx)(n.E,{variant:"secondary",className:"bg-secondary/20 text-secondary-foreground border-secondary/30",children:t.completedTasks})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold gradient-text",children:t.completedTasks}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Succesvol afgerond"})]})]})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Recente Projecten"})}),(0,s.jsx)(o.Wu,{children:0===t.recentProjects.length?(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Geen recente projecten gevonden"}):(0,s.jsx)("div",{className:"space-y-4",children:t.recentProjects.map(r=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:r.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[r._count.tasks," ",1===r._count.tasks?"taak":"taken"]})]}),(0,s.jsx)(i.$,{variant:"ghost",onClick:()=>e.push(`/dashboard/projects/${r.id}`),children:"Bekijken"})]},r.id))})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Snelle Acties"})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)(i.$,{className:"w-full justify-start glow-accent hover:shadow-lg transition-all duration-300",onClick:()=>e.push("/dashboard/projects/new"),children:"Nieuw Project"}),(0,s.jsx)(i.$,{className:"w-full justify-start border-primary/30 hover:border-primary/50 transition-all duration-300",variant:"outline",onClick:()=>e.push("/dashboard/projects"),children:"Alle Projecten"})]})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/page.tsx","default")},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(60687);t(43210);var a=t(24224),n=t(4780);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),e),...t})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},92141:(e,r,t)=>{Promise.resolve().then(t.bind(t,80559))},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var s=t(60687);t(43210);var a=t(8730),n=t(24224),i=t(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:t=!1,...n}){let d=t?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),e),...n})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,628,658,814,928,960,828],()=>t(9533));module.exports=s})();