(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6825:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["dashboard",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29912)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/projects/page",pathname:"/dashboard/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24018:(e,r,t)=>{Promise.resolve().then(t.bind(t,29912))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29912:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},47578:(e,r,t)=>{Promise.resolve().then(t.bind(t,50646))},50646:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(60687),a=t(26001);let n=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var i=t(16189),o=t(43210),d=t(91821),l=t(74654),c=t(96834),u=t(29523),p=t(44493),m=t(4780);function v({className:e,...r}){return(0,s.jsx)("div",{className:(0,m.cn)("animate-spin",e),...r,children:(0,s.jsxs)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}function h(){let e=(0,i.useRouter)(),[r,t]=(0,o.useState)([]),[m,h]=(0,o.useState)(!0),[x,g]=(0,o.useState)(null);return m?(0,s.jsx)("div",{className:"flex h-[50vh] items-center justify-center",children:(0,s.jsx)(v,{className:"h-8 w-8"})}):x?(0,s.jsx)(d.Fc,{variant:"destructive",children:(0,s.jsx)(d.TN,{children:x})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"Projecten"}),(0,s.jsx)(a.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsxs)(u.$,{onClick:()=>e.push("/dashboard/projects/new"),className:"glow-accent hover:shadow-lg transition-all duration-300",children:[(0,s.jsx)(n,{className:"mr-2 h-4 w-4"}),"Nieuw Project"]})})]}),(0,s.jsx)(l.g1,{children:(0,s.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3",children:r.map((r,t)=>(0,s.jsx)(l.YE,{delay:.1*t,children:(0,s.jsx)(a.P.div,{whileHover:{scale:1.02},transition:{duration:.2},children:(0,s.jsxs)(p.Zp,{className:"h-full border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{className:"gradient-text",children:r.name}),(0,s.jsx)(p.BT,{children:r.description})]}),(0,s.jsx)(p.Wu,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c.E,{variant:"ACTIVE"===r.status?"default":"COMPLETED"===r.status?"secondary":"outline",className:"ACTIVE"===r.status?"bg-primary/20 text-primary border-primary/30":"COMPLETED"===r.status?"bg-secondary/20 text-secondary-foreground border-secondary/30":"border-primary/30 text-primary",children:"ACTIVE"===r.status?"Actief":"COMPLETED"===r.status?"Afgerond":"On Hold"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[r._count.tasks," ",1===r._count.tasks?"taak":"taken"]})]})}),(0,s.jsx)(p.wL,{children:(0,s.jsx)(a.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"w-full",children:(0,s.jsx)(u.$,{variant:"outline",className:"w-full border-primary/30 hover:border-primary/50 hover:bg-primary/10 transition-all duration-300",onClick:()=>e.push(`/dashboard/projects/${r.id}`),children:"Bekijk Details"})})})]})})},r.id))})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(60687);t(43210);var a=t(24224),n=t(4780);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),e),...t})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var s=t(60687);t(43210);var a=t(8730),n=t(24224),i=t(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:t=!1,...n}){let d=t?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),e),...n})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,628,658,814,928,960,828],()=>t(6825));module.exports=s})();