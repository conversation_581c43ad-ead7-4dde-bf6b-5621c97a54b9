(()=>{var e={};e.id=367,e.ids=[367],e.modules={2919:(e,t,s)=>{Promise.resolve().then(s.bind(s,65975))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var r=s(60687);s(43210);var a=s(25911),n=s(78272),i=s(13964),o=s(3589),d=s(4780);function l({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"size-4"})})}},16831:(e,t,s)=>{Promise.resolve().then(s.bind(s,34181))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34181:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(91821),n=s(29523),i=s(44493),o=s(89667),d=s(15079),l=s(34729),c=s(82136),u=s(16189),p=s(43210);function m({params:e}){let t=(0,u.useRouter)(),{data:s}=(0,c.useSession)(),[m,x]=(0,p.useState)(null),[h,v]=(0,p.useState)(!0),[f,g]=(0,p.useState)(null),[b,j]=(0,p.useState)(!1),y=async s=>{if(s.preventDefault(),m){j(!0),g(null);try{if(!(await fetch(`/api/projects/${e.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).ok)throw Error("Fout bij het bijwerken van het project");t.push(`/dashboard/projects/${e.id}`)}catch(e){g(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{j(!1)}}};return h?(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):f||!m?(0,r.jsx)(a.Fc,{variant:"destructive",children:(0,r.jsx)(a.TN,{children:f||"Project niet gevonden"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Project Bewerken"}),(0,r.jsx)(n.$,{variant:"outline",onClick:()=>t.push(`/dashboard/projects/${e.id}`),children:"Terug"})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Project Details"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"name",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Naam"}),(0,r.jsx)(o.p,{id:"name",value:m.name,onChange:e=>x({...m,name:e.target.value}),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Beschrijving"}),(0,r.jsx)(l.T,{id:"description",value:m.description||"",onChange:e=>x({...m,description:e.target.value}),rows:4})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"status",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Status"}),(0,r.jsxs)(d.l6,{value:m.status,onValueChange:e=>x({...m,status:e}),children:[(0,r.jsx)(d.bq,{children:(0,r.jsx)(d.yv,{placeholder:"Selecteer status"})}),(0,r.jsxs)(d.gC,{children:[(0,r.jsx)(d.eb,{value:"ACTIVE",children:"Actief"}),(0,r.jsx)(d.eb,{value:"COMPLETED",children:"Afgerond"}),(0,r.jsx)(d.eb,{value:"ARCHIVED",children:"Gearchiveerd"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>t.push(`/dashboard/projects/${e.id}`),children:"Annuleren"}),(0,r.jsx)(n.$,{type:"submit",disabled:b,children:b?"Opslaan...":"Opslaan"})]})]})})]})]})}},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},87697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l={children:["",{children:["dashboard",{children:["projects",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65975)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/edit/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/projects/[id]/edit/page",pathname:"/dashboard/projects/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>d});var r=s(60687);s(43210);var a=s(24224),n=s(4780);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...s})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,628,658,814,928,960,262,828],()=>s(87697));module.exports=r})();