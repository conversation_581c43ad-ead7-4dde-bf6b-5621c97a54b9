(()=>{var e={};e.id=377,e.ids=[377],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx","default")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var a=s(60687);s(43210);var r=s(25911),i=s(78272),n=s(13964),d=s(3589),o=s(4780);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(d.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},35423:(e,t,s)=>{Promise.resolve().then(s.bind(s,13111))},60145:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(60687),r=s(91821),i=s(29523),n=s(44493),d=s(89667),o=s(15079),l=s(34729),c=s(82136),u=s(16189),p=s(43210);function m({params:e}){let t=(0,u.useRouter)(),{data:s}=(0,c.useSession)(),[m,x]=(0,p.useState)(null),[h,v]=(0,p.useState)(!0),[f,g]=(0,p.useState)(!1),[b,j]=(0,p.useState)(null),y=async s=>{if(s.preventDefault(),m){g(!0),j(null);try{if(!(await fetch(`/api/projects/${e.id}/tasks/${e.taskId}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).ok)throw Error("Fout bij het bijwerken van de taak");t.push(`/dashboard/projects/${e.id}`)}catch(e){j(e instanceof Error?e.message:"Er is een fout opgetreden")}finally{g(!1)}}},w=async()=>{if(confirm("Weet je zeker dat je deze taak wilt verwijderen?")){g(!0),j(null);try{if(!(await fetch(`/api/projects/${e.id}/tasks/${e.taskId}`,{method:"DELETE"})).ok)throw Error("Fout bij het verwijderen van de taak");t.push(`/dashboard/projects/${e.id}`)}catch(e){j(e instanceof Error?e.message:"Er is een fout opgetreden"),g(!1)}}};return h?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):b||!m?(0,a.jsx)(r.Fc,{variant:"destructive",children:(0,a.jsx)(r.TN,{children:b||"Taak niet gevonden"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:"Taak Bewerken"}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>t.push(`/dashboard/projects/${e.id}`),children:"Terug"})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Taak Details"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"title",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Titel"}),(0,a.jsx)(d.p,{id:"title",value:m.title,onChange:e=>x({...m,title:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Beschrijving"}),(0,a.jsx)(l.T,{id:"description",value:m.description||"",onChange:e=>x({...m,description:e.target.value}),rows:4})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"status",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Status"}),(0,a.jsxs)(o.l6,{value:m.status,onValueChange:e=>x({...m,status:e}),children:[(0,a.jsx)(o.bq,{children:(0,a.jsx)(o.yv,{placeholder:"Selecteer status"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"TODO",children:"Te Doen"}),(0,a.jsx)(o.eb,{value:"IN_PROGRESS",children:"In Behandeling"}),(0,a.jsx)(o.eb,{value:"DONE",children:"Afgerond"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"dueDate",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Deadline"}),(0,a.jsx)(d.p,{id:"dueDate",type:"date",value:m.dueDate?new Date(m.dueDate).toISOString().split("T")[0]:"",onChange:e=>x({...m,dueDate:e.target.value?new Date(e.target.value):null})})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(i.$,{type:"button",variant:"destructive",onClick:w,disabled:f,children:"Verwijderen"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>t.push(`/dashboard/projects/${e.id}`),children:"Annuleren"}),(0,a.jsx)(i.$,{type:"submit",disabled:f,children:f?"Opslaan...":"Opslaan"})]})]})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},89049:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["dashboard",{children:["projects",{children:["[id]",{children:["tasks",{children:["[taskId]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13111)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/tasks/[taskId]/edit/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/projects/[id]/tasks/[taskId]/edit/page",pathname:"/dashboard/projects/[id]/tasks/[taskId]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>d,TN:()=>o});var a=s(60687);s(43210);var r=s(24224),i=s(4780);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...s}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...s})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},95671:(e,t,s)=>{Promise.resolve().then(s.bind(s,60145))}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[243,628,658,814,928,960,262,828],()=>s(89049));module.exports=a})();