(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(91821),n=r(96834),i=r(29523),d=r(44493),o=r(82136),c=r(16189),l=r(43210);function u({params:e}){let t=(0,c.useRouter)(),{data:r}=(0,o.useSession)(),[u,p]=(0,l.useState)(null),[x,m]=(0,l.useState)(!0),[h,v]=(0,l.useState)(null);return x?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"})}):h||!u?(0,s.jsx)(a.Fc,{variant:"destructive",children:(0,s.jsx)(a.TN,{children:h||"Project niet gevonden"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:u.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Aangemaakt op"," ",new Date(u.createdAt).toLocaleDateString("nl-NL")]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>t.push(`/dashboard/projects/${u.id}/edit`),children:"Bewerken"}),(0,s.jsx)(i.$,{onClick:()=>t.push(`/dashboard/projects/${u.id}/tasks/new`),children:"Nieuwe Taak"})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Status"})}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(n.E,{variant:"ACTIVE"===u.status?"default":"COMPLETED"===u.status?"secondary":"outline",children:"ACTIVE"===u.status?"Actief":"COMPLETED"===u.status?"Afgerond":"Gearchiveerd"})}),u.description&&(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:u.description})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(d.ZB,{children:"Taken"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[u.tasks.length," taken"]})]})}),(0,s.jsx)(d.Wu,{children:0===u.tasks.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Geen taken gevonden"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(i.$,{variant:"link",onClick:()=>t.push(`/dashboard/projects/${u.id}/tasks/new`),children:"Voeg je eerste taak toe"})})]}):(0,s.jsx)("div",{className:"space-y-4",children:u.tasks.map(e=>(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n.E,{variant:"DONE"===e.status?"default":"IN_PROGRESS"===e.status?"secondary":"outline",children:"DONE"===e.status?"Afgerond":"IN_PROGRESS"===e.status?"In Behandeling":"Te Doen"}),e.dueDate&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(e.dueDate).toLocaleDateString("nl-NL")}),(0,s.jsx)(i.$,{variant:"ghost",onClick:()=>t.push(`/dashboard/projects/${u.id}/tasks/${e.id}`),children:"Bekijken"})]})]})})},e.id))})})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17584:(e,t,r)=>{Promise.resolve().then(r.bind(r,8638))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),d=r(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(t,o);let c={children:["",{children:["dashboard",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36880)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/projects/[id]/page",pathname:"/dashboard/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36880:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/projects/[id]/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},82736:(e,t,r)=>{Promise.resolve().then(r.bind(r,36880))},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>o});var s=r(60687);r(43210);var a=r(24224),n=r(4780);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...r})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(4780);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:r=!1,...n}){let o=r?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:t}),e),...n})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,628,658,814,928,960,828],()=>r(28261));module.exports=s})();