(()=>{var e={};e.id=631,e.ids=[631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8405:(e,t,s)=>{Promise.resolve().then(s.bind(s,22221))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22221:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(60687),r=s(91821),n=s(29523),i=s(44493),o=s(89667),d=s(54300),l=s(35950),c=s(27479),u=s(34729),p=s(74654),m=s(26001),h=s(82136),x=s(10218),g=s(43210),v=s(52581);function f(){let{data:e}=(0,h.useSession)(),{theme:t,setTheme:s}=(0,x.D)(),[f,j]=(0,g.useState)(!1),[b,y]=(0,g.useState)({name:"",email:"",bio:"",notifications:{email:!0,push:!1,marketing:!1}}),k=async()=>{j(!0);try{await new Promise(e=>setTimeout(e,1e3)),v.oR.success("Instellingen succesvol opgeslagen!")}catch(e){v.oR.error("Er is iets misgegaan bij het opslaan van de instellingen.")}finally{j(!1)}};return(0,a.jsx)(p.g1,{children:(0,a.jsx)(p._A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"Instellingen"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Beheer je account instellingen en voorkeuren."})]}),(0,a.jsx)(l.w,{}),(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsx)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Profiel"}),(0,a.jsx)(i.BT,{children:"Update je profiel informatie en persoonlijke gegevens."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"name",children:"Naam"}),(0,a.jsx)(o.p,{id:"name",value:b.name,onChange:e=>y(t=>({...t,name:e.target.value})),placeholder:"Je volledige naam"})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"E-mail"}),(0,a.jsx)(o.p,{id:"email",type:"email",value:b.email,onChange:e=>y(t=>({...t,email:e.target.value})),placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"bio",children:"Bio"}),(0,a.jsx)(u.T,{id:"bio",value:b.bio,onChange:e=>y(t=>({...t,bio:e.target.value})),placeholder:"Vertel iets over jezelf...",rows:3})]})]})]})}),(0,a.jsx)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Uiterlijk"}),(0,a.jsx)(i.BT,{children:"Pas het thema en uiterlijk van de applicatie aan."})]}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d.J,{children:"Donker thema"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Schakel tussen licht en donker thema"})]}),(0,a.jsx)(c.d,{checked:"dark"===t,onCheckedChange:e=>s(e?"dark":"light")})]})})]})}),(0,a.jsx)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Notificaties"}),(0,a.jsx)(i.BT,{children:"Configureer hoe en wanneer je notificaties wilt ontvangen."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d.J,{children:"E-mail notificaties"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang updates via e-mail"})]}),(0,a.jsx)(c.d,{checked:b.notifications.email,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,email:e}}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d.J,{children:"Push notificaties"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang push notificaties in je browser"})]}),(0,a.jsx)(c.d,{checked:b.notifications.push,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,push:e}}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(d.J,{children:"Marketing e-mails"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang nieuws en updates over nieuwe functies"})]}),(0,a.jsx)(c.d,{checked:b.notifications.marketing,onCheckedChange:e=>y(t=>({...t,notifications:{...t.notifications,marketing:e}}))})]})]})]})}),(0,a.jsx)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,a.jsxs)(i.Zp,{className:"border-destructive/50",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{className:"text-destructive",children:"Gevaarlijke Zone"}),(0,a.jsx)(i.BT,{children:"Permanente acties die niet ongedaan gemaakt kunnen worden."})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)(r.Fc,{variant:"destructive",children:(0,a.jsx)(r.TN,{children:"Het verwijderen van je account is permanent en kan niet ongedaan worden gemaakt. Alle je projecten en taken zullen verloren gaan."})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(n.$,{variant:"destructive",size:"sm",children:"Account Verwijderen"})})]})]})}),(0,a.jsx)(m.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"flex justify-end",children:(0,a.jsx)(n.$,{onClick:k,disabled:f,className:"glow-accent hover:shadow-lg transition-all duration-300",children:f?"Opslaan...":"Instellingen Opslaan"})})]})]})})})}},27479:(e,t,s)=>{"use strict";s.d(t,{d:()=>w});var a=s(60687),r=s(43210),n=s(70569),i=s(98599),o=s(11273),d=s(65551),l=s(83721),c=s(18853),u=s(14163),p="Switch",[m,h]=(0,o.A)(p),[x,g]=m(p),v=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:l,defaultChecked:c,required:m,disabled:h,value:g="on",onCheckedChange:v,form:f,...j}=e,[k,w]=r.useState(null),N=(0,i.s)(t,e=>w(e)),P=r.useRef(!1),C=!k||f||!!k.closest("form"),[_,R]=(0,d.i)({prop:l,defaultProp:c??!1,onChange:v,caller:p});return(0,a.jsxs)(x,{scope:s,checked:_,disabled:h,children:[(0,a.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":_,"aria-required":m,"data-state":y(_),"data-disabled":h?"":void 0,disabled:h,value:g,...j,ref:N,onClick:(0,n.m)(e.onClick,e=>{R(e=>!e),C&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),C&&(0,a.jsx)(b,{control:k,bubbles:!P.current,name:o,value:g,checked:_,required:m,disabled:h,form:f,style:{transform:"translateX(-100%)"}})]})});v.displayName=p;var f="SwitchThumb",j=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,n=g(f,s);return(0,a.jsx)(u.sG.span,{"data-state":y(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});j.displayName=f;var b=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:n=!0,...o},d)=>{let u=r.useRef(null),p=(0,i.s)(u,d),m=(0,l.Z)(s),h=(0,c.X)(t);return r.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let a=new Event("click",{bubbles:n});t.call(e,s),e.dispatchEvent(a)}},[m,s,n]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...o,tabIndex:-1,ref:p,style:{...o.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var k=s(4780);function w({className:e,...t}){return(0,a.jsx)(v,{"data-slot":"switch",className:(0,k.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(j,{"data-slot":"switch-thumb",className:(0,k.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(60687),r=s(43210),n=s(14163),i=r.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=s(4780);function d({className:e,...t}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55581:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62623)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},62623:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/settings/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66549:(e,t,s)=>{Promise.resolve().then(s.bind(s,62623))},79551:e=>{"use strict";e.exports=require("url")},83721:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(43210);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>d});var a=s(60687);s(43210);var r=s(24224),n=s(4780);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...s})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[243,628,658,814,928,960,828],()=>s(55581));module.exports=a})();