(()=>{var e={};e.id=399,e.ids=[399],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16461:(e,t,s)=>{Promise.resolve().then(s.bind(s,27977))},18193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["dashboard",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,27977)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/profile/page",pathname:"/dashboard/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27479:(e,t,s)=>{"use strict";s.d(t,{d:()=>N});var a=s(60687),r=s(43210),n=s(70569),i=s(98599),o=s(11273),l=s(65551),d=s(83721),c=s(18853),u=s(14163),p="Switch",[m,x]=(0,o.A)(p),[h,f]=m(p),b=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:d,defaultChecked:c,required:m,disabled:x,value:f="on",onCheckedChange:b,form:v,...j}=e,[w,N]=r.useState(null),k=(0,i.s)(t,e=>N(e)),P=r.useRef(!1),C=!w||v||!!w.closest("form"),[_,R]=(0,l.i)({prop:d,defaultProp:c??!1,onChange:b,caller:p});return(0,a.jsxs)(h,{scope:s,checked:_,disabled:x,children:[(0,a.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":_,"aria-required":m,"data-state":y(_),"data-disabled":x?"":void 0,disabled:x,value:f,...j,ref:k,onClick:(0,n.m)(e.onClick,e=>{R(e=>!e),C&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),C&&(0,a.jsx)(g,{control:w,bubbles:!P.current,name:o,value:f,checked:_,required:m,disabled:x,form:v,style:{transform:"translateX(-100%)"}})]})});b.displayName=p;var v="SwitchThumb",j=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,n=f(v,s);return(0,a.jsx)(u.sG.span,{"data-state":y(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});j.displayName=v;var g=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:n=!0,...o},l)=>{let u=r.useRef(null),p=(0,i.s)(u,l),m=(0,d.Z)(s),x=(0,c.X)(t);return r.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let a=new Event("click",{bubbles:n});t.call(e,s),e.dispatchEvent(a)}},[m,s,n]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...o,tabIndex:-1,ref:p,style:{...o.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var w=s(4780);function N({className:e,...t}){return(0,a.jsx)(b,{"data-slot":"switch",className:(0,w.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(j,{"data-slot":"switch-thumb",className:(0,w.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},27977:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/profile/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40429:(e,t,s)=>{Promise.resolve().then(s.bind(s,93017))},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var a=s(60687),r=s(43210),n=s(14163),i=r.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=s(4780);function l({className:e,...t}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},83721:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(43210);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},93017:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var a=s(60687),r=s(26001),n=s(58869),i=s(62688);let o=(0,i.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),l=(0,i.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),d=(0,i.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var c=s(82136),u=s(43210),p=s(52581),m=s(74654),x=s(32584),h=s(29523),f=s(44493),b=s(89667),v=s(54300),j=s(27479),g=s(70569),y=s(11273),w=s(72942),N=s(46059),k=s(14163),P=s(43),C=s(65551),_=s(96963),R="Tabs",[D,E]=(0,y.A)(R,[w.RG]),q=(0,w.RG)(),[A,G]=D(R),M=u.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:n,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:d="automatic",...c}=e,u=(0,P.jH)(l),[p,m]=(0,C.i)({prop:r,onChange:n,defaultProp:i??"",caller:R});return(0,a.jsx)(A,{scope:s,baseId:(0,_.B)(),value:p,onValueChange:m,orientation:o,dir:u,activationMode:d,children:(0,a.jsx)(k.sG.div,{dir:u,"data-orientation":o,...c,ref:t})})});M.displayName=R;var S="TabsList",T=u.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...n}=e,i=G(S,s),o=q(s);return(0,a.jsx)(w.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:r,children:(0,a.jsx)(k.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});T.displayName=S;var I="TabsTrigger",W=u.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...i}=e,o=G(I,s),l=q(s),d=V(o.baseId,r),c=J(o.baseId,r),u=r===o.value;return(0,a.jsx)(w.q7,{asChild:!0,...l,focusable:!n,active:u,children:(0,a.jsx)(k.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...i,ref:t,onMouseDown:(0,g.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,g.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,g.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;u||n||!e||o.onValueChange(r)})})})});W.displayName=I;var z="TabsContent",B=u.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:n,children:i,...o}=e,l=G(z,s),d=V(l.baseId,r),c=J(l.baseId,r),p=r===l.value,m=u.useRef(p);return u.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(N.C,{present:n||p,children:({present:s})=>(0,a.jsx)(k.sG.div,{"data-state":p?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!s,id:c,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:s&&i})})});function V(e,t){return`${e}-trigger-${t}`}function J(e,t){return`${e}-content-${t}`}B.displayName=z;var U=s(4780);function Z({className:e,...t}){return(0,a.jsx)(M,{"data-slot":"tabs",className:(0,U.cn)("flex flex-col gap-2",e),...t})}function F({className:e,...t}){return(0,a.jsx)(T,{"data-slot":"tabs-list",className:(0,U.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function K({className:e,...t}){return(0,a.jsx)(W,{"data-slot":"tabs-trigger",className:(0,U.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function L({className:e,...t}){return(0,a.jsx)(B,{"data-slot":"tabs-content",className:(0,U.cn)("flex-1 outline-none",e),...t})}function O(){let{data:e,update:t}=(0,c.useSession)(),[s,i]=(0,u.useState)(!1);if(!e?.user)return null;let g=e.user.name?.split(" ").map(e=>e[0]).join("").toUpperCase(),y=async e=>{e.preventDefault(),i(!0);try{await new Promise(e=>setTimeout(e,1e3)),p.oR.success("Profiel succesvol bijgewerkt")}catch(e){p.oR.error("Er is iets misgegaan bij het bijwerken van je profiel")}finally{i(!1)}};return(0,a.jsx)(m.g1,{children:(0,a.jsx)(m._A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Profiel"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Beheer je account instellingen en voorkeuren"})]}),(0,a.jsxs)(Z,{defaultValue:"account",className:"space-y-4",children:[(0,a.jsxs)(F,{children:[(0,a.jsx)(K,{value:"account",children:"Account"}),(0,a.jsx)(K,{value:"notifications",children:"Notificaties"}),(0,a.jsx)(K,{value:"appearance",children:"Weergave"})]}),(0,a.jsx)(L,{value:"account",className:"space-y-4",children:(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:"Profiel Informatie"}),(0,a.jsx)(f.BT,{children:"Update je persoonlijke informatie"})]}),(0,a.jsxs)("form",{onSubmit:y,children:[(0,a.jsxs)(f.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(x.eu,{className:"h-20 w-20",children:[(0,a.jsx)(x.BK,{src:e.user.image||"",alt:e.user.name||""}),(0,a.jsx)(x.q5,{className:"text-lg",children:g})]}),(0,a.jsx)(h.$,{variant:"outline",size:"sm",children:"Verander foto"})]}),(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(v.J,{htmlFor:"name",children:"Naam"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}),(0,a.jsx)(b.p,{id:"name",defaultValue:e.user.name||"",className:"rounded-l-none"})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(v.J,{htmlFor:"email",children:"Email"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,a.jsx)(o,{className:"h-4 w-4"})}),(0,a.jsx)(b.p,{id:"email",type:"email",defaultValue:e.user.email||"",className:"rounded-l-none"})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(v.J,{htmlFor:"password",children:"Wachtwoord"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 text-sm text-muted-foreground border border-r-0 rounded-l-md bg-muted",children:(0,a.jsx)(l,{className:"h-4 w-4"})}),(0,a.jsx)(b.p,{id:"password",type:"password",placeholder:"••••••••",className:"rounded-l-none"})]})]})]})]}),(0,a.jsx)(f.wL,{children:(0,a.jsx)(r.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,a.jsxs)(h.$,{type:"submit",disabled:s,children:[(0,a.jsx)(d,{className:"mr-2 h-4 w-4"}),s?"Bezig met opslaan...":"Opslaan"]})})})]})]})}),(0,a.jsx)(L,{value:"notifications",className:"space-y-4",children:(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:"Notificatie Instellingen"}),(0,a.jsx)(f.BT,{children:"Configureer hoe je notificaties wilt ontvangen"})]}),(0,a.jsxs)(f.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(v.J,{children:"Email Notificaties"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang updates over je projecten"})]}),(0,a.jsx)(j.d,{})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(v.J,{children:"Push Notificaties"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Ontvang meldingen in je browser"})]}),(0,a.jsx)(j.d,{})]})]})]})}),(0,a.jsx)(L,{value:"appearance",className:"space-y-4",children:(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:"Weergave Instellingen"}),(0,a.jsx)(f.BT,{children:"Pas de weergave van de applicatie aan"})]}),(0,a.jsxs)(f.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(v.J,{children:"Donker Modus"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Schakel tussen licht en donker thema"})]}),(0,a.jsx)(j.d,{})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(v.J,{children:"Taal"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Kies je voorkeurstaal"})]}),(0,a.jsxs)("select",{className:"h-9 rounded-md border border-input bg-background px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"nl",children:"Nederlands"}),(0,a.jsx)("option",{value:"en",children:"English"})]})]})]})]})})]})]})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[243,628,658,814,928,960,828],()=>s(18193));module.exports=a})();