(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:s}=t(39844);e.exports=s("/Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/next/dist/client/app-dir/link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11927:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),n=t(4536),o=t.n(n);function i(){return(0,s.jsx)("main",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,s.jsxs)("div",{className:"z-10 max-w-5xl w-full items-center justify-between font-mono text-sm",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-8 text-center",children:"Welkom bij het CRM Dashboard"}),(0,s.jsx)("p",{className:"text-xl mb-8 text-center",children:"Een modern systeem voor project- en taakbeheer"}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,s.jsx)(o(),{href:"/auth/login",className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors",children:"Inloggen"}),(0,s.jsx)(o(),{href:"/auth/register",className:"bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors",children:"Registreren"})]})]})})}},21741:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>o});var s=t(60687),n=t(82136);function o({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}t(10218)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>n});var s=t(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","Providers");(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","ThemeProvider")},33873:e=>{"use strict";e.exports=require("path")},33893:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38964:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},51679:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},53718:(e,r,t)=>{Promise.resolve().then(t.bind(t,29519))},61135:()=>{},62932:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77278:(e,r,t)=>{Promise.resolve().then(t.bind(t,21741))},79551:e=>{"use strict";e.exports=require("url")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413),n=t(80544),o=t.n(n);t(61135);var i=t(29519);function a({children:e}){return(0,s.jsx)("html",{lang:"en",className:o().variable,children:(0,s.jsx)("body",{className:o().className,children:(0,s.jsx)(i.Providers,{children:e})})})}}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,628,658,814],()=>t(33893));module.exports=s})();