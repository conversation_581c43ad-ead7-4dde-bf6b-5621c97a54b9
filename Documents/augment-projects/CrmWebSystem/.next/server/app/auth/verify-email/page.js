(()=>{var e={};e.id=270,e.ids=[270],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(49384),n=t(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21741:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>a});var s=t(60687),n=t(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}t(10218)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>n});var s=t(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","Providers");(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","ThemeProvider")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687);t(43210);var n=t(8730),a=t(24224),i=t(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:t,className:e})),...d})}},29852:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},38964:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},42491:(e,r,t)=>{Promise.resolve().then(t.bind(t,74994))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i,wL:()=>c});var s=t(60687);t(43210);var n=t(4780);function a({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},53718:(e,r,t)=>{Promise.resolve().then(t.bind(t,29519))},61135:()=>{},62932:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74994:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),n=t(29523),a=t(44493),i=t(16189),o=t(43210);function d(){let e=(0,i.useRouter)();(0,i.useSearchParams)().get("token");let[r,t]=(0,o.useState)(null),[d,l]=(0,o.useState)(!0);return d?(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsx)(a.Zp,{className:"w-full max-w-md",children:(0,s.jsxs)(a.aR,{className:"space-y-1",children:[(0,s.jsx)(a.ZB,{className:"text-2xl text-center",children:"Email verifi\xebren"}),(0,s.jsx)(a.BT,{className:"text-center",children:"Bezig met het verifi\xebren van je email adres..."})]})})}):r?(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)(a.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(a.aR,{className:"space-y-1",children:[(0,s.jsx)(a.ZB,{className:"text-2xl text-center",children:"Verificatie mislukt"}),(0,s.jsx)(a.BT,{className:"text-center",children:r})]}),(0,s.jsx)(a.wL,{className:"flex flex-col space-y-4",children:(0,s.jsx)(n.$,{className:"w-full",onClick:()=>e.push("/auth/login"),children:"Terug naar inloggen"})})]})}):(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsx)(a.Zp,{className:"w-full max-w-md",children:(0,s.jsxs)(a.aR,{className:"space-y-1",children:[(0,s.jsx)(a.ZB,{className:"text-2xl text-center",children:"Email geverifieerd"}),(0,s.jsx)(a.BT,{className:"text-center",children:"Je email adres is succesvol geverifieerd. Je wordt doorgestuurd naar de login pagina..."})]})})})}},77278:(e,r,t)=>{Promise.resolve().then(t.bind(t,21741))},78024:(e,r,t)=>{Promise.resolve().then(t.bind(t,29852))},79551:e=>{"use strict";e.exports=require("url")},88057:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29852)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/verify-email/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(80544),a=t.n(n);t(61135);var i=t(29519);function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:a().variable,children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(i.Providers,{children:e})})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,628,658,928],()=>t(88057));module.exports=s})();