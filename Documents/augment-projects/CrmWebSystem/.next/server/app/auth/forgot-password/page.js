(()=>{var e={};e.id=413,e.ids=[413],e.modules={2039:(e,r,t)=>{Promise.resolve().then(t.bind(t,81165))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(49384),n=t(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21741:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>a});var s=t(60687),n=t(82136);function a({children:e}){return(0,s.jsx)(n.<PERSON>,{children:e})}t(10218)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>n});var s=t(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","Providers");(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","ThemeProvider")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687);t(43210);var n=t(8730),a=t(24224),i=t(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:t,className:e})),...d})}},30063:(e,r,t)=>{Promise.resolve().then(t.bind(t,31843))},31843:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),n=t(91821),a=t(29523),i=t(44493),o=t(89667),d=t(85814),l=t.n(d),c=t(43210);function u(){let[e,r]=(0,c.useState)(null),[t,d]=(0,c.useState)(null),[u,p]=(0,c.useState)(!1);async function m(e){e.preventDefault(),r(null),d(null),p(!0);let t=new FormData(e.currentTarget).get("email");try{let e=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t})}),r=await e.json();if(!e.ok)throw Error(r.message||"Er is iets misgegaan bij het aanvragen van de reset link");d("Als er een account bestaat met dit email adres, ontvang je een reset link")}catch(e){r(e.message)}finally{p(!1)}}return(0,s.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.aR,{className:"space-y-1",children:[(0,s.jsx)(i.ZB,{className:"text-2xl text-center",children:"Wachtwoord vergeten"}),(0,s.jsx)(i.BT,{className:"text-center",children:"Voer je email adres in om een reset link te ontvangen"})]}),(0,s.jsxs)("form",{onSubmit:m,children:[(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[e&&(0,s.jsx)(n.Fc,{variant:"destructive",children:(0,s.jsx)(n.TN,{children:e})}),t&&(0,s.jsx)(n.Fc,{children:(0,s.jsx)(n.TN,{children:t})}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(o.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",required:!0})})]}),(0,s.jsxs)(i.wL,{className:"flex flex-col space-y-4",children:[(0,s.jsx)(a.$,{type:"submit",className:"w-full",disabled:u,children:u?"Bezig met versturen...":"Reset link aanvragen"}),(0,s.jsxs)("p",{className:"text-sm text-center text-muted-foreground",children:["Terug naar"," ",(0,s.jsx)(l(),{href:"/auth/login",className:"text-primary hover:underline",children:"inloggen"})]})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},38964:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i,wL:()=>c});var s=t(60687);t(43210);var n=t(4780);function a({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},53718:(e,r,t)=>{Promise.resolve().then(t.bind(t,29519))},61135:()=>{},62932:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77278:(e,r,t)=>{Promise.resolve().then(t.bind(t,21741))},79551:e=>{"use strict";e.exports=require("url")},81165:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx","default")},84577:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81165)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/auth/forgot-password/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(60687);t(43210);var n=t(4780);function a({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(60687);t(43210);var n=t(24224),a=t(4780);let i=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(i({variant:r}),e),...t})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(80544),a=t.n(n);t(61135);var i=t(29519);function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:a().variable,children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(i.Providers,{children:e})})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,628,658,814,928],()=>t(84577));module.exports=s})();