{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/layout.tsx"], "sourcesContent": ["import { myLocalFont } from '@/lib/fonts'\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" className={myLocalFont.variable}>\n      <body>{children}</body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,oIAAA,CAAA,cAAW,CAAC,QAAQ;kBAC7C,cAAA,8OAAC;sBAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}