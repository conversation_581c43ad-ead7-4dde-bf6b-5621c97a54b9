{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "utils.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/utils.ts"], "sourcesContent": ["import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n"], "names": ["isServer", "window", "noop", "undefined", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "constructor", "prot", "prototype", "hasOwnProperty", "toString", "call", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "getAbortController", "AbortController", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing"], "mappings": "AAYA,QAAA;AAwDA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAMA,QAAQ,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,MAAA,IAAUA,OAA5D;AAEA,SAASC,IAAT,GAA2B;IAChC,OAAOC,SAAP,CAAA;AACD,CAAA;AAEM,SAASC,gBAAT,CACLC,OADK,EAELC,KAFK,EAGI;IACT,OAAO,OAAOD,OAAP,KAAmB,UAAnB,GACFA,OAAD,CAAiDC,KAAjD,CADG,GAEHD,OAFJ,CAAA;AAGD,CAAA;AAEM,SAASE,cAAT,CAAwBC,KAAxB,EAAyD;IAC9D,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,CAAtC,IAA2CA,KAAK,KAAKC,QAA5D,CAAA;AACD,CAAA;AAEM,SAASC,UAAT,CAAuBC,MAAvB,EAAoCC,MAApC,EAAsD;IAC3D,OAAOD,MAAM,CAACE,MAAP,EAAeC,CAAD,GAAO,CAACF,MAAM,CAACG,QAAP,CAAgBD,CAAhB,CAAtB,CAAP,CAAA;AACD,CAAA;AAEM,SAASE,SAAT,CAAsBC,KAAtB,EAAkCC,KAAlC,EAAiDV,KAAjD,EAAgE;IACrE,MAAMW,IAAI,GAAGF,KAAK,CAACG,KAAN,CAAY,CAAZ,CAAb,CAAA;IACAD,IAAI,CAACD,KAAD,CAAJ,GAAcV,KAAd,CAAA;IACA,OAAOW,IAAP,CAAA;AACD,CAAA;AAEM,SAASE,cAAT,CAAwBC,SAAxB,EAA2CC,SAA3C,EAAuE;IAC5E,OAAOC,IAAI,CAACC,GAAL,CAASH,SAAS,GAAA,CAAIC,SAAS,IAAI,CAAjB,CAAT,GAA+BG,IAAI,CAACC,GAAL,EAAxC,EAAoD,CAApD,CAAP,CAAA;AACD,CAAA;AAEM,SAASC,cAAT,CAILC,IAJK,EAKLC,IALK,EAMLC,IANK,EAOK;IACV,IAAI,CAACC,UAAU,CAACH,IAAD,CAAf,EAAuB;QACrB,OAAOA,IAAP,CAAA;IACD,CAAA;IAED,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;QAC9B,OAAO;YAAE,GAAGC,IAAL;YAAWE,QAAQ,EAAEJ,IAArB;YAA2BK,OAAO,EAAEJ,IAAAA;SAA3C,CAAA;IACD,CAAA;IAED,OAAO;QAAE,GAAGA,IAAL;QAAWG,QAAQ,EAAEJ,IAAAA;KAA5B,CAAA;AACD,CAAA;AAEM,SAASM,iBAAT,CAGLN,IAHK,EAILC,IAJK,EAKLC,IALK,EAMK;IACV,IAAIC,UAAU,CAACH,IAAD,CAAd,EAAsB;QACpB,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;YAC9B,OAAO;gBAAE,GAAGC,IAAL;gBAAWK,WAAW,EAAEP,IAAxB;gBAA8BQ,UAAU,EAAEP,IAAAA;aAAjD,CAAA;QACD,CAAA;QACD,OAAO;YAAE,GAAGA,IAAL;YAAWM,WAAW,EAAEP,IAAAA;SAA/B,CAAA;IACD,CAAA;IAED,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;QAC9B,OAAO;YAAE,GAAGC,IAAL;YAAWO,UAAU,EAAER,IAAAA;SAA9B,CAAA;IACD,CAAA;IAED,OAAO;QAAE,GAAGA,IAAAA;KAAZ,CAAA;AACD,CAAA;AAEM,SAASS,eAAT,CAILT,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;IAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GAAmB;QAAC;YAAE,GAAGC,IAAL;YAAWG,QAAQ,EAAEJ,IAAAA;SAAtB;QAA8BE,IAA9B;KAAnB,GAAyD;QAACF,IAAI,IAAI,CAAA,CAAT;QAAaC,IAAb;KAD3D,CAAA;AAGD,CAAA;AAEM,SAASS,uBAAT,CAILV,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;IAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GACI;QAAC;YAAE,GAAGC,IAAL;YAAWM,WAAW,EAAEP,IAAAA;SAAzB;QAAiCE,IAAjC;KADJ,GAEI;QAACF,IAAI,IAAI,CAAA,CAAT;QAAaC,IAAb;KAHN,CAAA;AAKD,CAAA;AAEM,SAASU,UAAT,CACLC,OADK,EAELC,KAFK,EAGI;IACT,MAAM,EACJC,IAAI,GAAG,KADH,EAEJC,KAFI,EAGJC,WAHI,EAIJC,SAJI,EAKJb,QALI,EAMJc,KAAAA,EANI,GAOFN,OAPJ,CAAA;IASA,IAAIT,UAAU,CAACC,QAAD,CAAd,EAA0B;QACxB,IAAIW,KAAJ,EAAW;YACT,IAAIF,KAAK,CAACM,SAAN,KAAoBC,qBAAqB,CAAChB,QAAD,EAAWS,KAAK,CAACQ,OAAjB,CAA7C,EAAwE;gBACtE,OAAO,KAAP,CAAA;YACD,CAAA;SAHH,MAIO,IAAI,CAACC,eAAe,CAACT,KAAK,CAACT,QAAP,EAAiBA,QAAjB,CAApB,EAAgD;YACrD,OAAO,KAAP,CAAA;QACD,CAAA;IACF,CAAA;IAED,IAAIU,IAAI,KAAK,KAAb,EAAoB;QAClB,MAAMS,QAAQ,GAAGV,KAAK,CAACU,QAAN,EAAjB,CAAA;QACA,IAAIT,IAAI,KAAK,QAAT,IAAqB,CAACS,QAA1B,EAAoC;YAClC,OAAO,KAAP,CAAA;QACD,CAAA;QACD,IAAIT,IAAI,KAAK,UAAT,IAAuBS,QAA3B,EAAqC;YACnC,OAAO,KAAP,CAAA;QACD,CAAA;IACF,CAAA;IAED,IAAI,OAAOL,KAAP,KAAiB,SAAjB,IAA8BL,KAAK,CAACW,OAAN,EAAoBN,KAAAA,KAAtD,EAA6D;QAC3D,OAAO,KAAP,CAAA;IACD,CAAA;IAED,IACE,OAAOF,WAAP,KAAuB,WAAvB,IACAA,WAAW,KAAKH,KAAK,CAACY,KAAN,CAAYT,WAF9B,EAGE;QACA,OAAO,KAAP,CAAA;IACD,CAAA;IAED,IAAIC,SAAS,IAAI,CAACA,SAAS,CAACJ,KAAD,CAA3B,EAAoC;QAClC,OAAO,KAAP,CAAA;IACD,CAAA;IAED,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASa,aAAT,CACLd,OADK,EAELe,QAFK,EAGI;IACT,MAAM,EAAEZ,KAAF,EAASa,QAAT,EAAmBX,SAAnB,EAA8BV,WAAAA,EAA9B,GAA8CK,OAApD,CAAA;IACA,IAAIT,UAAU,CAACI,WAAD,CAAd,EAA6B;QAC3B,IAAI,CAACoB,QAAQ,CAACN,OAAT,CAAiBd,WAAtB,EAAmC;YACjC,OAAO,KAAP,CAAA;QACD,CAAA;QACD,IAAIQ,KAAJ,EAAW;YACT,IACEc,YAAY,CAACF,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,CAAZ,KAA+CsB,YAAY,CAACtB,WAAD,CAD7D,EAEE;gBACA,OAAO,KAAP,CAAA;YACD,CAAA;QACF,CAND,MAMO,IAAI,CAACe,eAAe,CAACK,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,EAA+BA,WAA/B,CAApB,EAAiE;YACtE,OAAO,KAAP,CAAA;QACD,CAAA;IACF,CAAA;IAED,IACE,OAAOqB,QAAP,KAAoB,SAApB,IACCD,QAAQ,CAACF,KAAT,CAAeK,MAAf,KAA0B,SAA3B,KAA0CF,QAF5C,EAGE;QACA,OAAO,KAAP,CAAA;IACD,CAAA;IAED,IAAIX,SAAS,IAAI,CAACA,SAAS,CAACU,QAAD,CAA3B,EAAuC;QACrC,OAAO,KAAP,CAAA;IACD,CAAA;IAED,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASP,qBAAT,CACLhB,QADK,EAELiB,OAFK,EAGG;IACR,MAAMU,MAAM,GAAG,CAAAV,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO,CAAEW,cAAT,KAA2BH,YAA1C,CAAA;IACA,OAAOE,MAAM,CAAC3B,QAAD,CAAb,CAAA;AACD,CAAA;AAED;;;CAGA,GACO,SAASyB,YAAT,CAAsBzB,QAAtB,EAAkD;IACvD,OAAO6B,IAAI,CAACC,SAAL,CAAe9B,QAAf,EAAyB,CAAC+B,CAAD,EAAIC,GAAJ,GAC9BC,aAAa,CAACD,GAAD,CAAb,GACIE,MAAM,CAACC,IAAP,CAAYH,GAAZ,CACGI,CAAAA,IADH,EAEGC,CAAAA,MAFH,CAEU,CAACC,MAAD,EAASC,GAAT,KAAiB;YACvBD,MAAM,CAACC,GAAD,CAAN,GAAcP,GAAG,CAACO,GAAD,CAAjB,CAAA;YACA,OAAOD,MAAP,CAAA;QACD,CALH,EAKK,CAAA,CALL,CADJ,GAOIN,GARC,CAAP,CAAA;AAUD,CAAA;AAED;;CAEA,GACO,SAASd,eAAT,CAAyBsB,CAAzB,EAAsCC,CAAtC,EAA4D;IACjE,OAAOC,gBAAgB,CAACF,CAAD,EAAIC,CAAJ,CAAvB,CAAA;AACD,CAAA;AAED;;CAEA,GACO,SAASC,gBAAT,CAA0BF,CAA1B,EAAkCC,CAAlC,EAAmD;IACxD,IAAID,CAAC,KAAKC,CAAV,EAAa;QACX,OAAO,IAAP,CAAA;IACD,CAAA;IAED,IAAI,OAAOD,CAAP,KAAa,OAAOC,CAAxB,EAA2B;QACzB,OAAO,KAAP,CAAA;IACD,CAAA;IAED,IAAID,CAAC,IAAIC,CAAL,IAAU,OAAOD,CAAP,KAAa,QAAvB,IAAmC,OAAOC,CAAP,KAAa,QAApD,EAA8D;QAC5D,OAAO,CAACP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAAeE,CAAAA,IAAf,EAAqBJ,GAAD,GAAS,CAACG,gBAAgB,CAACF,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA9C,CAAR,CAAA;IACD,CAAA;IAED,OAAO,KAAP,CAAA;AACD,CAAA;AAED;;;;CAIA,GAEO,SAASK,gBAAT,CAA0BJ,CAA1B,EAAkCC,CAAlC,EAA+C;IACpD,IAAID,CAAC,KAAKC,CAAV,EAAa;QACX,OAAOD,CAAP,CAAA;IACD,CAAA;IAED,MAAMxD,KAAK,GAAG6D,YAAY,CAACL,CAAD,CAAZ,IAAmBK,YAAY,CAACJ,CAAD,CAA7C,CAAA;IAEA,IAAIzD,KAAK,IAAKiD,aAAa,CAACO,CAAD,CAAb,IAAoBP,aAAa,CAACQ,CAAD,CAA/C,EAAqD;QACnD,MAAMK,KAAK,GAAG9D,KAAK,GAAGwD,CAAC,CAACO,MAAL,GAAcb,MAAM,CAACC,IAAP,CAAYK,CAAZ,EAAeO,MAAhD,CAAA;QACA,MAAMC,MAAM,GAAGhE,KAAK,GAAGyD,CAAH,GAAOP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAA3B,CAAA;QACA,MAAMQ,KAAK,GAAGD,MAAM,CAACD,MAArB,CAAA;QACA,MAAM7D,IAAS,GAAGF,KAAK,GAAG,EAAH,GAAQ,CAAA,CAA/B,CAAA;QAEA,IAAIkE,UAAU,GAAG,CAAjB,CAAA;QAEA,IAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAApB,EAA2BE,CAAC,EAA5B,CAAgC;YAC9B,MAAMZ,GAAG,GAAGvD,KAAK,GAAGmE,CAAH,GAAOH,MAAM,CAACG,CAAD,CAA9B,CAAA;YACAjE,IAAI,CAACqD,GAAD,CAAJ,GAAYK,gBAAgB,CAACJ,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA5B,CAAA;YACA,IAAIrD,IAAI,CAACqD,GAAD,CAAJ,KAAcC,CAAC,CAACD,GAAD,CAAnB,EAA0B;gBACxBW,UAAU,EAAA,CAAA;YACX,CAAA;QACF,CAAA;QAED,OAAOJ,KAAK,KAAKG,KAAV,IAAmBC,UAAU,KAAKJ,KAAlC,GAA0CN,CAA1C,GAA8CtD,IAArD,CAAA;IACD,CAAA;IAED,OAAOuD,CAAP,CAAA;AACD,CAAA;AAED;;CAEA,GACO,SAASW,mBAAT,CAAgCZ,CAAhC,EAAsCC,CAAtC,EAAqD;IAC1D,IAAKD,CAAC,IAAI,CAACC,CAAP,IAAcA,CAAC,IAAI,CAACD,CAAxB,EAA4B;QAC1B,OAAO,KAAP,CAAA;IACD,CAAA;IAED,IAAK,MAAMD,GAAX,IAAkBC,CAAlB,CAAqB;QACnB,IAAIA,CAAC,CAACD,GAAD,CAAD,KAAWE,CAAC,CAACF,GAAD,CAAhB,EAAuB;YACrB,OAAO,KAAP,CAAA;QACD,CAAA;IACF,CAAA;IAED,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASM,YAAT,CAAsBtE,KAAtB,EAAsC;IAC3C,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,KAAwBA,KAAK,CAACwE,MAAN,KAAiBb,MAAM,CAACC,IAAP,CAAY5D,KAAZ,EAAmBwE,MAAnE,CAAA;AACD,EAAA,gEAAA;AAGM,SAASd,aAAT,CAAuBsB,CAAvB,EAA4C;IACjD,IAAI,CAACC,kBAAkB,CAACD,CAAD,CAAvB,EAA4B;QAC1B,OAAO,KAAP,CAAA;IACD,CAHgD,CAAA,8BAAA;IAMjD,MAAME,IAAI,GAAGF,CAAC,CAACG,WAAf,CAAA;IACA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;QAC/B,OAAO,IAAP,CAAA;IACD,CATgD,CAAA,4BAAA;IAYjD,MAAME,IAAI,GAAGF,IAAI,CAACG,SAAlB,CAAA;IACA,IAAI,CAACJ,kBAAkB,CAACG,IAAD,CAAvB,EAA+B;QAC7B,OAAO,KAAP,CAAA;IACD,CAfgD,CAAA,yDAAA;IAkBjD,IAAI,CAACA,IAAI,CAACE,cAAL,CAAoB,eAApB,CAAL,EAA2C;QACzC,OAAO,KAAP,CAAA;IACD,CApBgD,CAAA,6BAAA;IAuBjD,OAAO,IAAP,CAAA;AACD,CAAA;AAED,SAASL,kBAAT,CAA4BD,CAA5B,EAA6C;IAC3C,OAAOrB,MAAM,CAAC0B,SAAP,CAAiBE,QAAjB,CAA0BC,IAA1B,CAA+BR,CAA/B,CAAA,KAAsC,iBAA7C,CAAA;AACD,CAAA;AAEM,SAASxD,UAAT,CAAoBxB,KAApB,EAAuD;IAC5D,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,CAAP,CAAA;AACD,CAAA;AAEM,SAASyF,OAAT,CAAiBzF,KAAjB,EAA6C;IAClD,OAAOA,KAAK,YAAY0F,KAAxB,CAAA;AACD,CAAA;AAEM,SAASC,KAAT,CAAeC,OAAf,EAA+C;IACpD,OAAO,IAAIC,OAAJ,EAAaC,OAAD,IAAa;QAC9BC,UAAU,CAACD,OAAD,EAAUF,OAAV,CAAV,CAAA;IACD,CAFM,CAAP,CAAA;AAGD,CAAA;AAED;;;CAGA,GACO,SAASI,iBAAT,CAA2BC,QAA3B,EAAiD;IACtDN,KAAK,CAAC,CAAD,CAAL,CAASO,IAAT,CAAcD,QAAd,CAAA,CAAA;AACD,CAAA;AAEM,SAASE,kBAAT,GAA2D;IAChE,IAAI,OAAOC,eAAP,KAA2B,UAA/B,EAA2C;QACzC,OAAO,IAAIA,eAAJ,EAAP,CAAA;IACD,CAAA;IACD,OAAA;AACD,CAAA;AAEM,SAASC,WAAT,CAGLC,QAHK,EAGwBC,IAHxB,EAGqC7D,OAHrC,EAG+D;IACpE,yEAAA;IACA,IAAIA,OAAO,CAAC8D,WAAZ,IAAA,IAAA,IAAI9D,OAAO,CAAC8D,WAAR,CAAsBF,QAAtB,EAAgCC,IAAhC,CAAJ,EAA2C;QACzC,OAAOD,QAAP,CAAA;KADF,MAEO,IAAI,OAAO5D,OAAO,CAAC+D,iBAAf,KAAqC,UAAzC,EAAqD;QAC1D,OAAO/D,OAAO,CAAC+D,iBAAR,CAA0BH,QAA1B,EAAoCC,IAApC,CAAP,CAAA;IACD,CAFM,MAEA,IAAI7D,OAAO,CAAC+D,iBAAR,KAA8B,KAAlC,EAAyC;QAC9C,8DAAA;QACA,OAAOpC,gBAAgB,CAACiC,QAAD,EAAWC,IAAX,CAAvB,CAAA;IACD,CAAA;IACD,OAAOA,IAAP,CAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "file": "notifyManager.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/notifyManager.ts"], "sourcesContent": ["import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "names": ["createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "batch", "result", "flush", "schedule", "push", "scheduleMicrotask", "batchCalls", "args", "originalQueue", "length", "for<PERSON>ach", "setNotifyFunction", "fn", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger"], "mappings": ";;;;;;AAYO,SAASA,mBAAT,GAA+B;IACpC,IAAIC,KAAuB,GAAG,EAA9B,CAAA;IACA,IAAIC,YAAY,GAAG,CAAnB,CAAA;IACA,IAAIC,QAAwB,IAAIC,QAAD,IAAc;QAC3CA,QAAQ,EAAA,CAAA;KADV,CAAA;IAGA,IAAIC,aAAkC,IAAID,QAAD,IAA0B;QACjEA,QAAQ,EAAA,CAAA;KADV,CAAA;IAIA,MAAME,KAAK,GAAOF,QAAJ,IAA6B;QACzC,IAAIG,MAAJ,CAAA;QACAL,YAAY,EAAA,CAAA;QACZ,IAAI;YACFK,MAAM,GAAGH,QAAQ,EAAjB,CAAA;QACD,CAFD,QAEU;YACRF,YAAY,EAAA,CAAA;YACZ,IAAI,CAACA,YAAL,EAAmB;gBACjBM,KAAK,EAAA,CAAA;YACN,CAAA;QACF,CAAA;QACD,OAAOD,MAAP,CAAA;KAXF,CAAA;IAcA,MAAME,QAAQ,IAAIL,QAAD,IAAoC;QACnD,IAAIF,YAAJ,EAAkB;YAChBD,KAAK,CAACS,IAAN,CAAWN,QAAX,CAAA,CAAA;QACD,CAFD,MAEO;aACLO,oOAAAA,AAAiB,EAAC,MAAM;gBACtBR,QAAQ,CAACC,QAAD,CAAR,CAAA;YACD,CAFgB,CAAjB,CAAA;QAGD,CAAA;KAPH,CAAA;IAUA;;GAEF,GACE,MAAMQ,UAAU,IACdR,QADiB,IAES;QAC1B,OAAO,CAAC,GAAGS,IAAJ,KAAa;YAClBJ,QAAQ,CAAC,MAAM;gBACbL,QAAQ,CAAC,GAAGS,IAAJ,CAAR,CAAA;YACD,CAFO,CAAR,CAAA;SADF,CAAA;KAHF,CAAA;IAUA,MAAML,KAAK,GAAG,MAAY;QACxB,MAAMM,aAAa,GAAGb,KAAtB,CAAA;QACAA,KAAK,GAAG,EAAR,CAAA;QACA,IAAIa,aAAa,CAACC,MAAlB,EAA0B;aACxBJ,oOAAAA,AAAiB,EAAC,MAAM;gBACtBN,aAAa,CAAC,MAAM;oBAClBS,aAAa,CAACE,OAAd,EAAuBZ,QAAD,IAAc;wBAClCD,QAAQ,CAACC,QAAD,CAAR,CAAA;qBADF,CAAA,CAAA;gBAGD,CAJY,CAAb,CAAA;YAKD,CANgB,CAAjB,CAAA;QAOD,CAAA;KAXH,CAAA;IAcA;;;GAGF,GACE,MAAMa,iBAAiB,IAAIC,EAAD,IAAwB;QAChDf,QAAQ,GAAGe,EAAX,CAAA;KADF,CAAA;IAIA;;;GAGF,GACE,MAAMC,sBAAsB,IAAID,EAAD,IAA6B;QAC1Db,aAAa,GAAGa,EAAhB,CAAA;KADF,CAAA;IAIA,OAAO;QACLZ,KADK;QAELM,UAFK;QAGLH,QAHK;QAILQ,iBAJK;QAKLE,sBAAAA;KALF,CAAA;AAOD,EAAA,YAAA;AAGYC,MAAAA,aAAa,GAAGpB,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "file": "subscribable.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/subscribable.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size"], "mappings": ";;;AAEO,MAAMA,YAAN,CAA0D;IAG/DC,WAAW,EAAG;QACZ,IAAA,CAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB,CAAA;QACA,IAAKC,CAAAA,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAAjB,CAAA;IACD,CAAA;IAEDD,SAAS,CAACE,QAAD,EAAkC;QACzC,MAAMC,QAAQ,GAAG;YAAED,QAAAA;SAAnB,CAAA;QACA,IAAA,CAAKJ,SAAL,CAAeM,GAAf,CAAmBD,QAAnB,CAAA,CAAA;QAEA,IAAA,CAAKE,WAAL,EAAA,CAAA;QAEA,OAAO,MAAM;YACX,IAAA,CAAKP,SAAL,CAAeQ,MAAf,CAAsBH,QAAtB,CAAA,CAAA;YACA,IAAA,CAAKI,aAAL,EAAA,CAAA;SAFF,CAAA;IAID,CAAA;IAEDC,YAAY,GAAY;QACtB,OAAO,IAAKV,CAAAA,SAAL,CAAeW,IAAf,GAAsB,CAA7B,CAAA;IACD,CAAA;IAESJ,WAAW,GAAS,CAE7B,CAAA;IAESE,aAAa,GAAS,CAE/B,CAAA;AA9B8D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "file": "focusManager.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "names": ["FocusManager", "Subscribable", "constructor", "setup", "onFocus", "isServer", "window", "addEventListener", "listener", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "focused", "setFocused", "changed", "listeners", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager"], "mappings": ";;;;;;;;AAOO,MAAMA,YAAN,6NAA2BC,eAA3B,CAAwC;IAM7CC,WAAW,EAAG;QACZ,KAAA,EAAA,CAAA;QACA,IAAKC,CAAAA,KAAL,IAAcC,OAAD,IAAa;YACxB,mEAAA;YACA,uEAAA;YACA,IAAI,8MAACC,WAAD,IAAaC,MAAM,CAACC,gBAAxB,EAA0C;gBACxC,MAAMC,QAAQ,GAAG,IAAMJ,OAAO,EAA9B,CADwC,CAAA,wCAAA;gBAGxCE,MAAM,CAACC,gBAAP,CAAwB,kBAAxB,EAA4CC,QAA5C,EAAsD,KAAtD,CAAA,CAAA;gBACAF,MAAM,CAACC,gBAAP,CAAwB,OAAxB,EAAiCC,QAAjC,EAA2C,KAA3C,CAAA,CAAA;gBAEA,OAAO,MAAM;oBACX,iDAAA;oBACAF,MAAM,CAACG,mBAAP,CAA2B,kBAA3B,EAA+CD,QAA/C,CAAA,CAAA;oBACAF,MAAM,CAACG,mBAAP,CAA2B,OAA3B,EAAoCD,QAApC,CAAA,CAAA;iBAHF,CAAA;YAKD,CAAA;YACD,OAAA;SAfF,CAAA;IAiBD,CAAA;IAESE,WAAW,GAAS;QAC5B,IAAI,CAAC,IAAKC,CAAAA,OAAV,EAAmB;YACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKT,KAA3B,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAESU,aAAa,GAAG;QACxB,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;YAAA,IAAA,aAAA,CAAA;YACxB,CAAA,aAAA,GAAA,IAAA,CAAKH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YACA,IAAKA,CAAAA,OAAL,GAAeI,SAAf,CAAA;QACD,CAAA;IACF,CAAA;IAEDH,gBAAgB,CAACT,KAAD,EAAuB;QAAA,IAAA,cAAA,CAAA;QACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;QACA,CAAA,cAAA,GAAA,IAAA,CAAKQ,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,CAAKA,OAAL,GAAeR,KAAK,EAAEa,OAAD,IAAa;YAChC,IAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;gBAChC,IAAKC,CAAAA,UAAL,CAAgBD,OAAhB,CAAA,CAAA;YACD,CAFD,MAEO;gBACL,IAAA,CAAKZ,OAAL,EAAA,CAAA;YACD,CAAA;QACF,CANmB,CAApB,CAAA;IAOD,CAAA;IAEDa,UAAU,CAACD,OAAD,EAA0B;QAClC,MAAME,OAAO,GAAG,IAAKF,CAAAA,OAAL,KAAiBA,OAAjC,CAAA;QACA,IAAIE,OAAJ,EAAa;YACX,IAAKF,CAAAA,OAAL,GAAeA,OAAf,CAAA;YACA,IAAA,CAAKZ,OAAL,EAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDA,OAAO,GAAS;QACd,IAAA,CAAKe,SAAL,CAAeC,OAAf,CAAuB,CAAC,EAAEZ,QAAAA,EAAH,KAAkB;YACvCA,QAAQ,EAAA,CAAA;SADV,CAAA,CAAA;IAGD,CAAA;IAEDa,SAAS,GAAY;QACnB,IAAI,OAAO,IAAA,CAAKL,OAAZ,KAAwB,SAA5B,EAAuC;YACrC,OAAO,IAAA,CAAKA,OAAZ,CAAA;QACD,CAHkB,CAAA,qDAAA;QAMnB,IAAI,OAAOM,QAAP,KAAoB,WAAxB,EAAqC;YACnC,OAAO,IAAP,CAAA;QACD,CAAA;QAED,OAAO;YAACP,SAAD;YAAY,SAAZ;YAAuB,WAAvB;SAAoCQ,CAAAA,QAApC,CACLD,QAAQ,CAACE,eADJ,CAAP,CAAA;IAGD,CAAA;AA/E4C,CAAA;AAkFlCC,MAAAA,YAAY,GAAG,IAAIzB,YAAJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "file": "onlineManager.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/onlineManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n"], "names": ["onlineEvents", "OnlineManager", "Subscribable", "constructor", "setup", "onOnline", "isServer", "window", "addEventListener", "listener", "for<PERSON>ach", "event", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "online", "setOnline", "changed", "listeners", "isOnline", "navigator", "onLine", "onlineManager"], "mappings": ";;;;;;;;AAOA,MAAMA,YAAY,GAAG;IAAC,QAAD;IAAW,SAAX;CAArB,CAAA;AAEO,MAAMC,aAAN,6NAA4BC,eAA5B,CAAyC;IAM9CC,WAAW,EAAG;QACZ,KAAA,EAAA,CAAA;QACA,IAAKC,CAAAA,KAAL,IAAcC,QAAD,IAAc;YACzB,mEAAA;YACA,uEAAA;YACA,IAAI,8MAACC,WAAD,IAAaC,MAAM,CAACC,gBAAxB,EAA0C;gBACxC,MAAMC,QAAQ,GAAG,IAAMJ,QAAQ,EAA/B,CADwC,CAAA,mBAAA;gBAGxCL,YAAY,CAACU,OAAb,EAAsBC,KAAD,IAAW;oBAC9BJ,MAAM,CAACC,gBAAP,CAAwBG,KAAxB,EAA+BF,QAA/B,EAAyC,KAAzC,CAAA,CAAA;iBADF,CAAA,CAAA;gBAIA,OAAO,MAAM;oBACX,iDAAA;oBACAT,YAAY,CAACU,OAAb,EAAsBC,KAAD,IAAW;wBAC9BJ,MAAM,CAACK,mBAAP,CAA2BD,KAA3B,EAAkCF,QAAlC,CAAA,CAAA;qBADF,CAAA,CAAA;iBAFF,CAAA;YAMD,CAAA;YAED,OAAA;SAlBF,CAAA;IAoBD,CAAA;IAESI,WAAW,GAAS;QAC5B,IAAI,CAAC,IAAKC,CAAAA,OAAV,EAAmB;YACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKX,KAA3B,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAESY,aAAa,GAAG;QACxB,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;YAAA,IAAA,aAAA,CAAA;YACxB,CAAA,aAAA,GAAA,IAAA,CAAKH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YACA,IAAKA,CAAAA,OAAL,GAAeI,SAAf,CAAA;QACD,CAAA;IACF,CAAA;IAEDH,gBAAgB,CAACX,KAAD,EAAuB;QAAA,IAAA,cAAA,CAAA;QACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;QACA,CAAA,cAAA,GAAA,IAAA,CAAKU,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,CAAKA,OAAL,GAAeV,KAAK,EAAEe,MAAD,IAAsB;YACzC,IAAI,OAAOA,MAAP,KAAkB,SAAtB,EAAiC;gBAC/B,IAAKC,CAAAA,SAAL,CAAeD,MAAf,CAAA,CAAA;YACD,CAFD,MAEO;gBACL,IAAA,CAAKd,QAAL,EAAA,CAAA;YACD,CAAA;QACF,CANmB,CAApB,CAAA;IAOD,CAAA;IAEDe,SAAS,CAACD,MAAD,EAAyB;QAChC,MAAME,OAAO,GAAG,IAAKF,CAAAA,MAAL,KAAgBA,MAAhC,CAAA;QAEA,IAAIE,OAAJ,EAAa;YACX,IAAKF,CAAAA,MAAL,GAAcA,MAAd,CAAA;YACA,IAAA,CAAKd,QAAL,EAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDA,QAAQ,GAAS;QACf,IAAA,CAAKiB,SAAL,CAAeZ,OAAf,CAAuB,CAAC,EAAED,QAAAA,EAAH,KAAkB;YACvCA,QAAQ,EAAA,CAAA;SADV,CAAA,CAAA;IAGD,CAAA;IAEDc,QAAQ,GAAY;QAClB,IAAI,OAAO,IAAA,CAAKJ,MAAZ,KAAuB,SAA3B,EAAsC;YACpC,OAAO,IAAA,CAAKA,MAAZ,CAAA;QACD,CAAA;QAED,IACE,OAAOK,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACC,MAAjB,KAA4B,WAF9B,EAGE;YACA,OAAO,IAAP,CAAA;QACD,CAAA;QAED,OAAOD,SAAS,CAACC,MAAjB,CAAA;IACD,CAAA;AAnF6C,CAAA;AAsFnCC,MAAAA,aAAa,GAAG,IAAIzB,aAAJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "file": "retryer.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n"], "names": ["defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "onlineManager", "isOnline", "CancelledError", "constructor", "options", "revert", "silent", "isCancelledError", "value", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "Promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "focusManager", "isFocused", "resolve", "onSuccess", "onError", "pause", "continueResolve", "canContinue", "onPause", "then", "undefined", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "sleep", "continue", "didContinue"], "mappings": ";;;;;;;;;;;;AA0CA,SAASA,iBAAT,CAA2BC,YAA3B,EAAiD;IAC/C,OAAOC,IAAI,CAACC,GAAL,CAAS,OAAO,CAAKF,IAAAA,YAArB,EAAmC,KAAnC,CAAP,CAAA;AACD,CAAA;AAEM,SAASG,QAAT,CAAkBC,WAAlB,EAAiE;IACtE,OAAO,CAACA,WAAD,IAACA,IAAAA,GAAAA,WAAD,GAAgB,QAAhB,MAA8B,QAA9B,GACHC,qOAAa,CAACC,QAAd,EADG,GAEH,IAFJ,CAAA;AAGD,CAAA;AAEM,MAAMC,cAAN,CAAqB;IAG1BC,WAAW,CAACC,OAAD,CAA0B;QACnC,IAAA,CAAKC,MAAL,GAAcD,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEC,MAAvB,CAAA;QACA,IAAA,CAAKC,MAAL,GAAcF,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEE,MAAvB,CAAA;IACD,CAAA;AANyB,CAAA;AASrB,SAASC,gBAAT,CAA0BC,KAA1B,EAA+D;IACpE,OAAOA,KAAK,YAAYN,cAAxB,CAAA;AACD,CAAA;AAEM,SAASO,aAAT,CACLC,MADK,EAEW;IAChB,IAAIC,gBAAgB,GAAG,KAAvB,CAAA;IACA,IAAIhB,YAAY,GAAG,CAAnB,CAAA;IACA,IAAIiB,UAAU,GAAG,KAAjB,CAAA;IACA,IAAIC,UAAJ,CAAA;IACA,IAAIC,cAAJ,CAAA;IACA,IAAIC,aAAJ,CAAA;IAEA,MAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAmB,CAACC,YAAD,EAAeC,WAAf,KAA+B;QAChEL,cAAc,GAAGI,YAAjB,CAAA;QACAH,aAAa,GAAGI,WAAhB,CAAA;IACD,CAHe,CAAhB,CAAA;IAKA,MAAMC,MAAM,IAAIC,aAAD,IAAyC;QACtD,IAAI,CAACT,UAAL,EAAiB;YACfU,MAAM,CAAC,IAAIpB,cAAJ,CAAmBmB,aAAnB,CAAD,CAAN,CAAA;YAEAX,MAAM,CAACa,KAAP,IAAAb,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACa,KAAP,EAAA,CAAA;QACD,CAAA;KALH,CAAA;IAOA,MAAMC,WAAW,GAAG,MAAM;QACxBb,gBAAgB,GAAG,IAAnB,CAAA;KADF,CAAA;IAIA,MAAMc,aAAa,GAAG,MAAM;QAC1Bd,gBAAgB,GAAG,KAAnB,CAAA;KADF,CAAA;IAIA,MAAMe,WAAW,GAAG,IAClB,qNAACC,eAAY,CAACC,SAAb,EAAD,IACClB,MAAM,CAACX,WAAP,KAAuB,QAAvB,IAAmC,sNAACC,gBAAa,CAACC,QAAd,EAFvC,CAAA;IAIA,MAAM4B,OAAO,IAAIrB,KAAD,IAAgB;QAC9B,IAAI,CAACI,UAAL,EAAiB;YACfA,UAAU,GAAG,IAAb,CAAA;YACAF,MAAM,CAACoB,SAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAApB,MAAM,CAACoB,SAAP,CAAmBtB,KAAnB,CAAA,CAAA;YACAK,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,EAAA,CAAA;YACVC,cAAc,CAACN,KAAD,CAAd,CAAA;QACD,CAAA;KANH,CAAA;IASA,MAAMc,MAAM,IAAId,KAAD,IAAgB;QAC7B,IAAI,CAACI,UAAL,EAAiB;YACfA,UAAU,GAAG,IAAb,CAAA;YACAF,MAAM,CAACqB,OAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAArB,MAAM,CAACqB,OAAP,CAAiBvB,KAAjB,CAAA,CAAA;YACAK,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,EAAA,CAAA;YACVE,aAAa,CAACP,KAAD,CAAb,CAAA;QACD,CAAA;KANH,CAAA;IASA,MAAMwB,KAAK,GAAG,MAAM;QAClB,OAAO,IAAIf,OAAJ,EAAagB,eAAD,IAAqB;YACtCpB,UAAU,IAAIL,KAAD,IAAW;gBACtB,MAAM0B,WAAW,GAAGtB,UAAU,IAAI,CAACc,WAAW,EAA9C,CAAA;gBACA,IAAIQ,WAAJ,EAAiB;oBACfD,eAAe,CAACzB,KAAD,CAAf,CAAA;gBACD,CAAA;gBACD,OAAO0B,WAAP,CAAA;aALF,CAAA;YAOAxB,MAAM,CAACyB,OAAP,IAAAzB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACyB,OAAP,EAAA,CAAA;SARK,CAAA,CASJC,IATI,CASC,MAAM;YACZvB,UAAU,GAAGwB,SAAb,CAAA;YACA,IAAI,CAACzB,UAAL,EAAiB;gBACfF,MAAM,CAAC4B,UAAP,IAAA5B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAAC4B,UAAP,EAAA,CAAA;YACD,CAAA;QACF,CAdM,CAAP,CAAA;IAeD,CAhBD,CAlDgB,CAAA,uBAAA;IAqEhB,MAAMC,GAAG,GAAG,MAAM;QAChB,iCAAA;QACA,IAAI3B,UAAJ,EAAgB;YACd,OAAA;QACD,CAAA;QAED,IAAI4B,cAAJ,CANgB,CAAA,gBAAA;QAShB,IAAI;YACFA,cAAc,GAAG9B,MAAM,CAAC+B,EAAP,EAAjB,CAAA;SADF,CAEE,OAAOC,KAAP,EAAc;YACdF,cAAc,GAAGvB,OAAO,CAACK,MAAR,CAAeoB,KAAf,CAAjB,CAAA;QACD,CAAA;QAEDzB,OAAO,CAACY,OAAR,CAAgBW,cAAhB,CACGJ,CAAAA,IADH,CACQP,OADR,CAEGc,CAAAA,KAFH,EAEUD,KAAD,IAAW;YAAA,IAAA,aAAA,EAAA,kBAAA,CAAA;YAChB,wCAAA;YACA,IAAI9B,UAAJ,EAAgB;gBACd,OAAA;YACD,CAJe,CAAA,mCAAA;YAOhB,MAAMgC,KAAK,GAAGlC,CAAAA,aAAAA,GAAAA,MAAM,CAACkC,KAAV,KAAA,OAAA,gBAAmB,CAA9B,CAAA;YACA,MAAMC,UAAU,GAAGnC,CAAAA,kBAAAA,GAAAA,MAAM,CAACmC,UAAV,KAAA,OAAA,qBAAwBnD,iBAAxC,CAAA;YACA,MAAMoD,KAAK,GACT,OAAOD,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAClD,YAAD,EAAe+C,KAAf,CADd,GAEIG,UAHN,CAAA;YAIA,MAAME,WAAW,GACfH,KAAK,KAAK,IAAV,IACC,OAAOA,KAAP,KAAiB,QAAjB,IAA6BjD,YAAY,GAAGiD,KAD7C,IAEC,OAAOA,KAAP,KAAiB,UAAjB,IAA+BA,KAAK,CAACjD,YAAD,EAAe+C,KAAf,CAHvC,CAAA;YAKA,IAAI/B,gBAAgB,IAAI,CAACoC,WAAzB,EAAsC;gBACpC,uDAAA;gBACAzB,MAAM,CAACoB,KAAD,CAAN,CAAA;gBACA,OAAA;YACD,CAAA;YAED/C,YAAY,GAxBI,CAAA,iBAAA;YA2BhBe,MAAM,CAACsC,MAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAtC,MAAM,CAACsC,MAAP,CAAgBrD,YAAhB,EAA8B+C,KAA9B,CAAA,CA3BgB,CAAA,QAAA;6NA8BhBO,QAAAA,AAAK,EAACH,KAAD,CAAL,CAAA,qEAAA;aAEGV,IAFH,CAEQ,MAAM;gBACV,IAAIV,WAAW,EAAf,EAAmB;oBACjB,OAAOM,KAAK,EAAZ,CAAA;gBACD,CAAA;gBACD,OAAA;aANJ,CAAA,CAQGI,IARH,CAQQ,MAAM;gBACV,IAAIzB,gBAAJ,EAAsB;oBACpBW,MAAM,CAACoB,KAAD,CAAN,CAAA;gBACD,CAFD,MAEO;oBACLH,GAAG,EAAA,CAAA;gBACJ,CAAA;aAbL,CAAA,CAAA;SAhCJ,CAAA,CAAA;IAgDD,CA/DD,CArEgB,CAAA,aAAA;IAuIhB,IAAIzC,QAAQ,CAACY,MAAM,CAACX,WAAR,CAAZ,EAAkC;QAChCwC,GAAG,EAAA,CAAA;IACJ,CAFD,MAEO;QACLP,KAAK,EAAA,CAAGI,IAAR,CAAaG,GAAb,CAAA,CAAA;IACD,CAAA;IAED,OAAO;QACLvB,OADK;QAELI,MAFK;QAGL8B,QAAQ,EAAE,MAAM;YACd,MAAMC,WAAW,GAAGtC,UAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,EAA9B,CAAA;YACA,OAAOsC,WAAW,GAAGnC,OAAH,GAAaC,OAAO,CAACY,OAAR,EAA/B,CAAA;SALG;QAOLL,WAPK;QAQLC,aAAAA;KARF,CAAA;AAUD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "file": "queryObserver.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/queryObserver.ts"], "sourcesContent": ["import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "names": ["QueryObserver", "Subscribable", "constructor", "client", "options", "trackedProps", "Set", "selectError", "bindMethods", "setOptions", "remove", "bind", "refetch", "onSubscribe", "listeners", "size", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "process", "env", "NODE_ENV", "isDataEqual", "<PERSON><PERSON><PERSON><PERSON>", "error", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "result", "createResult", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "state", "getCurrentResult", "trackResult", "trackedResult", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "configurable", "enumerable", "get", "add", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchPage", "fetch", "meta", "fetchOptimistic", "defaultedOptions", "isFetchingOptimistic", "then", "fetchOptions", "cancelRefetch", "promise", "throwOnError", "catch", "noop", "isServer", "isStale", "isValidTimeout", "time", "timeUntilStale", "dataUpdatedAt", "timeout", "staleTimeoutId", "setTimeout", "refetchInterval", "data", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "focusManager", "isFocused", "clearTimeout", "undefined", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "placeholderData", "Date", "now", "isFetching", "isLoading", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "some", "<PERSON><PERSON><PERSON>", "changed", "has", "onQueryUpdate", "action", "onSuccess", "manual", "isCancelledError", "onError", "notify<PERSON><PERSON>ger", "batch", "onSettled", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "value", "suspense", "isStaleByTime", "optimisticResult"], "mappings": ";;;;;;;;;;;;;AAwCO,MAAMA,aAAN,SAMGC,mOANH,CAMsD;IA8B3DC,WAAW,CACTC,MADS,EAETC,OAFS,CAST;QACA,KAAA,EAAA,CAAA;QAEA,IAAKD,CAAAA,MAAL,GAAcA,MAAd,CAAA;QACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAIC,GAAJ,EAApB,CAAA;QACA,IAAKC,CAAAA,WAAL,GAAmB,IAAnB,CAAA;QACA,IAAA,CAAKC,WAAL,EAAA,CAAA;QACA,IAAKC,CAAAA,UAAL,CAAgBL,OAAhB,CAAA,CAAA;IACD,CAAA;IAESI,WAAW,GAAS;QAC5B,IAAKE,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAd,CAAA;QACA,IAAKC,CAAAA,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAaD,IAAb,CAAkB,IAAlB,CAAf,CAAA;IACD,CAAA;IAESE,WAAW,GAAS;QAC5B,IAAI,IAAA,CAAKC,SAAL,CAAeC,IAAf,KAAwB,CAA5B,EAA+B;YAC7B,IAAA,CAAKC,YAAL,CAAkBC,WAAlB,CAA8B,IAA9B,CAAA,CAAA;YAEA,IAAIC,kBAAkB,CAAC,IAAKF,CAAAA,YAAN,EAAoB,IAAKZ,CAAAA,OAAzB,CAAtB,EAAyD;gBACvD,IAAA,CAAKe,YAAL,EAAA,CAAA;YACD,CAAA;YAED,IAAA,CAAKC,YAAL,EAAA,CAAA;QACD,CAAA;IACF,CAAA;IAESC,aAAa,GAAS;QAC9B,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;YACxB,IAAA,CAAKC,OAAL,EAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDC,sBAAsB,GAAY;QAChC,OAAOC,aAAa,CAClB,IAAKT,CAAAA,YADa,EAElB,IAAA,CAAKZ,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAasB,kBAHK,CAApB,CAAA;IAKD,CAAA;IAEDC,wBAAwB,GAAY;QAClC,OAAOF,aAAa,CAClB,IAAKT,CAAAA,YADa,EAElB,IAAA,CAAKZ,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAawB,oBAHK,CAApB,CAAA;IAKD,CAAA;IAEDL,OAAO,GAAS;QACd,IAAA,CAAKT,SAAL,GAAiB,IAAIR,GAAJ,EAAjB,CAAA;QACA,IAAA,CAAKuB,iBAAL,EAAA,CAAA;QACA,IAAA,CAAKC,oBAAL,EAAA,CAAA;QACA,IAAA,CAAKd,YAAL,CAAkBe,cAAlB,CAAiC,IAAjC,CAAA,CAAA;IACD,CAAA;IAEDtB,UAAU,CACRL,OADQ,EAQR4B,aARQ,EASF;QACN,MAAMC,WAAW,GAAG,IAAA,CAAK7B,OAAzB,CAAA;QACA,MAAM8B,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;QAEA,IAAKZ,CAAAA,OAAL,GAAe,IAAKD,CAAAA,MAAL,CAAYgC,mBAAZ,CAAgC/B,OAAhC,CAAf,CAAA;QAEA,IACEgC,OAAO,CAACC,GAAR,CAAYC,QAAZ,gCAAyB,YAAzB,IACA,OAAA,CAAOlC,OAAP,IAAA,OAAA,KAAA,IAAOA,OAAO,CAAEmC,WAAhB,CAAA,KAAgC,WAFlC,EAGE;YACA,IAAA,CAAKpC,MAAL,CACGqC,SADH,EAAA,CAEGC,KAFH,CAAA,wLAAA,CAAA,CAAA;QAKD,CAAA;QAED,IAAI,kNAACC,sBAAmB,AAAnBA,EAAoBT,WAAD,EAAc,IAAK7B,CAAAA,OAAnB,CAAxB,EAAqD;YACnD,IAAA,CAAKD,MAAL,CAAYwC,aAAZ,EAAA,CAA4BC,MAA5B,CAAmC;gBACjCC,IAAI,EAAE,wBAD2B;gBAEjCC,KAAK,EAAE,IAAA,CAAK9B,YAFqB;gBAGjC+B,QAAQ,EAAE,IAAA;aAHZ,CAAA,CAAA;QAKD,CAAA;QAED,IACE,OAAO,IAAA,CAAK3C,OAAL,CAAa4C,OAApB,KAAgC,WAAhC,IACA,OAAO,IAAA,CAAK5C,OAAL,CAAa4C,OAApB,KAAgC,SAFlC,EAGE;YACA,MAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN,CAAA;QACD,CA9BK,CAAA,0DAAA;QAiCN,IAAI,CAAC,IAAA,CAAK7C,OAAL,CAAa8C,QAAlB,EAA4B;YAC1B,IAAA,CAAK9C,OAAL,CAAa8C,QAAb,GAAwBjB,WAAW,CAACiB,QAApC,CAAA;QACD,CAAA;QAED,IAAA,CAAKC,WAAL,EAAA,CAAA;QAEA,MAAMC,OAAO,GAAG,IAAA,CAAK9B,YAAL,EAAhB,CAvCM,CAAA,iCAAA;QA0CN,IACE8B,OAAO,IACPC,qBAAqB,CACnB,IAAA,CAAKrC,YADc,EAEnBkB,SAFmB,EAGnB,IAAK9B,CAAAA,OAHc,EAInB6B,WAJmB,CAFvB,EAQE;YACA,IAAA,CAAKd,YAAL,EAAA,CAAA;QACD,CApDK,CAAA,gBAAA;QAuDN,IAAA,CAAKmC,YAAL,CAAkBtB,aAAlB,CAAA,CAvDM,CAAA,kCAAA;QA0DN,IACEoB,OAAO,IAAA,CACN,IAAA,CAAKpC,YAAL,KAAsBkB,SAAtB,IACC,IAAA,CAAK9B,OAAL,CAAa4C,OAAb,KAAyBf,WAAW,CAACe,OADtC,IAEC,IAAA,CAAK5C,OAAL,CAAamD,SAAb,KAA2BtB,WAAW,CAACsB,SAHlC,CADT,EAKE;YACA,IAAA,CAAKC,kBAAL,EAAA,CAAA;QACD,CAAA;QAED,MAAMC,mBAAmB,GAAG,IAAA,CAAKC,sBAAL,EAA5B,CAnEM,CAAA,oCAAA;QAsEN,IACEN,OAAO,IAAA,CACN,IAAKpC,CAAAA,YAAL,KAAsBkB,SAAtB,IACC,IAAA,CAAK9B,OAAL,CAAa4C,OAAb,KAAyBf,WAAW,CAACe,OADtC,IAECS,mBAAmB,KAAK,IAAA,CAAKE,sBAHxB,CADT,EAKE;YACA,IAAKC,CAAAA,qBAAL,CAA2BH,mBAA3B,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDI,mBAAmB,CACjBzD,OADiB,EAQmB;QACpC,MAAM0C,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CAAYwC,aAAZ,EAA4BmB,CAAAA,KAA5B,CAAkC,IAAA,CAAK3D,MAAvC,EAA+CC,OAA/C,CAAd,CAAA;QAEA,MAAM2D,MAAM,GAAG,IAAKC,CAAAA,YAAL,CAAkBlB,KAAlB,EAAyB1C,OAAzB,CAAf,CAAA;QAEA,IAAI6D,qCAAqC,CAAC,IAAD,EAAOF,MAAP,EAAe3D,OAAf,CAAzC,EAAkE;YAChE,6DAAA;YACA,qEAAA;YACA,wCAAA;YACA,mEAAA;YACA,yEAAA;YACA,6CAAA;YACA,uEAAA;YACA,wEAAA;YACA,sEAAA;YACA,yEAAA;YACA,uBAAA;YACA,mEAAA;YACA,yCAAA;YAEA,sEAAA;YACA,gBAAA;YACA,IAAK8D,CAAAA,aAAL,GAAqBH,MAArB,CAAA;YACA,IAAKI,CAAAA,oBAAL,GAA4B,IAAA,CAAK/D,OAAjC,CAAA;YACA,IAAA,CAAKgE,kBAAL,GAA0B,IAAKpD,CAAAA,YAAL,CAAkBqD,KAA5C,CAAA;QACD,CAAA;QACD,OAAON,MAAP,CAAA;IACD,CAAA;IAEDO,gBAAgB,GAAuC;QACrD,OAAO,IAAA,CAAKJ,aAAZ,CAAA;IACD,CAAA;IAEDK,WAAW,CACTR,MADS,EAE2B;QACpC,MAAMS,aAAa,GAAG,CAAA,CAAtB,CAAA;QAEAC,MAAM,CAACC,IAAP,CAAYX,MAAZ,EAAoBY,OAApB,EAA6BC,GAAD,IAAS;YACnCH,MAAM,CAACI,cAAP,CAAsBL,aAAtB,EAAqCI,GAArC,EAA0C;gBACxCE,YAAY,EAAE,KAD0B;gBAExCC,UAAU,EAAE,IAF4B;gBAGxCC,GAAG,EAAE,MAAM;oBACT,IAAA,CAAK3E,YAAL,CAAkB4E,GAAlB,CAAsBL,GAAtB,CAAA,CAAA;oBACA,OAAOb,MAAM,CAACa,GAAD,CAAb,CAAA;gBACD,CAAA;aANH,CAAA,CAAA;SADF,CAAA,CAAA;QAWA,OAAOJ,aAAP,CAAA;IACD,CAAA;IAEDU,eAAe,GAAuD;QACpE,OAAO,IAAA,CAAKlE,YAAZ,CAAA;IACD,CAAA;IAEDN,MAAM,GAAS;QACb,IAAA,CAAKP,MAAL,CAAYwC,aAAZ,GAA4BjC,MAA5B,CAAmC,IAAA,CAAKM,YAAxC,CAAA,CAAA;IACD,CAAA;IAEDJ,OAAO,CAAY,EACjBuE,WADiB,EAEjB,GAAG/E,OAAAA,EAFc,GAGiC,CAAA,CAH7C,EAKL;QACA,OAAO,IAAKgF,CAAAA,KAAL,CAAW;YAChB,GAAGhF,OADa;YAEhBiF,IAAI,EAAE;gBAAEF,WAAAA;YAAF,CAAA;QAFU,CAAX,CAAP,CAAA;IAID,CAAA;IAEDG,eAAe,CACblF,OADa,EAQgC;QAC7C,MAAMmF,gBAAgB,GAAG,IAAKpF,CAAAA,MAAL,CAAYgC,mBAAZ,CAAgC/B,OAAhC,CAAzB,CAAA;QAEA,MAAM0C,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CACXwC,aADW,EAEXmB,CAAAA,KAFW,CAEL,IAAA,CAAK3D,MAFA,EAEQoF,gBAFR,CAAd,CAAA;QAGAzC,KAAK,CAAC0C,oBAAN,GAA6B,IAA7B,CAAA;QAEA,OAAO1C,KAAK,CAACsC,KAAN,EAAA,CAAcK,IAAd,CAAmB,IAAM,IAAKzB,CAAAA,YAAL,CAAkBlB,KAAlB,EAAyByC,gBAAzB,CAAzB,CAAP,CAAA;IACD,CAAA;IAESH,KAAK,CACbM,YADa,EAEgC;QAAA,IAAA,qBAAA,CAAA;QAC7C,OAAO,IAAKvE,CAAAA,YAAL,CAAkB;YACvB,GAAGuE,YADoB;YAEvBC,aAAa,EAAED,CAAAA,qBAAAA,GAAAA,YAAY,CAACC,aAAf,KAAgC,IAAA,GAAA,qBAAA,GAAA,IAAA;SAFxC,CAAA,CAGJF,IAHI,CAGC,MAAM;YACZ,IAAA,CAAKnC,YAAL,EAAA,CAAA;YACA,OAAO,IAAA,CAAKY,aAAZ,CAAA;QACD,CANM,CAAP,CAAA;IAOD,CAAA;IAEO/C,YAAY,CAClBuE,YADkB,EAEe;QACjC,qFAAA;QACA,IAAKvC,CAAAA,WAAL,GAFiC,CAAA,QAAA;QAKjC,IAAIyC,OAAwC,GAAG,IAAA,CAAK5E,YAAL,CAAkBoE,KAAlB,CAC7C,IAAKhF,CAAAA,OADwC,EAE7CsF,YAF6C,CAA/C,CAAA;QAKA,IAAI,CAAA,CAACA,YAAD,IAAA,IAAA,IAACA,YAAY,CAAEG,YAAf,CAAJ,EAAiC;YAC/BD,OAAO,GAAGA,OAAO,CAACE,KAAR,8MAAcC,OAAd,CAAV,CAAA;QACD,CAAA;QAED,OAAOH,OAAP,CAAA;IACD,CAAA;IAEOpC,kBAAkB,GAAS;QACjC,IAAA,CAAK3B,iBAAL,EAAA,CAAA;QAEA,iNACEmE,WAAQ,IACR,IAAK9B,CAAAA,aAAL,CAAmB+B,OADnB,IAEA,kNAACC,iBAAAA,AAAc,EAAC,IAAK9F,CAAAA,OAAL,CAAamD,SAAd,CAHjB,EAIE;YACA,OAAA;QACD,CAAA;QAED,MAAM4C,IAAI,oNAAGC,iBAAc,AAAdA,EACX,IAAA,CAAKlC,aAAL,CAAmBmC,aADM,EAEzB,IAAA,CAAKjG,OAAL,CAAamD,SAFY,CAA3B,CAXiC,CAAA,4EAAA;QAiBjC,4DAAA;QACA,MAAM+C,OAAO,GAAGH,IAAI,GAAG,CAAvB,CAAA;QAEA,IAAA,CAAKI,cAAL,GAAsBC,UAAU,CAAC,MAAM;YACrC,IAAI,CAAC,IAAA,CAAKtC,aAAL,CAAmB+B,OAAxB,EAAiC;gBAC/B,IAAA,CAAK3C,YAAL,EAAA,CAAA;YACD,CAAA;SAH6B,EAI7BgD,OAJ6B,CAAhC,CAAA;IAKD,CAAA;IAEO5C,sBAAsB,GAAG;QAAA,IAAA,qBAAA,CAAA;QAC/B,OAAO,OAAO,IAAKtD,CAAAA,OAAL,CAAaqG,eAApB,KAAwC,UAAxC,GACH,IAAA,CAAKrG,OAAL,CAAaqG,eAAb,CAA6B,IAAKvC,CAAAA,aAAL,CAAmBwC,IAAhD,EAAsD,IAAA,CAAK1F,YAA3D,CADG,GAEH,CAAA,qBAAA,GAAA,IAAA,CAAKZ,OAAL,CAAaqG,eAFV,KAAA,IAAA,GAAA,qBAAA,GAE6B,KAFpC,CAAA;IAGD,CAAA;IAEO7C,qBAAqB,CAAC+C,YAAD,EAAqC;QAChE,IAAA,CAAK7E,oBAAL,EAAA,CAAA;QAEA,IAAK6B,CAAAA,sBAAL,GAA8BgD,YAA9B,CAAA;QAEA,iNACEX,WAAQ,IACR,IAAK5F,CAAAA,OAAL,CAAa4C,OAAb,KAAyB,KADzB,IAEA,kNAACkD,iBAAAA,AAAc,EAAC,IAAA,CAAKvC,sBAAN,CAFf,IAGA,IAAA,CAAKA,sBAAL,KAAgC,CAJlC,EAKE;YACA,OAAA;QACD,CAAA;QAED,IAAA,CAAKiD,iBAAL,GAAyBC,WAAW,CAAC,MAAM;YACzC,IACE,IAAA,CAAKzG,OAAL,CAAa0G,2BAAb,wNACAC,eAAY,CAACC,SAAb,EAFF,EAGE;gBACA,IAAA,CAAK7F,YAAL,EAAA,CAAA;YACD,CAAA;SANiC,EAOjC,IAAKwC,CAAAA,sBAP4B,CAApC,CAAA;IAQD,CAAA;IAEOvC,YAAY,GAAS;QAC3B,IAAA,CAAKoC,kBAAL,EAAA,CAAA;QACA,IAAA,CAAKI,qBAAL,CAA2B,IAAKF,CAAAA,sBAAL,EAA3B,CAAA,CAAA;IACD,CAAA;IAEO7B,iBAAiB,GAAS;QAChC,IAAI,IAAA,CAAK0E,cAAT,EAAyB;YACvBU,YAAY,CAAC,IAAKV,CAAAA,cAAN,CAAZ,CAAA;YACA,IAAKA,CAAAA,cAAL,GAAsBW,SAAtB,CAAA;QACD,CAAA;IACF,CAAA;IAEOpF,oBAAoB,GAAS;QACnC,IAAI,IAAA,CAAK8E,iBAAT,EAA4B;YAC1BO,aAAa,CAAC,IAAKP,CAAAA,iBAAN,CAAb,CAAA;YACA,IAAKA,CAAAA,iBAAL,GAAyBM,SAAzB,CAAA;QACD,CAAA;IACF,CAAA;IAESlD,YAAY,CACpBlB,KADoB,EAEpB1C,OAFoB,EASgB;QACpC,MAAM8B,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;QACA,MAAMiB,WAAW,GAAG,IAAA,CAAK7B,OAAzB,CAAA;QACA,MAAMgH,UAAU,GAAG,IAAA,CAAKlD,aAAxB,CAAA;QAGA,MAAMmD,eAAe,GAAG,IAAA,CAAKjD,kBAA7B,CAAA;QACA,MAAMkD,iBAAiB,GAAG,IAAA,CAAKnD,oBAA/B,CAAA;QACA,MAAMoD,WAAW,GAAGzE,KAAK,KAAKZ,SAA9B,CAAA;QACA,MAAMsF,iBAAiB,GAAGD,WAAW,GACjCzE,KAAK,CAACuB,KAD2B,GAEjC,IAAA,CAAKoD,wBAFT,CAAA;QAGA,MAAMC,eAAe,GAAGH,WAAW,GAC/B,IAAA,CAAKrD,aAD0B,GAE/B,IAAA,CAAKyD,mBAFT,CAAA;QAIA,MAAM,EAAEtD,KAAAA,EAAF,GAAYvB,KAAlB,CAAA;QACA,IAAI,EAAEuD,aAAF,EAAiB5D,KAAjB,EAAwBmF,cAAxB,EAAwCC,WAAxC,EAAqDC,MAAAA,EAArD,GAAgEzD,KAApE,CAAA;QACA,IAAI0D,cAAc,GAAG,KAArB,CAAA;QACA,IAAIC,iBAAiB,GAAG,KAAxB,CAAA;QACA,IAAItB,IAAJ,CApBoC,CAAA,wDAAA;QAuBpC,IAAItG,OAAO,CAAC6H,kBAAZ,EAAgC;YAC9B,MAAM7E,OAAO,GAAG,IAAK9B,CAAAA,YAAL,EAAhB,CAAA;YAEA,MAAM4G,YAAY,GAAG,CAAC9E,OAAD,IAAYlC,kBAAkB,CAAC4B,KAAD,EAAQ1C,OAAR,CAAnD,CAAA;YAEA,MAAM+H,eAAe,GACnB/E,OAAO,IAAIC,qBAAqB,CAACP,KAAD,EAAQZ,SAAR,EAAmB9B,OAAnB,EAA4B6B,WAA5B,CADlC,CAAA;YAGA,IAAIiG,YAAY,IAAIC,eAApB,EAAqC;gBACnCN,WAAW,GAAGO,8NAAQ,AAARA,EAAStF,KAAK,CAAC1C,OAAN,CAAciI,WAAf,CAAR,GACV,UADU,GAEV,QAFJ,CAAA;gBAGA,IAAI,CAAChC,aAAL,EAAoB;oBAClByB,MAAM,GAAG,SAAT,CAAA;gBACD,CAAA;YACF,CAAA;YACD,IAAI1H,OAAO,CAAC6H,kBAAR,KAA+B,aAAnC,EAAkD;gBAChDJ,WAAW,GAAG,MAAd,CAAA;YACD,CAAA;QACF,CA1CmC,CAAA,+BAAA;QA6CpC,IACEzH,OAAO,CAACkI,gBAAR,IACA,CAACjE,KAAK,CAACgC,aADP,IAEAqB,eAFA,IAAA,IAAA,IAEAA,eAAe,CAAEa,SAFjB,IAGAT,MAAM,KAAK,OAJb,EAKE;YACApB,IAAI,GAAGgB,eAAe,CAAChB,IAAvB,CAAA;YACAL,aAAa,GAAGqB,eAAe,CAACrB,aAAhC,CAAA;YACAyB,MAAM,GAAGJ,eAAe,CAACI,MAAzB,CAAA;YACAC,cAAc,GAAG,IAAjB,CAAA;QACD,CAVD,MAYK,IAAI3H,OAAO,CAACoI,MAAR,IAAkB,OAAOnE,KAAK,CAACqC,IAAb,KAAsB,WAA5C,EAAyD;YAC5D,wBAAA;YACA,IACEU,UAAU,IACV/C,KAAK,CAACqC,IAAN,KAAA,CAAeW,eAAf,IAAeA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAe,CAAEX,IAAhC,CADA,IAEAtG,OAAO,CAACoI,MAAR,KAAmB,IAAA,CAAKC,QAH1B,EAIE;gBACA/B,IAAI,GAAG,IAAA,CAAKgC,YAAZ,CAAA;YACD,CAND,MAMO;gBACL,IAAI;oBACF,IAAA,CAAKD,QAAL,GAAgBrI,OAAO,CAACoI,MAAxB,CAAA;oBACA9B,IAAI,GAAGtG,OAAO,CAACoI,MAAR,CAAenE,KAAK,CAACqC,IAArB,CAAP,CAAA;oBACAA,IAAI,oNAAGiC,cAAAA,AAAW,EAACvB,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAEV,IAAb,EAAmBA,IAAnB,EAAyBtG,OAAzB,CAAlB,CAAA;oBACA,IAAKsI,CAAAA,YAAL,GAAoBhC,IAApB,CAAA;oBACA,IAAKnG,CAAAA,WAAL,GAAmB,IAAnB,CAAA;iBALF,CAME,OAAOA,WAAP,EAAoB;oBACpB,IAAI6B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;wBACE,IAAA,CAAKnC,MAAL,CAAYqC,SAAZ,EAAwBC,CAAAA,KAAxB,CAA8BlC,WAA9B,CAAA,CAAA;oBACD,CAAA;oBACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;gBACD,CAAA;YACF,CAAA;QACF,CAtBI,MAwBA;YACHmG,IAAI,GAAGrC,KAAK,CAACqC,IAAb,CAAA;QACD,CAnFmC,CAAA,kCAAA;QAsFpC,IACE,OAAOtG,OAAO,CAACwI,eAAf,KAAmC,WAAnC,IACA,OAAOlC,IAAP,KAAgB,WADhB,IAEAoB,MAAM,KAAK,SAHb,EAIE;YACA,IAAIc,eAAJ,CADA,CAAA,2BAAA;YAIA,IACExB,UAAU,IAAV,IAAA,IAAAA,UAAU,CAAEY,iBAAZ,IACA5H,OAAO,CAACwI,eAAR,KAAA,CAA4BtB,iBAA5B,IAAA,IAAA,GAAA,KAAA,CAAA,GAA4BA,iBAAiB,CAAEsB,eAA/C,CAFF,EAGE;gBACAA,eAAe,GAAGxB,UAAU,CAACV,IAA7B,CAAA;YACD,CALD,MAKO;gBACLkC,eAAe,GACb,OAAOxI,OAAO,CAACwI,eAAf,KAAmC,UAAnC,GACKxI,OAAO,CAACwI,eAAT,EADJ,GAEIxI,OAAO,CAACwI,eAHd,CAAA;gBAIA,IAAIxI,OAAO,CAACoI,MAAR,IAAkB,OAAOI,eAAP,KAA2B,WAAjD,EAA8D;oBAC5D,IAAI;wBACFA,eAAe,GAAGxI,OAAO,CAACoI,MAAR,CAAeI,eAAf,CAAlB,CAAA;wBACA,IAAKrI,CAAAA,WAAL,GAAmB,IAAnB,CAAA;qBAFF,CAGE,OAAOA,WAAP,EAAoB;wBACpB,IAAI6B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;4BACE,IAAA,CAAKnC,MAAL,CAAYqC,SAAZ,EAAwBC,CAAAA,KAAxB,CAA8BlC,WAA9B,CAAA,CAAA;wBACD,CAAA;wBACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;oBACD,CAAA;gBACF,CAAA;YACF,CAAA;YAED,IAAI,OAAOqI,eAAP,KAA2B,WAA/B,EAA4C;gBAC1Cd,MAAM,GAAG,SAAT,CAAA;gBACApB,IAAI,oNAAGiC,cAAAA,AAAW,EAACvB,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAEV,IAAb,EAAmBkC,eAAnB,EAAoCxI,OAApC,CAAlB,CAAA;gBACA4H,iBAAiB,GAAG,IAApB,CAAA;YACD,CAAA;QACF,CAAA;QAED,IAAI,IAAA,CAAKzH,WAAT,EAAsB;YACpBkC,KAAK,GAAG,IAAA,CAAKlC,WAAb,CAAA;YACAmG,IAAI,GAAG,IAAA,CAAKgC,YAAZ,CAAA;YACAd,cAAc,GAAGiB,IAAI,CAACC,GAAL,EAAjB,CAAA;YACAhB,MAAM,GAAG,OAAT,CAAA;QACD,CAAA;QAED,MAAMiB,UAAU,GAAGlB,WAAW,KAAK,UAAnC,CAAA;QACA,MAAMmB,SAAS,GAAGlB,MAAM,KAAK,SAA7B,CAAA;QACA,MAAMmB,OAAO,GAAGnB,MAAM,KAAK,OAA3B,CAAA;QAEA,MAAM/D,MAA8C,GAAG;YACrD+D,MADqD;YAErDD,WAFqD;YAGrDmB,SAHqD;YAIrDT,SAAS,EAAET,MAAM,KAAK,SAJ+B;YAKrDmB,OALqD;YAMrDC,gBAAgB,EAAEF,SAAS,IAAID,UANsB;YAOrDrC,IAPqD;YAQrDL,aARqD;YASrD5D,KATqD;YAUrDmF,cAVqD;YAWrDuB,YAAY,EAAE9E,KAAK,CAAC+E,iBAXiC;YAYrDC,aAAa,EAAEhF,KAAK,CAACiF,kBAZgC;YAarDC,gBAAgB,EAAElF,KAAK,CAACkF,gBAb6B;YAcrDC,SAAS,EAAEnF,KAAK,CAACoF,eAAN,GAAwB,CAAxB,IAA6BpF,KAAK,CAACkF,gBAAN,GAAyB,CAdZ;YAerDG,mBAAmB,EACjBrF,KAAK,CAACoF,eAAN,GAAwBjC,iBAAiB,CAACiC,eAA1C,IACApF,KAAK,CAACkF,gBAAN,GAAyB/B,iBAAiB,CAAC+B,gBAjBQ;YAkBrDR,UAlBqD;YAmBrDY,YAAY,EAAEZ,UAAU,IAAI,CAACC,SAnBwB;YAoBrDY,cAAc,EAAEX,OAAO,IAAI5E,KAAK,CAACgC,aAAN,KAAwB,CApBE;YAqBrDwD,QAAQ,EAAEhC,WAAW,KAAK,QArB2B;YAsBrDG,iBAtBqD;YAuBrDD,cAvBqD;YAwBrD+B,cAAc,EAAEb,OAAO,IAAI5E,KAAK,CAACgC,aAAN,KAAwB,CAxBE;YAyBrDJ,OAAO,EAAEA,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAzBqC;YA0BrDQ,OAAO,EAAE,IAAA,CAAKA,OA1BuC;YA2BrDF,MAAM,EAAE,IAAKA,CAAAA,MAAAA;SA3Bf,CAAA;QA8BA,OAAOqD,MAAP,CAAA;IACD,CAAA;IAEDT,YAAY,CAACtB,aAAD,EAAsC;QAChD,MAAMoF,UAAU,GAAG,IAAA,CAAKlD,aAAxB,CAAA;QAIA,MAAM6F,UAAU,GAAG,IAAA,CAAK/F,YAAL,CAAkB,IAAA,CAAKhD,YAAvB,EAAqC,IAAKZ,CAAAA,OAA1C,CAAnB,CAAA;QACA,IAAA,CAAKgE,kBAAL,GAA0B,IAAKpD,CAAAA,YAAL,CAAkBqD,KAA5C,CAAA;QACA,IAAA,CAAKF,oBAAL,GAA4B,IAAK/D,CAAAA,OAAjC,CAPgD,CAAA,yDAAA;QAUhD,qNAAIsC,sBAAAA,AAAmB,EAACqH,UAAD,EAAa3C,UAAb,CAAvB,EAAiD;YAC/C,OAAA;QACD,CAAA;QAED,IAAA,CAAKlD,aAAL,GAAqB6F,UAArB,CAdgD,CAAA,uCAAA;QAiBhD,MAAMC,oBAAmC,GAAG;YAAEC,KAAK,EAAE,IAAA;SAArD,CAAA;QAEA,MAAMC,qBAAqB,GAAG,MAAe;YAC3C,IAAI,CAAC9C,UAAL,EAAiB;gBACf,OAAO,IAAP,CAAA;YACD,CAAA;YAED,MAAM,EAAE+C,mBAAAA,EAAF,GAA0B,IAAA,CAAK/J,OAArC,CAAA;YACA,MAAMgK,wBAAwB,GAC5B,OAAOD,mBAAP,KAA+B,UAA/B,GACIA,mBAAmB,EADvB,GAEIA,mBAHN,CAAA;YAKA,IACEC,wBAAwB,KAAK,KAA7B,IACC,CAACA,wBAAD,IAA6B,CAAC,IAAK/J,CAAAA,YAAL,CAAkBU,IAFnD,EAGE;gBACA,OAAO,IAAP,CAAA;YACD,CAAA;YAED,MAAMsJ,aAAa,GAAG,IAAI/J,GAAJ,CACpB8J,wBADoB,IAAA,IAAA,GACpBA,wBADoB,GACQ,IAAK/J,CAAAA,YADb,CAAtB,CAAA;YAIA,IAAI,IAAKD,CAAAA,OAAL,CAAakK,gBAAjB,EAAmC;gBACjCD,aAAa,CAACpF,GAAd,CAAkB,OAAlB,CAAA,CAAA;YACD,CAAA;YAED,OAAOR,MAAM,CAACC,IAAP,CAAY,IAAA,CAAKR,aAAjB,CAAgCqG,CAAAA,IAAhC,EAAsC3F,GAAD,IAAS;gBACnD,MAAM4F,QAAQ,GAAG5F,GAAjB,CAAA;gBACA,MAAM6F,OAAO,GAAG,IAAA,CAAKvG,aAAL,CAAmBsG,QAAnB,CAAiCpD,KAAAA,UAAU,CAACoD,QAAD,CAA3D,CAAA;gBACA,OAAOC,OAAO,IAAIJ,aAAa,CAACK,GAAd,CAAkBF,QAAlB,CAAlB,CAAA;YACD,CAJM,CAAP,CAAA;SA1BF,CAAA;QAiCA,IAAI,CAAAxI,aAAa,IAAb,IAAA,GAAA,KAAA,CAAA,GAAAA,aAAa,CAAElB,SAAf,MAA6B,KAA7B,IAAsCoJ,qBAAqB,EAA/D,EAAmE;YACjEF,oBAAoB,CAAClJ,SAArB,GAAiC,IAAjC,CAAA;QACD,CAAA;QAED,IAAA,CAAK8B,MAAL,CAAY;YAAE,GAAGoH,oBAAL;YAA2B,GAAGhI,aAAAA;SAA1C,CAAA,CAAA;IACD,CAAA;IAEOmB,WAAW,GAAS;QAC1B,MAAML,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CAAYwC,aAAZ,EAAA,CAA4BmB,KAA5B,CAAkC,IAAK3D,CAAAA,MAAvC,EAA+C,IAAA,CAAKC,OAApD,CAAd,CAAA;QAEA,IAAI0C,KAAK,KAAK,IAAK9B,CAAAA,YAAnB,EAAiC;YAC/B,OAAA;QACD,CAAA;QAED,MAAMkB,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;QAGA,IAAKA,CAAAA,YAAL,GAAoB8B,KAApB,CAAA;QACA,IAAA,CAAK2E,wBAAL,GAAgC3E,KAAK,CAACuB,KAAtC,CAAA;QACA,IAAKsD,CAAAA,mBAAL,GAA2B,IAAA,CAAKzD,aAAhC,CAAA;QAEA,IAAI,IAAA,CAAK5C,YAAL,EAAJ,EAAyB;YACvBY,SAAS,IAAA,IAAT,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAEH,cAAX,CAA0B,IAA1B,CAAA,CAAA;YACAe,KAAK,CAAC7B,WAAN,CAAkB,IAAlB,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAED0J,aAAa,CAACC,MAAD,EAAsC;QACjD,MAAM5I,aAA4B,GAAG,CAAA,CAArC,CAAA;QAEA,IAAI4I,MAAM,CAAC/H,IAAP,KAAgB,SAApB,EAA+B;YAC7Bb,aAAa,CAAC6I,SAAd,GAA0B,CAACD,MAAM,CAACE,MAAlC,CAAA;QACD,CAFD,MAEO,IAAIF,MAAM,CAAC/H,IAAP,KAAgB,OAAhB,IAA2B,EAACkI,qOAAAA,AAAgB,EAACH,MAAM,CAACnI,KAAR,CAAhD,EAAgE;YACrET,aAAa,CAACgJ,OAAd,GAAwB,IAAxB,CAAA;QACD,CAAA;QAED,IAAK1H,CAAAA,YAAL,CAAkBtB,aAAlB,CAAA,CAAA;QAEA,IAAI,IAAA,CAAKV,YAAL,EAAJ,EAAyB;YACvB,IAAA,CAAKF,YAAL,EAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEOwB,MAAM,CAACZ,aAAD,EAAqC;6NACjDiJ,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,4CAAA;YACA,IAAIlJ,aAAa,CAAC6I,SAAlB,EAA6B;gBAAA,IAAA,qBAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;gBAC3B,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKzK,OAAL,EAAayK,SAAb,KAAA,OAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,eAAyB,IAAK3G,CAAAA,aAAL,CAAmBwC,IAA5C,CAAA,CAAA;gBACA,CAAKtG,qBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAa+K,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EAAyB,IAAA,CAAKjH,aAAL,CAAmBwC,IAA5C,EAAmD,IAAnD,CAAA,CAAA;YACD,CAHD,MAGO,IAAI1E,aAAa,CAACgJ,OAAlB,EAA2B;gBAAA,IAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;gBAChC,CAAA,qBAAA,GAAA,CAAA,cAAA,GAAA,IAAA,CAAK5K,OAAL,EAAa4K,OAAb,KAAA,OAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,gBAAuB,IAAK9G,CAAAA,aAAL,CAAmBzB,KAA1C,CAAA,CAAA;gBACA,CAAKrC,sBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAa+K,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAyBjE,SAAzB,EAAoC,IAAA,CAAKhD,aAAL,CAAmBzB,KAAvD,CAAA,CAAA;YACD,CARuB,CAAA,6BAAA;YAWxB,IAAIT,aAAa,CAAClB,SAAlB,EAA6B;gBAC3B,IAAA,CAAKA,SAAL,CAAe6D,OAAf,CAAuB,CAAC,EAAEyG,QAAAA,EAAH,KAAkB;oBACvCA,QAAQ,CAAC,IAAKlH,CAAAA,aAAN,CAAR,CAAA;iBADF,CAAA,CAAA;YAGD,CAfuB,CAAA,2BAAA;YAkBxB,IAAIlC,aAAa,CAACiI,KAAlB,EAAyB;gBACvB,IAAA,CAAK9J,MAAL,CAAYwC,aAAZ,EAAA,CAA4BC,MAA5B,CAAmC;oBACjCE,KAAK,EAAE,IAAA,CAAK9B,YADqB;oBAEjC6B,IAAI,EAAE,wBAAA;iBAFR,CAAA,CAAA;YAID,CAAA;SAvBH,CAAA,CAAA;IAyBD,CAAA;AAjrB0D,CAAA;AAorB7D,SAASwI,iBAAT,CACEvI,KADF,EAEE1C,OAFF,EAGW;IACT,OACEA,OAAO,CAAC4C,OAAR,KAAoB,KAApB,IACA,CAACF,KAAK,CAACuB,KAAN,CAAYgC,aADb,IAEA,CAAA,CAAEvD,KAAK,CAACuB,KAAN,CAAYyD,MAAZ,KAAuB,OAAvB,IAAkC1H,OAAO,CAACkL,YAAR,KAAyB,KAA7D,CAHF,CAAA;AAKD,CAAA;AAED,SAASpK,kBAAT,CACE4B,KADF,EAEE1C,OAFF,EAGW;IACT,OACEiL,iBAAiB,CAACvI,KAAD,EAAQ1C,OAAR,CAAjB,IACC0C,KAAK,CAACuB,KAAN,CAAYgC,aAAZ,GAA4B,CAA5B,IACC5E,aAAa,CAACqB,KAAD,EAAQ1C,OAAR,EAAiBA,OAAO,CAACmL,cAAzB,CAHjB,CAAA;AAKD,CAAA;AAED,SAAS9J,aAAT,CACEqB,KADF,EAEE1C,OAFF,EAGEoL,KAHF,EAME;IACA,IAAIpL,OAAO,CAAC4C,OAAR,KAAoB,KAAxB,EAA+B;QAC7B,MAAMyI,KAAK,GAAG,OAAOD,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC1I,KAAD,CAAnC,GAA6C0I,KAA3D,CAAA;QAEA,OAAOC,KAAK,KAAK,QAAV,IAAuBA,KAAK,KAAK,KAAV,IAAmBxF,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAAxD,CAAA;IACD,CAAA;IACD,OAAO,KAAP,CAAA;AACD,CAAA;AAED,SAASiD,qBAAT,CACEP,KADF,EAEEZ,SAFF,EAGE9B,OAHF,EAIE6B,WAJF,EAKW;IACT,OACE7B,OAAO,CAAC4C,OAAR,KAAoB,KAApB,IAAA,CACCF,KAAK,KAAKZ,SAAV,IAAuBD,WAAW,CAACe,OAAZ,KAAwB,KADhD,CAEC,IAAA,CAAA,CAAC5C,OAAO,CAACsL,QAAT,IAAqB5I,KAAK,CAACuB,KAAN,CAAYyD,MAAZ,KAAuB,OAF7C,KAGA7B,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAJT,CAAA;AAMD,CAAA;AAED,SAAS6F,OAAT,CACEnD,KADF,EAEE1C,OAFF,EAGW;IACT,OAAO0C,KAAK,CAAC6I,aAAN,CAAoBvL,OAAO,CAACmD,SAA5B,CAAP,CAAA;AACD,EAAA,wEAAA;AAGD,iEAAA;AACA,SAASU,qCAAT,CAOElB,QAPF,EAQE6I,gBARF,EASExL,OATF,EAgBE;IACA,sEAAA;IACA,mCAAA;IACA,2EAAA;IACA,QAAA;IACA,0EAAA;IACA,oCAAA;IACA,IAAIA,OAAO,CAACkI,gBAAZ,EAA8B;QAC5B,OAAO,KAAP,CAAA;IACD,CATD,CAAA,4EAAA;IAYA,WAAA;IACA,IAAIlI,OAAO,CAACwI,eAAR,KAA4B1B,SAAhC,EAA2C;QACzC,gEAAA;QACA,6EAAA;QACA,qCAAA;QACA,OAAO0E,gBAAgB,CAAC5D,iBAAxB,CAAA;IACD,CAlBD,CAAA,6EAAA;IAqBA,mDAAA;IACA,IAAI,kNAACtF,sBAAAA,AAAmB,EAACK,QAAQ,CAACuB,gBAAT,EAAD,EAA8BsH,gBAA9B,CAAxB,EAAyE;QACvE,OAAO,IAAP,CAAA;IACD,CAxBD,CAAA,8DAAA;IA2BA,OAAO,KAAP,CAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "file": "logger.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/logger.ts"], "sourcesContent": ["export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n"], "names": ["defaultLogger", "console"], "mappings": ";;;AAQO,MAAMA,aAAqB,GAAGC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "file": "removable.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "names": ["Removable", "destroy", "clearGcTimeout", "scheduleGc", "isValidTimeout", "cacheTime", "gcTimeout", "setTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout", "undefined"], "mappings": ";;;;;AAEO,MAAeA,SAAf,CAAyB;IAI9BC,OAAO,GAAS;QACd,IAAA,CAAKC,cAAL,EAAA,CAAA;IACD,CAAA;IAESC,UAAU,GAAS;QAC3B,IAAA,CAAKD,cAAL,EAAA,CAAA;QAEA,qNAAIE,iBAAAA,AAAc,EAAC,IAAKC,CAAAA,SAAN,CAAlB,EAAoC;YAClC,IAAA,CAAKC,SAAL,GAAiBC,UAAU,CAAC,MAAM;gBAChC,IAAA,CAAKC,cAAL,EAAA,CAAA;aADyB,EAExB,IAAKH,CAAAA,SAFmB,CAA3B,CAAA;QAGD,CAAA;IACF,CAAA;IAESI,eAAe,CAACC,YAAD,EAAyC;QAChE,0EAAA;QACA,IAAKL,CAAAA,SAAL,GAAiBM,IAAI,CAACC,GAAL,CACf,IAAA,CAAKP,SAAL,IAAkB,CADH,EAEfK,YAFe,IAEfA,IAAAA,GAAAA,YAFe,gNAEEG,WAAQ,GAAGC,QAAH,GAAc,CAAI,GAAA,EAAJ,GAAS,IAFjC,CAAjB,CAAA;IAID,CAAA;IAESZ,cAAc,GAAG;QACzB,IAAI,IAAA,CAAKI,SAAT,EAAoB;YAClBS,YAAY,CAAC,IAAKT,CAAAA,SAAN,CAAZ,CAAA;YACA,IAAKA,CAAAA,SAAL,GAAiBU,SAAjB,CAAA;QACD,CAAA;IACF,CAAA;AA/B6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "file": "mutation.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/mutation.ts"], "sourcesContent": ["import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n"], "names": ["Mutation", "Removable", "constructor", "config", "defaultOptions", "mutationId", "mutationCache", "logger", "defaultLogger", "observers", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateCacheTime", "cacheTime", "meta", "setState", "dispatch", "type", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "retryer", "execute", "executeMutation", "createRetryer", "fn", "mutationFn", "Promise", "reject", "variables", "onFail", "failureCount", "error", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "promise", "restored", "onMutate", "context", "data", "onSuccess", "onSettled", "onError", "process", "env", "NODE_ENV", "undefined", "action", "reducer", "failureReason", "isPaused", "canFetch", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onMutationUpdate"], "mappings": ";;;;;;;;;;;;AAkFA,QAAA;AAEO,MAAMA,QAAN,0NAKGC,YALH,CAKa;IAWlBC,WAAW,CAACC,MAAD,CAA8D;QACvE,KAAA,EAAA,CAAA;QAEA,IAAA,CAAKC,cAAL,GAAsBD,MAAM,CAACC,cAA7B,CAAA;QACA,IAAA,CAAKC,UAAL,GAAkBF,MAAM,CAACE,UAAzB,CAAA;QACA,IAAA,CAAKC,aAAL,GAAqBH,MAAM,CAACG,aAA5B,CAAA;QACA,IAAA,CAAKC,MAAL,GAAcJ,MAAM,CAACI,MAAP,kNAAiBC,gBAA/B,CAAA;QACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;QACA,IAAA,CAAKC,KAAL,GAAaP,MAAM,CAACO,KAAP,IAAgBC,eAAe,EAA5C,CAAA;QAEA,IAAA,CAAKC,UAAL,CAAgBT,MAAM,CAACU,OAAvB,CAAA,CAAA;QACA,IAAA,CAAKC,UAAL,EAAA,CAAA;IACD,CAAA;IAEDF,UAAU,CACRC,OADQ,EAEF;QACN,IAAA,CAAKA,OAAL,GAAe;YAAE,GAAG,IAAA,CAAKT,cAAV;YAA0B,GAAGS,OAAAA;SAA5C,CAAA;QAEA,IAAA,CAAKE,eAAL,CAAqB,IAAKF,CAAAA,OAAL,CAAaG,SAAlC,CAAA,CAAA;IACD,CAAA;IAEO,IAAJC,IAAI,GAA6B;QACnC,OAAO,IAAA,CAAKJ,OAAL,CAAaI,IAApB,CAAA;IACD,CAAA;IAEDC,QAAQ,CAACR,KAAD,EAAkE;QACxE,IAAA,CAAKS,QAAL,CAAc;YAAEC,IAAI,EAAE,UAAR;YAAoBV,KAAAA;SAAlC,CAAA,CAAA;IACD,CAAA;IAEDW,WAAW,CAACC,QAAD,EAAuD;QAChE,IAAI,CAAC,IAAA,CAAKb,SAAL,CAAec,QAAf,CAAwBD,QAAxB,CAAL,EAAwC;YACtC,IAAA,CAAKb,SAAL,CAAee,IAAf,CAAoBF,QAApB,EADsC,CAAA,iDAAA;YAItC,IAAA,CAAKG,cAAL,EAAA,CAAA;YAEA,IAAKnB,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;gBACxBN,IAAI,EAAE,eADkB;gBAExBO,QAAQ,EAAE,IAFc;gBAGxBL,QAAAA;aAHF,CAAA,CAAA;QAKD,CAAA;IACF,CAAA;IAEDM,cAAc,CAACN,QAAD,EAAuD;QACnE,IAAA,CAAKb,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeoB,MAAf,EAAuBC,CAAD,GAAOA,CAAC,KAAKR,QAAnC,CAAjB,CAAA;QAEA,IAAA,CAAKR,UAAL,EAAA,CAAA;QAEA,IAAKR,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;YACxBN,IAAI,EAAE,iBADkB;YAExBO,QAAQ,EAAE,IAFc;YAGxBL,QAAAA;SAHF,CAAA,CAAA;IAKD,CAAA;IAESS,cAAc,GAAG;QACzB,IAAI,CAAC,IAAA,CAAKtB,SAAL,CAAeuB,MAApB,EAA4B;YAC1B,IAAI,IAAA,CAAKtB,KAAL,CAAWuB,MAAX,KAAsB,SAA1B,EAAqC;gBACnC,IAAA,CAAKnB,UAAL,EAAA,CAAA;YACD,CAFD,MAEO;gBACL,IAAA,CAAKR,aAAL,CAAmB4B,MAAnB,CAA0B,IAA1B,CAAA,CAAA;YACD,CAAA;QACF,CAAA;IACF,CAAA;IAEDC,QAAQ,GAAqB;QAAA,IAAA,qBAAA,EAAA,aAAA,CAAA;QAC3B,OAAO,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKC,OAAZ,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAcD,QAAd,EAAP,KAAA,IAAA,GAAA,qBAAA,GAAmC,IAAKE,CAAAA,OAAL,EAAnC,CAAA;IACD,CAAA;IAEY,MAAPA,OAAO,GAAmB;QAC9B,MAAMC,eAAe,GAAG,MAAM;YAAA,IAAA,mBAAA,CAAA;YAC5B,IAAKF,CAAAA,OAAL,GAAeG,mOAAAA,AAAa,EAAC;gBAC3BC,EAAE,EAAE,MAAM;oBACR,IAAI,CAAC,IAAA,CAAK3B,OAAL,CAAa4B,UAAlB,EAA8B;wBAC5B,OAAOC,OAAO,CAACC,MAAR,CAAe,qBAAf,CAAP,CAAA;oBACD,CAAA;oBACD,OAAO,IAAA,CAAK9B,OAAL,CAAa4B,UAAb,CAAwB,IAAK/B,CAAAA,KAAL,CAAWkC,SAAnC,CAAP,CAAA;iBALyB;gBAO3BC,MAAM,EAAE,CAACC,YAAD,EAAeC,KAAf,KAAyB;oBAC/B,IAAA,CAAK5B,QAAL,CAAc;wBAAEC,IAAI,EAAE,QAAR;wBAAkB0B,YAAlB;wBAAgCC,KAAAA;qBAA9C,CAAA,CAAA;iBARyB;gBAU3BC,OAAO,EAAE,MAAM;oBACb,IAAA,CAAK7B,QAAL,CAAc;wBAAEC,IAAI,EAAE,OAAA;qBAAtB,CAAA,CAAA;iBAXyB;gBAa3B6B,UAAU,EAAE,MAAM;oBAChB,IAAA,CAAK9B,QAAL,CAAc;wBAAEC,IAAI,EAAE,UAAA;qBAAtB,CAAA,CAAA;iBAdyB;gBAgB3B8B,KAAK,EAAA,CAAA,sBAAE,IAAKrC,CAAAA,OAAL,CAAaqC,KAAf,KAAA,OAAA,sBAAwB,CAhBF;gBAiB3BC,UAAU,EAAE,IAAA,CAAKtC,OAAL,CAAasC,UAjBE;gBAkB3BC,WAAW,EAAE,IAAKvC,CAAAA,OAAL,CAAauC,WAAAA;YAlBC,CAAD,CAA5B,CAAA;YAqBA,OAAO,IAAA,CAAKhB,OAAL,CAAaiB,OAApB,CAAA;SAtBF,CAAA;QAyBA,MAAMC,QAAQ,GAAG,IAAA,CAAK5C,KAAL,CAAWuB,MAAX,KAAsB,SAAvC,CAAA;QACA,IAAI;YAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;YACF,IAAI,CAACqB,QAAL,EAAe;gBAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,aAAA,CAAA;gBACb,IAAA,CAAKnC,QAAL,CAAc;oBAAEC,IAAI,EAAE,SAAR;oBAAmBwB,SAAS,EAAE,IAAK/B,CAAAA,OAAL,CAAa+B,SAAAA;gBAA3C,CAAd,EADa,CAAA,wBAAA;gBAGb,MAAA,CAAA,CAAA,qBAAA,GAAM,CAAKtC,sBAAAA,GAAAA,IAAAA,CAAAA,aAAL,CAAmBH,MAAnB,EAA0BoD,QAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,qBACJ,CAAA,IAAA,CAAA,sBAAA,EAAA,IAAA,CAAK7C,KAAL,CAAWkC,SADP,EAEJ,IAFI,CAAN,CAAA,CAAA;gBAIA,MAAMY,OAAO,GAAG,MAAA,CAAM,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAK3C,OAAL,EAAa0C,QAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,aAAA,EAAwB,IAAK7C,CAAAA,KAAL,CAAWkC,SAAnC,CAAN,CAAhB,CAAA;gBACA,IAAIY,OAAO,KAAK,IAAA,CAAK9C,KAAL,CAAW8C,OAA3B,EAAoC;oBAClC,IAAA,CAAKrC,QAAL,CAAc;wBACZC,IAAI,EAAE,SADM;wBAEZoC,OAFY;wBAGZZ,SAAS,EAAE,IAAKlC,CAAAA,KAAL,CAAWkC,SAAAA;qBAHxB,CAAA,CAAA;gBAKD,CAAA;YACF,CAAA;YACD,MAAMa,IAAI,GAAG,MAAMnB,eAAe,EAAlC,CAjBE,CAAA,wBAAA;YAoBF,MAAA,CAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKhC,aAAL,CAAmBH,MAAnB,EAA0BuD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,uBAAA,IAAA,CAAA,wBACJD,IADI,EAEJ,IAAA,CAAK/C,KAAL,CAAWkC,SAFP,EAGJ,IAAA,CAAKlC,KAAL,CAAW8C,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;YAOA,MAAA,CAAA,CAAA,qBAAA,GAAM,CAAA,iBAAA,IAAA,CAAK3C,OAAL,EAAa6C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJD,IADI,EAEJ,IAAA,CAAK/C,KAAL,CAAWkC,SAFP,EAGJ,IAAKlC,CAAAA,KAAL,CAAW8C,OAHP,CAAN,EA3BE,CAAA,wBAAA;YAkCF,MAAA,CAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKlD,aAAL,CAAmBH,MAAnB,EAA0BwD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJF,CAAAA,IAAAA,CAAAA,sBAAAA,EAAAA,IADI,EAEJ,IAFI,EAGJ,IAAK/C,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;YAQA,MAAA,CAAA,CAAA,qBAAA,GAAM,CAAA,iBAAA,IAAA,CAAK3C,OAAL,EAAa8C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJF,IADI,EAEJ,IAFI,EAGJ,IAAK/C,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,CAAN,CAAA,CAAA;YAOA,IAAA,CAAKrC,QAAL,CAAc;gBAAEC,IAAI,EAAE,SAAR;gBAAmBqC,IAAAA;aAAjC,CAAA,CAAA;YACA,OAAOA,IAAP,CAAA;SAlDF,CAmDE,OAAOV,KAAP,EAAc;YACd,IAAI;gBAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;gBACF,wBAAA;gBACA,MAAA,CAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKzC,aAAL,CAAmBH,MAAnB,EAA0ByD,OAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,uBAAA,IAAA,CAAA,wBACJb,KADI,EAEJ,IAAA,CAAKrC,KAAL,CAAWkC,SAFP,EAGJ,IAAA,CAAKlC,KAAL,CAAW8C,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;gBAOA,IAAIK,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;oBACE,IAAA,CAAKxD,MAAL,CAAYwC,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;gBACD,CAAA;gBAED,MAAA,CAAA,CAAA,qBAAA,GAAM,CAAA,iBAAA,IAAA,CAAKlC,OAAL,EAAa+C,OAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJb,KADI,EAEJ,IAAA,CAAKrC,KAAL,CAAWkC,SAFP,EAGJ,IAAKlC,CAAAA,KAAL,CAAW8C,OAHP,CAAN,EAbE,CAAA,wBAAA;gBAoBF,MAAA,CAAM,CAAA,sBAAA,GAAA,CAAA,uBAAA,GAAA,IAAA,CAAKlD,aAAL,CAAmBH,MAAnB,EAA0BwD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJK,CAAAA,IAAAA,CAAAA,uBAAAA,EAAAA,SADI,EAEJjB,KAFI,EAGJ,IAAKrC,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;gBAQA,MAAA,CAAA,CAAA,sBAAA,GAAM,CAAA,iBAAA,IAAA,CAAK3C,OAAL,EAAa8C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EACJK,SADI,EAEJjB,KAFI,EAGJ,IAAKrC,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,CAAN,CAAA,CAAA;gBAMA,MAAMT,KAAN,CAAA;YACD,CAnCD,QAmCU;gBACR,IAAA,CAAK5B,QAAL,CAAc;oBAAEC,IAAI,EAAE,OAAR;oBAAiB2B,KAAK,EAAEA,KAAAA;iBAAtC,CAAA,CAAA;YACD,CAAA;QACF,CAAA;IACF,CAAA;IAEO5B,QAAQ,CAAC8C,MAAD,EAA4D;QAC1E,MAAMC,OAAO,IACXxD,KADc,IAEyC;YACvD,OAAQuD,MAAM,CAAC7C,IAAf;gBACE,KAAK,QAAL;oBACE,OAAO;wBACL,GAAGV,KADE;wBAELoC,YAAY,EAAEmB,MAAM,CAACnB,YAFhB;wBAGLqB,aAAa,EAAEF,MAAM,CAAClB,KAAAA;qBAHxB,CAAA;gBAKF,KAAK,OAAL;oBACE,OAAO;wBACL,GAAGrC,KADE;wBAEL0D,QAAQ,EAAE,IAAA;qBAFZ,CAAA;gBAIF,KAAK,UAAL;oBACE,OAAO;wBACL,GAAG1D,KADE;wBAEL0D,QAAQ,EAAE,KAAA;qBAFZ,CAAA;gBAIF,KAAK,SAAL;oBACE,OAAO;wBACL,GAAG1D,KADE;wBAEL8C,OAAO,EAAES,MAAM,CAACT,OAFX;wBAGLC,IAAI,EAAEO,SAHD;wBAILlB,YAAY,EAAE,CAJT;wBAKLqB,aAAa,EAAE,IALV;wBAMLpB,KAAK,EAAE,IANF;wBAOLqB,QAAQ,EAAE,EAACC,6NAAAA,AAAQ,EAAC,IAAA,CAAKxD,OAAL,CAAauC,WAAd,CAPd;wBAQLnB,MAAM,EAAE,SARH;wBASLW,SAAS,EAAEqB,MAAM,CAACrB,SAAAA;qBATpB,CAAA;gBAWF,KAAK,SAAL;oBACE,OAAO;wBACL,GAAGlC,KADE;wBAEL+C,IAAI,EAAEQ,MAAM,CAACR,IAFR;wBAGLX,YAAY,EAAE,CAHT;wBAILqB,aAAa,EAAE,IAJV;wBAKLpB,KAAK,EAAE,IALF;wBAMLd,MAAM,EAAE,SANH;wBAOLmC,QAAQ,EAAE,KAAA;qBAPZ,CAAA;gBASF,KAAK,OAAL;oBACE,OAAO;wBACL,GAAG1D,KADE;wBAEL+C,IAAI,EAAEO,SAFD;wBAGLjB,KAAK,EAAEkB,MAAM,CAAClB,KAHT;wBAILD,YAAY,EAAEpC,KAAK,CAACoC,YAAN,GAAqB,CAJ9B;wBAKLqB,aAAa,EAAEF,MAAM,CAAClB,KALjB;wBAMLqB,QAAQ,EAAE,KANL;wBAOLnC,MAAM,EAAE,OAAA;qBAPV,CAAA;gBASF,KAAK,UAAL;oBACE,OAAO;wBACL,GAAGvB,KADE;wBAEL,GAAGuD,MAAM,CAACvD,KAAAA;qBAFZ,CAAA;YAlDJ,CAAA;SAHF,CAAA;QA2DA,IAAA,CAAKA,KAAL,GAAawD,OAAO,CAAC,IAAA,CAAKxD,KAAN,CAApB,CAAA;6NAEA4D,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAK9D,SAAL,CAAe+D,OAAf,EAAwBlD,QAAD,IAAc;gBACnCA,QAAQ,CAACmD,gBAAT,CAA0BR,MAA1B,CAAA,CAAA;aADF,CAAA,CAAA;YAGA,IAAK3D,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;gBACxBC,QAAQ,EAAE,IADc;gBAExBP,IAAI,EAAE,SAFkB;gBAGxB6C,MAAAA;aAHF,CAAA,CAAA;SAJF,CAAA,CAAA;IAUD,CAAA;AAlRiB,CAAA;AAqRb,SAAStD,eAAT,GAKiD;IACtD,OAAO;QACL6C,OAAO,EAAEQ,SADJ;QAELP,IAAI,EAAEO,SAFD;QAGLjB,KAAK,EAAE,IAHF;QAILD,YAAY,EAAE,CAJT;QAKLqB,aAAa,EAAE,IALV;QAMLC,QAAQ,EAAE,KANL;QAOLnC,MAAM,EAAE,MAPH;QAQLW,SAAS,EAAEoB,SAAAA;KARb,CAAA;AAUD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "file": "mutationObserver.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n"], "names": ["MutationObserver", "Subscribable", "constructor", "client", "options", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "notify", "type", "mutation", "currentMutation", "observer", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "listeners", "onSuccess", "onError", "getCurrentResult", "currentResult", "undefined", "variables", "mutateOptions", "build", "addObserver", "execute", "state", "getDefaultState", "isLoading", "status", "result", "isPending", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "data", "context", "onSettled", "error", "for<PERSON>ach", "listener"], "mappings": ";;;;;;;;;;;AAyBA,QAAA;AAEO,MAAMA,gBAAN,6NAKGC,eALH,CAOL;IAaAC,WAAW,CACTC,MADS,EAETC,OAFS,CAGT;QACA,KAAA,EAAA,CAAA;QAEA,IAAKD,CAAAA,MAAL,GAAcA,MAAd,CAAA;QACA,IAAKE,CAAAA,UAAL,CAAgBD,OAAhB,CAAA,CAAA;QACA,IAAA,CAAKE,WAAL,EAAA,CAAA;QACA,IAAA,CAAKC,YAAL,EAAA,CAAA;IACD,CAAA;IAESD,WAAW,GAAS;QAC5B,IAAKE,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAd,CAAA;QACA,IAAKC,CAAAA,KAAL,GAAa,IAAKA,CAAAA,KAAL,CAAWD,IAAX,CAAgB,IAAhB,CAAb,CAAA;IACD,CAAA;IAEDJ,UAAU,CACRD,OADQ,EAER;QAAA,IAAA,qBAAA,CAAA;QACA,MAAMO,WAAW,GAAG,IAAA,CAAKP,OAAzB,CAAA;QACA,IAAKA,CAAAA,OAAL,GAAe,IAAKD,CAAAA,MAAL,CAAYS,sBAAZ,CAAmCR,OAAnC,CAAf,CAAA;QACA,IAAI,kNAACS,sBAAAA,AAAmB,EAACF,WAAD,EAAc,IAAKP,CAAAA,OAAnB,CAAxB,EAAqD;YACnD,IAAA,CAAKD,MAAL,CAAYW,gBAAZ,EAAA,CAA+BC,MAA/B,CAAsC;gBACpCC,IAAI,EAAE,wBAD8B;gBAEpCC,QAAQ,EAAE,IAAA,CAAKC,eAFqB;gBAGpCC,QAAQ,EAAE,IAAA;aAHZ,CAAA,CAAA;QAKD,CAAA;QACD,CAAA,qBAAA,GAAA,IAAA,CAAKD,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAsBb,UAAtB,CAAiC,IAAA,CAAKD,OAAtC,CAAA,CAAA;IACD,CAAA;IAESgB,aAAa,GAAS;QAC9B,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;YAAA,IAAA,sBAAA,CAAA;YACxB,CAAA,sBAAA,GAAA,IAAA,CAAKH,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsBI,cAAtB,CAAqC,IAArC,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDC,gBAAgB,CAACC,MAAD,EAA4D;QAC1E,IAAKjB,CAAAA,YAAL,GAD0E,CAAA,uCAAA;QAI1E,MAAMkB,aAA4B,GAAG;YACnCC,SAAS,EAAE,IAAA;SADb,CAAA;QAIA,IAAIF,MAAM,CAACR,IAAP,KAAgB,SAApB,EAA+B;YAC7BS,aAAa,CAACE,SAAd,GAA0B,IAA1B,CAAA;QACD,CAFD,MAEO,IAAIH,MAAM,CAACR,IAAP,KAAgB,OAApB,EAA6B;YAClCS,aAAa,CAACG,OAAd,GAAwB,IAAxB,CAAA;QACD,CAAA;QAED,IAAKb,CAAAA,MAAL,CAAYU,aAAZ,CAAA,CAAA;IACD,CAAA;IAEDI,gBAAgB,GAKd;QACA,OAAO,IAAA,CAAKC,aAAZ,CAAA;IACD,CAAA;IAEDpB,KAAK,GAAS;QACZ,IAAKQ,CAAAA,eAAL,GAAuBa,SAAvB,CAAA;QACA,IAAA,CAAKxB,YAAL,EAAA,CAAA;QACA,IAAA,CAAKQ,MAAL,CAAY;YAAEW,SAAS,EAAE,IAAA;SAAzB,CAAA,CAAA;IACD,CAAA;IAEDlB,MAAM,CACJwB,SADI,EAEJ5B,OAFI,EAGY;QAChB,IAAK6B,CAAAA,aAAL,GAAqB7B,OAArB,CAAA;QAEA,IAAI,IAAA,CAAKc,eAAT,EAA0B;YACxB,IAAA,CAAKA,eAAL,CAAqBI,cAArB,CAAoC,IAApC,CAAA,CAAA;QACD,CAAA;QAED,IAAA,CAAKJ,eAAL,GAAuB,IAAKf,CAAAA,MAAL,CAAYW,gBAAZ,EAAA,CAA+BoB,KAA/B,CAAqC,IAAA,CAAK/B,MAA1C,EAAkD;YACvE,GAAG,IAAA,CAAKC,OAD+D;YAEvE4B,SAAS,EACP,OAAOA,SAAP,KAAqB,WAArB,GAAmCA,SAAnC,GAA+C,IAAK5B,CAAAA,OAAL,CAAa4B,SAAAA;QAHS,CAAlD,CAAvB,CAAA;QAMA,IAAA,CAAKd,eAAL,CAAqBiB,WAArB,CAAiC,IAAjC,CAAA,CAAA;QAEA,OAAO,IAAKjB,CAAAA,eAAL,CAAqBkB,OAArB,EAAP,CAAA;IACD,CAAA;IAEO7B,YAAY,GAAS;QAC3B,MAAM8B,KAAK,GAAG,IAAA,CAAKnB,eAAL,GACV,IAAKA,CAAAA,eAAL,CAAqBmB,KADX,uNAEVC,kBAAAA,AAAe,EAFnB,CAAA;QAIA,MAAMC,SAAS,GAAGF,KAAK,CAACG,MAAN,KAAiB,SAAnC,CAAA;QACA,MAAMC,MAKL,GAAG;YACF,GAAGJ,KADD;YAEFE,SAFE;YAGFG,SAAS,EAAEH,SAHT;YAIFI,SAAS,EAAEN,KAAK,CAACG,MAAN,KAAiB,SAJ1B;YAKFI,OAAO,EAAEP,KAAK,CAACG,MAAN,KAAiB,OALxB;YAMFK,MAAM,EAAER,KAAK,CAACG,MAAN,KAAiB,MANvB;YAOFhC,MAAM,EAAE,IAAA,CAAKA,MAPX;YAQFE,KAAK,EAAE,IAAKA,CAAAA,KAAAA;SAbd,CAAA;QAgBA,IAAKoB,CAAAA,aAAL,GAAqBW,MAArB,CAAA;IAMD,CAAA;IAEO1B,MAAM,CAACX,OAAD,EAAyB;QACrC0C,qOAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,qCAAA;YACA,IAAI,IAAA,CAAKd,aAAL,IAAsB,IAAKZ,CAAAA,YAAL,EAA1B,EAA+C;gBAC7C,IAAIjB,OAAO,CAACuB,SAAZ,EAAuB;oBAAA,IAAA,qBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;oBACrB,CAAA,qBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKM,aAAL,EAAmBN,SAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,IAAA,CAAKG,aAAL,CAAmBkB,IADrB,EAEE,IAAA,CAAKlB,aAAL,CAAmBE,SAFrB,EAGE,IAAKF,CAAAA,aAAL,CAAmBmB,OAHrB,CAAA,CAAA;oBAKA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKhB,aAAL,EAAmBiB,SAAnB,KAAA,OAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,sBACE,IAAKpB,CAAAA,aAAL,CAAmBkB,IADrB,EAEE,IAFF,EAGE,IAAA,CAAKlB,aAAL,CAAmBE,SAHrB,EAIE,IAAKF,CAAAA,aAAL,CAAmBmB,OAJrB,CAAA,CAAA;gBAMD,CAZD,MAYO,IAAI7C,OAAO,CAACwB,OAAZ,EAAqB;oBAAA,IAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;oBAC1B,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKK,aAAL,EAAmBL,OAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,oBAAA,EAAA,IAAA,CAAKE,aAAL,CAAmBqB,KADrB,EAEE,IAAA,CAAKrB,aAAL,CAAmBE,SAFrB,EAGE,IAAKF,CAAAA,aAAL,CAAmBmB,OAHrB,CAAA,CAAA;oBAKA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKhB,aAAL,EAAmBiB,SAAnB,KAAA,OAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,sBACEnB,SADF,EAEE,IAAA,CAAKD,aAAL,CAAmBqB,KAFrB,EAGE,IAAA,CAAKrB,aAAL,CAAmBE,SAHrB,EAIE,IAAKF,CAAAA,aAAL,CAAmBmB,OAJrB,CAAA,CAAA;gBAMD,CAAA;YACF,CA5BuB,CAAA,6BAAA;YA+BxB,IAAI7C,OAAO,CAACsB,SAAZ,EAAuB;gBACrB,IAAA,CAAKA,SAAL,CAAe0B,OAAf,CAAuB,CAAC,EAAEC,QAAAA,EAAH,KAAkB;oBACvCA,QAAQ,CAAC,IAAKvB,CAAAA,aAAN,CAAR,CAAA;iBADF,CAAA,CAAA;YAGD,CAAA;SAnCH,CAAA,CAAA;IAqCD,CAAA;AA5KD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "file": "infiniteQueryBehavior.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/infiniteQueryBehavior.ts"], "sourcesContent": ["import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n"], "names": ["infiniteQueryBehavior", "onFetch", "context", "fetchFn", "refetchPage", "fetchOptions", "meta", "fetchMore", "pageParam", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "state", "data", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "aborted", "addEventListener", "queryFn", "options", "Promise", "reject", "queryHash", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "length", "resolve", "queryFnContext", "query<PERSON><PERSON>", "queryFnResult", "promise", "then", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "i", "shouldFetchNextPage", "finalPromise", "hasNextPage", "Array", "isArray", "nextPageParam", "hasPreviousPage", "previousPageParam"], "mappings": ";;;;;;;AASO,SAASA,qBAAT,GAIuD;IAC5D,OAAO;QACLC,OAAO,GAAGC,OAAD,IAAa;YACpBA,OAAO,CAACC,OAAR,GAAkB,MAAM;gBAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,oBAAA,CAAA;gBACtB,MAAMC,WAA2D,GAC/DF,CAAAA,qBAAAA,GAAAA,OAAO,CAACG,YADuD,KAAA,OAAA,KAAA,IAAA,CAAA,yBAC/D,qBAAsBC,CAAAA,IADyC,KAC/D,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4BF,WAD9B,CAAA;gBAEA,MAAMG,SAAS,GAAGL,CAAAA,sBAAAA,GAAAA,OAAO,CAACG,YAAX,KAAA,OAAA,KAAA,IAAA,CAAA,yBAAG,sBAAsBC,CAAAA,IAAzB,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4BC,SAA9C,CAAA;gBACA,MAAMC,SAAS,GAAGD,SAAH,IAAGA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAEC,SAA7B,CAAA;gBACA,MAAMC,kBAAkB,GAAG,CAAAF,SAAS,IAAA,IAAT,GAAA,KAAA,IAAAA,SAAS,CAAEG,SAAX,MAAyB,SAApD,CAAA;gBACA,MAAMC,sBAAsB,GAAG,CAAAJ,SAAS,IAAA,IAAT,GAAA,KAAA,IAAAA,SAAS,CAAEG,SAAX,MAAyB,UAAxD,CAAA;gBACA,MAAME,QAAQ,GAAG,CAAA,CAAA,mBAAA,GAAAV,OAAO,CAACW,KAAR,CAAcC,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,mBAAA,CAAoBC,KAApB,KAA6B,EAA9C,CAAA;gBACA,MAAMC,aAAa,GAAG,CAAA,CAAA,oBAAA,GAAAd,OAAO,CAACW,KAAR,CAAcC,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,oBAAA,CAAoBG,UAApB,KAAkC,EAAxD,CAAA;gBACA,IAAIC,aAAa,GAAGF,aAApB,CAAA;gBACA,IAAIG,SAAS,GAAG,KAAhB,CAAA;gBAEA,MAAMC,iBAAiB,IAAIC,MAAD,IAAqB;oBAC7CC,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;wBACtCG,UAAU,EAAE,IAD0B;wBAEtCC,GAAG,EAAE,MAAM;4BAAA,IAAA,eAAA,CAAA;4BACT,IAAA,CAAA,eAAA,GAAIvB,OAAO,CAACwB,MAAZ,KAAI,IAAA,IAAA,eAAA,CAAgBC,OAApB,EAA6B;gCAC3BR,SAAS,GAAG,IAAZ,CAAA;4BACD,CAFD,MAEO;gCAAA,IAAA,gBAAA,CAAA;gCACL,CAAAjB,gBAAAA,GAAAA,OAAO,CAACwB,MAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAgBE,gBAAhB,CAAiC,OAAjC,EAA0C,MAAM;oCAC9CT,SAAS,GAAG,IAAZ,CAAA;iCADF,CAAA,CAAA;4BAGD,CAAA;4BACD,OAAOjB,OAAO,CAACwB,MAAf,CAAA;wBACD,CAAA;qBAXH,CAAA,CAAA;gBAaD,CAdD,CAZsB,CAAA,qBAAA;gBA6BtB,MAAMG,OAAO,GACX3B,OAAO,CAAC4B,OAAR,CAAgBD,OAAhB,IAAA,CACC,IACCE,OAAO,CAACC,MAAR,CAAA,mCACmC9B,OAAO,CAAC4B,OAAR,CAAgBG,SADnD,GAAA,IAFF,CADF,CAAA;gBAOA,MAAMC,aAAa,GAAG,CACpBnB,KADoB,EAEpBoB,KAFoB,EAGpBC,IAHoB,EAIpBC,QAJoB,KAKjB;oBACHnB,aAAa,GAAGmB,QAAQ,GACpB;wBAACF,KAAD,EAAQ;2BAAGjB,aAAX;qBADoB,GAEpB,CAAC;2BAAGA,aAAJ;wBAAmBiB,KAAnB;qBAFJ,CAAA;oBAGA,OAAOE,QAAQ,GAAG;wBAACD,IAAD,EAAO;2BAAGrB,KAAV;qBAAH,GAAsB,CAAC;2BAAGA,KAAJ;wBAAWqB,IAAX;qBAArC,CAAA;gBACD,CAVD,CApCsB,CAAA,kCAAA;gBAiDtB,MAAME,SAAS,GAAG,CAChBvB,KADgB,EAEhBwB,MAFgB,EAGhBJ,KAHgB,EAIhBE,QAJgB,KAKO;oBACvB,IAAIlB,SAAJ,EAAe;wBACb,OAAOY,OAAO,CAACC,MAAR,CAAe,WAAf,CAAP,CAAA;oBACD,CAAA;oBAED,IAAI,OAAOG,KAAP,KAAiB,WAAjB,IAAgC,CAACI,MAAjC,IAA2CxB,KAAK,CAACyB,MAArD,EAA6D;wBAC3D,OAAOT,OAAO,CAACU,OAAR,CAAgB1B,KAAhB,CAAP,CAAA;oBACD,CAAA;oBAED,MAAM2B,cAAoC,GAAG;wBAC3CC,QAAQ,EAAEzC,OAAO,CAACyC,QADyB;wBAE3CnC,SAAS,EAAE2B,KAFgC;wBAG3C7B,IAAI,EAAEJ,OAAO,CAAC4B,OAAR,CAAgBxB,IAAAA;qBAHxB,CAAA;oBAMAc,iBAAiB,CAACsB,cAAD,CAAjB,CAAA;oBAEA,MAAME,aAAa,GAAGf,OAAO,CAACa,cAAD,CAA7B,CAAA;oBAEA,MAAMG,OAAO,GAAGd,OAAO,CAACU,OAAR,CAAgBG,aAAhB,CAA+BE,CAAAA,IAA/B,EAAqCV,IAAD,GAClDF,aAAa,CAACnB,KAAD,EAAQoB,KAAR,EAAeC,IAAf,EAAqBC,QAArB,CADC,CAAhB,CAAA;oBAIA,OAAOQ,OAAP,CAAA;iBA5BF,CAAA;gBA+BA,IAAIA,OAAJ,CAhFsB,CAAA,oBAAA;gBAmFtB,IAAI,CAACjC,QAAQ,CAAC4B,MAAd,EAAsB;oBACpBK,OAAO,GAAGP,SAAS,CAAC,EAAD,CAAnB,CAAA;gBACD,CAFD,MAKK,IAAI7B,kBAAJ,EAAwB;oBAC3B,MAAM8B,MAAM,GAAG,OAAO/B,SAAP,KAAqB,WAApC,CAAA;oBACA,MAAM2B,KAAK,GAAGI,MAAM,GAChB/B,SADgB,GAEhBuC,gBAAgB,CAAC7C,OAAO,CAAC4B,OAAT,EAAkBlB,QAAlB,CAFpB,CAAA;oBAGAiC,OAAO,GAAGP,SAAS,CAAC1B,QAAD,EAAW2B,MAAX,EAAmBJ,KAAnB,CAAnB,CAAA;gBACD,CANI,MASA,IAAIxB,sBAAJ,EAA4B;oBAC/B,MAAM4B,MAAM,GAAG,OAAO/B,SAAP,KAAqB,WAApC,CAAA;oBACA,MAAM2B,KAAK,GAAGI,MAAM,GAChB/B,SADgB,GAEhBwC,oBAAoB,CAAC9C,OAAO,CAAC4B,OAAT,EAAkBlB,QAAlB,CAFxB,CAAA;oBAGAiC,OAAO,GAAGP,SAAS,CAAC1B,QAAD,EAAW2B,MAAX,EAAmBJ,KAAnB,EAA0B,IAA1B,CAAnB,CAAA;gBACD,CANI,MASA;oBACHjB,aAAa,GAAG,EAAhB,CAAA;oBAEA,MAAMqB,MAAM,GAAG,OAAOrC,OAAO,CAAC4B,OAAR,CAAgBiB,gBAAvB,KAA4C,WAA3D,CAAA;oBAEA,MAAME,oBAAoB,GACxB7C,WAAW,IAAIQ,QAAQ,CAAC,CAAD,CAAvB,GACIR,WAAW,CAACQ,QAAQ,CAAC,CAAD,CAAT,EAAc,CAAd,EAAiBA,QAAjB,CADf,GAEI,IAHN,CALG,CAAA,mBAAA;oBAWHiC,OAAO,GAAGI,oBAAoB,GAC1BX,SAAS,CAAC,EAAD,EAAKC,MAAL,EAAavB,aAAa,CAAC,CAAD,CAA1B,CADiB,GAE1Be,OAAO,CAACU,OAAR,CAAgBP,aAAa,CAAC,EAAD,EAAKlB,aAAa,CAAC,CAAD,CAAlB,EAAuBJ,QAAQ,CAAC,CAAD,CAA/B,CAA7B,CAFJ,CAXG,CAAA,wBAAA;oBAgBH,IAAK,IAAIsC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtC,QAAQ,CAAC4B,MAA7B,EAAqCU,CAAC,EAAtC,CAA0C;wBACxCL,OAAO,GAAGA,OAAO,CAACC,IAAR,EAAc/B,KAAD,IAAW;4BAChC,MAAMoC,mBAAmB,GACvB/C,WAAW,IAAIQ,QAAQ,CAACsC,CAAD,CAAvB,GACI9C,WAAW,CAACQ,QAAQ,CAACsC,CAAD,CAAT,EAAcA,CAAd,EAAiBtC,QAAjB,CADf,GAEI,IAHN,CAAA;4BAKA,IAAIuC,mBAAJ,EAAyB;gCACvB,MAAMhB,KAAK,GAAGI,MAAM,GAChBvB,aAAa,CAACkC,CAAD,CADG,GAEhBH,gBAAgB,CAAC7C,OAAO,CAAC4B,OAAT,EAAkBf,KAAlB,CAFpB,CAAA;gCAGA,OAAOuB,SAAS,CAACvB,KAAD,EAAQwB,MAAR,EAAgBJ,KAAhB,CAAhB,CAAA;4BACD,CAAA;4BACD,OAAOJ,OAAO,CAACU,OAAR,CACLP,aAAa,CAACnB,KAAD,EAAQC,aAAa,CAACkC,CAAD,CAArB,EAA0BtC,QAAQ,CAACsC,CAAD,CAAlC,CADR,CAAP,CAAA;wBAGD,CAfS,CAAV,CAAA;oBAgBD,CAAA;gBACF,CAAA;gBAED,MAAME,YAAY,GAAGP,OAAO,CAACC,IAAR,EAAc/B,KAAD,GAAA,CAAY;wBAC5CA,KAD4C;wBAE5CE,UAAU,EAAEC,aAAAA;oBAFgC,CAAZ,CAAb,CAArB,CAAA;gBAKA,OAAOkC,YAAP,CAAA;aAnJF,CAAA;QAqJD,CAAA;KAvJH,CAAA;AAyJD,CAAA;AAEM,SAASL,gBAAT,CACLjB,OADK,EAELf,KAFK,EAGgB;IACrB,OAAOe,OAAO,CAACiB,gBAAf,IAAA,OAAA,KAAA,IAAOjB,OAAO,CAACiB,gBAAR,CAA2BhC,KAAK,CAACA,KAAK,CAACyB,MAAN,GAAe,CAAhB,CAAhC,EAAoDzB,KAApD,CAAP,CAAA;AACD,CAAA;AAEM,SAASiC,oBAAT,CACLlB,OADK,EAELf,KAFK,EAGgB;IACrB,OAAOe,OAAO,CAACkB,oBAAf,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOlB,OAAO,CAACkB,oBAAR,CAA+BjC,KAAK,CAAC,CAAD,CAApC,EAAyCA,KAAzC,CAAP,CAAA;AACD,CAAA;AAED;;;CAGA,GACO,SAASsC,WAAT,CACLvB,OADK,EAELf,KAFK,EAGgB;IACrB,IAAIe,OAAO,CAACiB,gBAAR,IAA4BO,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAAhC,EAAsD;QACpD,MAAMyC,aAAa,GAAGT,gBAAgB,CAACjB,OAAD,EAAUf,KAAV,CAAtC,CAAA;QACA,OACE,OAAOyC,aAAP,KAAyB,WAAzB,IACAA,aAAa,KAAK,IADlB,IAEAA,aAAa,KAAK,KAHpB,CAAA;IAKD,CAAA;IACD,OAAA;AACD,CAAA;AAED;;;CAGA,GACO,SAASC,eAAT,CACL3B,OADK,EAELf,KAFK,EAGgB;IACrB,IAAIe,OAAO,CAACkB,oBAAR,IAAgCM,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAApC,EAA0D;QACxD,MAAM2C,iBAAiB,GAAGV,oBAAoB,CAAClB,OAAD,EAAUf,KAAV,CAA9C,CAAA;QACA,OACE,OAAO2C,iBAAP,KAA6B,WAA7B,IACAA,iBAAiB,KAAK,IADtB,IAEAA,iBAAiB,KAAK,KAHxB,CAAA;IAKD,CAAA;IACD,OAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1790, "column": 0}, "map": {"version": 3, "file": "infiniteQueryObserver.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/infiniteQueryObserver.ts"], "sourcesContent": ["import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryK<PERSON>,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryK<PERSON>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n"], "names": ["InfiniteQueryObserver", "QueryObserver", "constructor", "client", "options", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "infiniteQueryBehavior", "getOptimisticResult", "pageParam", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "state", "result", "isFetching", "isRefetching", "isFetchingNextPage", "fetchMeta", "isFetchingPreviousPage", "hasNextPage", "data", "pages", "hasPreviousPage"], "mappings": ";;;;;;;AAuBO,MAAMA,qBAAN,8NAMGC,gBANH,CAYL;IACA,gBAAA;IAKA,gBAAA;IAGA,gBAAA;IAKA,qEAAA;IACAC,WAAW,CACTC,MADS,EAETC,OAFS,CAST;QACA,KAAMD,CAAAA,MAAN,EAAcC,OAAd,CAAA,CAAA;IACD,CAAA;IAESC,WAAW,GAAS;QAC5B,KAAA,CAAMA,WAAN,EAAA,CAAA;QACA,IAAKC,CAAAA,aAAL,GAAqB,IAAKA,CAAAA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB,CAAA;QACA,IAAKC,CAAAA,iBAAL,GAAyB,IAAKA,CAAAA,iBAAL,CAAuBD,IAAvB,CAA4B,IAA5B,CAAzB,CAAA;IACD,CAAA;IAEDE,UAAU,CACRL,OADQ,EAQRM,aARQ,EASF;QACN,KAAA,CAAMD,UAAN,CACE;YACE,GAAGL,OADL;YAEEO,QAAQ,EAAEC,yPAAAA,AAAqB,EAAA;QAFjC,CADF,EAKEF,aALF,CAAA,CAAA;IAOD,CAAA;IAEDG,mBAAmB,CACjBT,OADiB,EAQ2B;QAC5CA,OAAO,CAACO,QAAR,oOAAmBC,wBAAAA,AAAqB,EAAxC,CAAA;QACA,OAAO,KAAMC,CAAAA,mBAAN,CAA0BT,OAA1B,CAAP,CAAA;IAID,CAAA;IAEDE,aAAa,CAAC,EAAEQ,SAAF,EAAa,GAAGV,OAAAA,EAAhB,GAAkD,CAAA,CAAnD,EAEX;QACA,OAAO,IAAKW,CAAAA,KAAL,CAAW;YAChB,GAAGX,OADa;YAEhBY,IAAI,EAAE;gBACJC,SAAS,EAAE;oBAAEC,SAAS,EAAE,SAAb;oBAAwBJ,SAAAA;gBAAxB,CAAA;YADP,CAAA;QAFU,CAAX,CAAP,CAAA;IAMD,CAAA;IAEDN,iBAAiB,CAAC,EAChBM,SADgB,EAEhB,GAAGV,OAAAA,EAFa,GAGY,CAAA,CAHb,EAKf;QACA,OAAO,IAAKW,CAAAA,KAAL,CAAW;YAChB,GAAGX,OADa;YAEhBY,IAAI,EAAE;gBACJC,SAAS,EAAE;oBAAEC,SAAS,EAAE,UAAb;oBAAyBJ,SAAAA;gBAAzB,CAAA;YADP,CAAA;QAFU,CAAX,CAAP,CAAA;IAMD,CAAA;IAESK,YAAY,CACpBC,KADoB,EAEpBhB,OAFoB,EASwB;QAAA,IAAA,gBAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,YAAA,CAAA;QAC5C,MAAM,EAAEiB,KAAAA,EAAF,GAAYD,KAAlB,CAAA;QACA,MAAME,MAAM,GAAG,KAAMH,CAAAA,YAAN,CAAmBC,KAAnB,EAA0BhB,OAA1B,CAAf,CAAA;QAEA,MAAM,EAAEmB,UAAF,EAAcC,YAAAA,EAAd,GAA+BF,MAArC,CAAA;QAEA,MAAMG,kBAAkB,GACtBF,UAAU,IAAI,CAAAF,CAAAA,gBAAAA,GAAAA,KAAK,CAACK,SAAN,KAAiBT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,SAAjB,KAA4BC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,SAD1D,CAAA;QAGA,MAAMS,sBAAsB,GAC1BJ,UAAU,IAAI,CAAAF,CAAAA,iBAAAA,GAAAA,KAAK,CAACK,SAAN,KAAiBT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,SAAjB,KAA4BC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,UAD1D,CAAA;QAGA,OAAO;YACL,GAAGI,MADE;YAELhB,aAAa,EAAE,IAAA,CAAKA,aAFf;YAGLE,iBAAiB,EAAE,IAAA,CAAKA,iBAHnB;YAILoB,WAAW,mOAAEA,cAAAA,AAAW,EAACxB,OAAD,EAAA,CAAA,WAAA,GAAUiB,KAAK,CAACQ,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,WAAYC,CAAAA,KAAtB,CAJnB;YAKLC,eAAe,mOAAEA,kBAAAA,AAAe,EAAC3B,OAAD,EAAA,CAAA,YAAA,GAAUiB,KAAK,CAACQ,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,YAAYC,CAAAA,KAAtB,CAL3B;YAMLL,kBANK;YAOLE,sBAPK;YAQLH,YAAY,EACVA,YAAY,IAAI,CAACC,kBAAjB,IAAuC,CAACE,sBAAAA;SAT5C,CAAA;IAWD,CAAA;AA9HD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "file": "query.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/query.ts"], "sourcesContent": ["import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n"], "names": ["Query", "Removable", "constructor", "config", "abortSignalConsumed", "defaultOptions", "setOptions", "options", "observers", "cache", "logger", "defaultLogger", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "scheduleGc", "meta", "updateCacheTime", "cacheTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "replaceData", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "promise", "retryer", "then", "noop", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "timeUntilStale", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "queryFn", "process", "env", "NODE_ENV", "Array", "isArray", "error", "abortController", "getAbortController", "queryFnContext", "pageParam", "undefined", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "revertState", "fetchMeta", "onError", "isCancelledError", "onSettled", "isFetchingOptimistic", "createRetryer", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "canFetch", "status", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt"], "mappings": ";;;;;;;;;;;;;AA0IA,QAAA;AAEO,MAAMA,KAAN,0NAKGC,YALH,CAKa;IAiBlBC,WAAW,CAACC,MAAD,CAA8D;QACvE,KAAA,EAAA,CAAA;QAEA,IAAKC,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;QACA,IAAA,CAAKC,cAAL,GAAsBF,MAAM,CAACE,cAA7B,CAAA;QACA,IAAA,CAAKC,UAAL,CAAgBH,MAAM,CAACI,OAAvB,CAAA,CAAA;QACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;QACA,IAAA,CAAKC,KAAL,GAAaN,MAAM,CAACM,KAApB,CAAA;QACA,IAAA,CAAKC,MAAL,GAAcP,MAAM,CAACO,MAAP,kNAAiBC,gBAA/B,CAAA;QACA,IAAA,CAAKC,QAAL,GAAgBT,MAAM,CAACS,QAAvB,CAAA;QACA,IAAA,CAAKC,SAAL,GAAiBV,MAAM,CAACU,SAAxB,CAAA;QACA,IAAKC,CAAAA,YAAL,GAAoBX,MAAM,CAACY,KAAP,IAAgBC,eAAe,CAAC,IAAKT,CAAAA,OAAN,CAAnD,CAAA;QACA,IAAKQ,CAAAA,KAAL,GAAa,IAAA,CAAKD,YAAlB,CAAA;QACA,IAAA,CAAKG,UAAL,EAAA,CAAA;IACD,CAAA;IAEO,IAAJC,IAAI,GAA0B;QAChC,OAAO,IAAA,CAAKX,OAAL,CAAaW,IAApB,CAAA;IACD,CAAA;IAEOZ,UAAU,CAChBC,OADgB,EAEV;QACN,IAAA,CAAKA,OAAL,GAAe;YAAE,GAAG,IAAA,CAAKF,cAAV;YAA0B,GAAGE,OAAAA;SAA5C,CAAA;QAEA,IAAA,CAAKY,eAAL,CAAqB,IAAKZ,CAAAA,OAAL,CAAaa,SAAlC,CAAA,CAAA;IACD,CAAA;IAESC,cAAc,GAAG;QACzB,IAAI,CAAC,IAAA,CAAKb,SAAL,CAAec,MAAhB,IAA0B,IAAKP,CAAAA,KAAL,CAAWQ,WAAX,KAA2B,MAAzD,EAAiE;YAC/D,IAAA,CAAKd,KAAL,CAAWe,MAAX,CAAkB,IAAlB,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDC,OAAO,CACLC,OADK,EAELnB,OAFK,EAGE;QACP,MAAMoB,IAAI,IAAGC,8NAAAA,AAAW,EAAC,IAAA,CAAKb,KAAL,CAAWY,IAAZ,EAAkBD,OAAlB,EAA2B,IAAA,CAAKnB,OAAhC,CAAxB,CADO,CAAA,iCAAA;QAIP,IAAA,CAAKsB,QAAL,CAAc;YACZF,IADY;YAEZG,IAAI,EAAE,SAFM;YAGZC,aAAa,EAAExB,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEyB,SAHZ;YAIZC,MAAM,EAAE1B,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE0B,MAAAA;SAJnB,CAAA,CAAA;QAOA,OAAON,IAAP,CAAA;IACD,CAAA;IAEDO,QAAQ,CACNnB,KADM,EAENoB,eAFM,EAGA;QACN,IAAA,CAAKN,QAAL,CAAc;YAAEC,IAAI,EAAE,UAAR;YAAoBf,KAApB;YAA2BoB,eAAAA;SAAzC,CAAA,CAAA;IACD,CAAA;IAEDC,MAAM,CAAC7B,OAAD,EAAyC;QAAA,IAAA,aAAA,CAAA;QAC7C,MAAM8B,OAAO,GAAG,IAAA,CAAKA,OAArB,CAAA;QACA,CAAA,aAAA,GAAA,IAAA,CAAKC,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAcF,MAAd,CAAqB7B,OAArB,CAAA,CAAA;QACA,OAAO8B,OAAO,GAAGA,OAAO,CAACE,IAAR,8MAAaC,OAAb,CAAA,CAAmBC,KAAnB,8MAAyBD,OAAzB,CAAH,GAAoCE,OAAO,CAACC,OAAR,EAAlD,CAAA;IACD,CAAA;IAEDC,OAAO,GAAS;QACd,KAAA,CAAMA,OAAN,EAAA,CAAA;QAEA,IAAA,CAAKR,MAAL,CAAY;YAAES,MAAM,EAAE,IAAA;SAAtB,CAAA,CAAA;IACD,CAAA;IAEDC,KAAK,GAAS;QACZ,IAAA,CAAKF,OAAL,EAAA,CAAA;QACA,IAAKV,CAAAA,QAAL,CAAc,IAAA,CAAKpB,YAAnB,CAAA,CAAA;IACD,CAAA;IAEDiC,QAAQ,GAAY;QAClB,OAAO,IAAKvC,CAAAA,SAAL,CAAewC,IAAf,EAAqBC,QAAD,GAAcA,QAAQ,CAAC1C,OAAT,CAAiB2C,OAAjB,KAA6B,KAA/D,CAAP,CAAA;IACD,CAAA;IAEDC,UAAU,GAAY;QACpB,OAAO,IAAA,CAAKC,iBAAL,EAA2B,GAAA,CAA3B,IAAgC,CAAC,IAAA,CAAKL,QAAL,EAAxC,CAAA;IACD,CAAA;IAEDM,OAAO,GAAY;QACjB,OACE,IAAA,CAAKtC,KAAL,CAAWuC,aAAX,IACA,CAAC,IAAA,CAAKvC,KAAL,CAAWgB,aADZ,IAEA,IAAA,CAAKvB,SAAL,CAAewC,IAAf,EAAqBC,QAAD,GAAcA,QAAQ,CAACM,gBAAT,EAA4BF,CAAAA,OAA9D,CAHF,CAAA;IAKD,CAAA;IAEDG,aAAa,CAACC,SAAS,GAAG,CAAb,EAAyB;QACpC,OACE,IAAA,CAAK1C,KAAL,CAAWuC,aAAX,IACA,CAAC,IAAA,CAAKvC,KAAL,CAAWgB,aADZ,IAEA,kNAAC2B,iBAAAA,AAAc,EAAC,IAAK3C,CAAAA,KAAL,CAAWgB,aAAZ,EAA2B0B,SAA3B,CAHjB,CAAA;IAKD,CAAA;IAEDE,OAAO,GAAS;QAAA,IAAA,cAAA,CAAA;QACd,MAAMV,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,EAAqBC,CAAD,GAAOA,CAAC,CAACC,wBAAF,EAA3B,CAAjB,CAAA;QAEA,IAAIb,QAAJ,EAAc;YACZA,QAAQ,CAACc,OAAT,CAAiB;gBAAEC,aAAa,EAAE,KAAA;aAAlC,CAAA,CAAA;QACD,CALa,CAAA,qCAAA;QAQd,CAAK1B,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,KAAA,OAAA,KAAA,IAAA,eAAc2B,QAAd,EAAA,CAAA;IACD,CAAA;IAEDC,QAAQ,GAAS;QAAA,IAAA,cAAA,CAAA;QACf,MAAMjB,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,EAAqBC,CAAD,GAAOA,CAAC,CAACM,sBAAF,EAA3B,CAAjB,CAAA;QAEA,IAAIlB,QAAJ,EAAc;YACZA,QAAQ,CAACc,OAAT,CAAiB;gBAAEC,aAAa,EAAE,KAAA;aAAlC,CAAA,CAAA;QACD,CALc,CAAA,qCAAA;QAQf,CAAK1B,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,KAAA,OAAA,KAAA,IAAA,eAAc2B,QAAd,EAAA,CAAA;IACD,CAAA;IAEDG,WAAW,CAACnB,QAAD,EAAyD;QAClE,IAAI,CAAC,IAAA,CAAKzC,SAAL,CAAe6D,QAAf,CAAwBpB,QAAxB,CAAL,EAAwC;YACtC,IAAA,CAAKzC,SAAL,CAAe8D,IAAf,CAAoBrB,QAApB,EADsC,CAAA,8CAAA;YAItC,IAAA,CAAKsB,cAAL,EAAA,CAAA;YAEA,IAAK9D,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;gBAAE1C,IAAI,EAAE,eAAR;gBAAyB2C,KAAK,EAAE,IAAhC;gBAAsCxB,QAAAA;aAAxD,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDyB,cAAc,CAACzB,QAAD,EAAyD;QACrE,IAAI,IAAA,CAAKzC,SAAL,CAAe6D,QAAf,CAAwBpB,QAAxB,CAAJ,EAAuC;YACrC,IAAA,CAAKzC,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAemE,MAAf,EAAuBd,CAAD,GAAOA,CAAC,KAAKZ,QAAnC,CAAjB,CAAA;YAEA,IAAI,CAAC,IAAA,CAAKzC,SAAL,CAAec,MAApB,EAA4B;gBAC1B,uDAAA;gBACA,2DAAA;gBACA,IAAI,IAAA,CAAKgB,OAAT,EAAkB;oBAChB,IAAI,IAAA,CAAKlC,mBAAT,EAA8B;wBAC5B,IAAKkC,CAAAA,OAAL,CAAaF,MAAb,CAAoB;4BAAEwC,MAAM,EAAE,IAAA;yBAA9B,CAAA,CAAA;oBACD,CAFD,MAEO;wBACL,IAAKtC,CAAAA,OAAL,CAAauC,WAAb,EAAA,CAAA;oBACD,CAAA;gBACF,CAAA;gBAED,IAAA,CAAK5D,UAAL,EAAA,CAAA;YACD,CAAA;YAED,IAAKR,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;gBAAE1C,IAAI,EAAE,iBAAR;gBAA2B2C,KAAK,EAAE,IAAlC;gBAAwCxB,QAAAA;aAA1D,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDG,iBAAiB,GAAW;QAC1B,OAAO,IAAA,CAAK5C,SAAL,CAAec,MAAtB,CAAA;IACD,CAAA;IAEDwD,UAAU,GAAS;QACjB,IAAI,CAAC,IAAA,CAAK/D,KAAL,CAAWuC,aAAhB,EAA+B;YAC7B,IAAA,CAAKzB,QAAL,CAAc;gBAAEC,IAAI,EAAE,YAAA;aAAtB,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDiD,KAAK,CACHxE,OADG,EAEHyE,YAFG,EAGa;QAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;QAChB,IAAI,IAAA,CAAKjE,KAAL,CAAWQ,WAAX,KAA2B,MAA/B,EAAuC;YACrC,IAAI,IAAA,CAAKR,KAAL,CAAWgB,aAAX,IAA4BiD,YAA5B,IAA4BA,IAAAA,IAAAA,YAAY,CAAEhB,aAA9C,EAA6D;gBAC3D,sEAAA;gBACA,IAAA,CAAK5B,MAAL,CAAY;oBAAES,MAAM,EAAE,IAAA;iBAAtB,CAAA,CAAA;YACD,CAHD,MAGO,IAAI,IAAKR,CAAAA,OAAT,EAAkB;gBAAA,IAAA,cAAA,CAAA;gBACvB,sFAAA;gBACA,CAAA,cAAA,GAAA,IAAA,CAAKC,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAc2C,aAAd,EAAA,CAFuB,CAAA,oDAAA;gBAIvB,OAAO,IAAA,CAAK5C,OAAZ,CAAA;YACD,CAAA;QACF,CAXe,CAAA,gFAAA;QAchB,IAAI9B,OAAJ,EAAa;YACX,IAAKD,CAAAA,UAAL,CAAgBC,OAAhB,CAAA,CAAA;QACD,CAhBe,CAAA,yFAAA;QAmBhB,2EAAA;QACA,IAAI,CAAC,IAAA,CAAKA,OAAL,CAAa2E,OAAlB,EAA2B;YACzB,MAAMjC,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,EAAqBC,CAAD,GAAOA,CAAC,CAACtD,OAAF,CAAU2E,OAArC,CAAjB,CAAA;YACA,IAAIjC,QAAJ,EAAc;gBACZ,IAAA,CAAK3C,UAAL,CAAgB2C,QAAQ,CAAC1C,OAAzB,CAAA,CAAA;YACD,CAAA;QACF,CAAA;QAED,IAAI4E,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;YACE,IAAI,CAACC,KAAK,CAACC,OAAN,CAAc,IAAA,CAAKhF,OAAL,CAAaK,QAA3B,CAAL,EAA2C;gBACzC,IAAKF,CAAAA,MAAL,CAAY8E,KAAZ,CAAA,qIAAA,CAAA,CAAA;YAGD,CAAA;QACF,CAAA;QAED,MAAMC,eAAe,mNAAGC,sBAAAA,AAAkB,EAA1C,CAnCgB,EAAA,gCAAA;QAsChB,MAAMC,cAA+C,GAAG;YACtD/E,QAAQ,EAAE,IAAA,CAAKA,QADuC;YAEtDgF,SAAS,EAAEC,SAF2C;YAGtD3E,IAAI,EAAE,IAAKA,CAAAA,IAAAA;QAH2C,CAAxD,CAtCgB,CAAA,wDAAA;QA6ChB,yDAAA;QACA,WAAA;QACA,MAAM4E,iBAAiB,IAAIC,MAAD,IAAqB;YAC7CC,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;gBACtCG,UAAU,EAAE,IAD0B;gBAEtCC,GAAG,EAAE,MAAM;oBACT,IAAIV,eAAJ,EAAqB;wBACnB,IAAKrF,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;wBACA,OAAOqF,eAAe,CAACW,MAAvB,CAAA;oBACD,CAAA;oBACD,OAAOP,SAAP,CAAA;gBACD,CAAA;aARH,CAAA,CAAA;SADF,CAAA;QAaAC,iBAAiB,CAACH,cAAD,CAAjB,CA5DgB,CAAA,wBAAA;QA+DhB,MAAMU,OAAO,GAAG,MAAM;YACpB,IAAI,CAAC,IAAA,CAAK9F,OAAL,CAAa2E,OAAlB,EAA2B;gBACzB,OAAOxC,OAAO,CAAC4D,MAAR,CAAA,gCAAA,GAC4B,IAAA,CAAK/F,OAAL,CAAaM,SADzC,GAAP,GAAA,CAAA,CAAA;YAGD,CAAA;YACD,IAAKT,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;YACA,OAAO,IAAA,CAAKG,OAAL,CAAa2E,OAAb,CAAqBS,cAArB,CAAP,CAAA;QACD,CARD,CA/DgB,CAAA,wBAAA;QA0EhB,MAAMY,OAA6D,GAAG;YACpEvB,YADoE;YAEpEzE,OAAO,EAAE,IAAA,CAAKA,OAFsD;YAGpEK,QAAQ,EAAE,IAAA,CAAKA,QAHqD;YAIpEG,KAAK,EAAE,IAAA,CAAKA,KAJwD;YAKpEsF,OAAAA;SALF,CAAA;QAQAP,iBAAiB,CAACS,OAAD,CAAjB,CAAA;QAEA,CAAKhG,qBAAAA,GAAAA,IAAAA,CAAAA,OAAL,CAAaiG,QAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAuBC,OAAvB,CAA+BF,OAA/B,EApFgB,CAAA,6DAAA;QAuFhB,IAAA,CAAKG,WAAL,GAAmB,IAAK3F,CAAAA,KAAxB,CAvFgB,CAAA,6CAAA;QA0FhB,IACE,IAAA,CAAKA,KAAL,CAAWQ,WAAX,KAA2B,MAA3B,IACA,IAAKR,CAAAA,KAAL,CAAW4F,SAAX,KAAA,CAAA,CAAA,qBAAA,GAAyBJ,OAAO,CAACvB,YAAjC,KAAA,OAAA,KAAA,IAAyB,qBAAsB9D,CAAAA,IAA/C,CAFF,EAGE;YAAA,IAAA,sBAAA,CAAA;YACA,IAAA,CAAKW,QAAL,CAAc;gBAAEC,IAAI,EAAE,OAAR;gBAAiBZ,IAAI,EAAEqF,CAAAA,sBAAAA,GAAAA,OAAO,CAACvB,YAAV,KAAA,OAAA,KAAA,IAAE,sBAAsB9D,CAAAA,IAAAA;aAA3D,CAAA,CAAA;QACD,CAAA;QAED,MAAM0F,OAAO,IAAIpB,KAAD,IAA0C;YACxD,wCAAA;YACA,IAAI,CAAA,oNAAEqB,mBAAAA,AAAgB,EAACrB,KAAD,CAAhB,IAA2BA,KAAK,CAAC3C,MAAnC,CAAJ,EAAgD;gBAC9C,IAAA,CAAKhB,QAAL,CAAc;oBACZC,IAAI,EAAE,OADM;oBAEZ0D,KAAK,EAAEA,KAAAA;iBAFT,CAAA,CAAA;YAID,CAAA;YAED,IAAI,CAACqB,sOAAAA,AAAgB,EAACrB,KAAD,CAArB,EAA8B;gBAAA,IAAA,qBAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;gBAC5B,wBAAA;gBACA,CAAK/E,qBAAAA,GAAAA,CAAAA,kBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWN,MAAX,EAAkByG,OAAlB,KAA4BpB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,kBAAAA,EAAAA,KAA5B,EAAmC,IAAnC,CAAA,CAAA;gBACA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAK/E,KAAL,CAAWN,MAAX,EAAkB2G,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACE,IAAK/F,CAAAA,KAAL,CAAWY,IADb,EAEE6D,KAFF,EAGE,IAHF,CAAA,CAAA;gBAMA,IAAIL,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;oBACE,IAAA,CAAK3E,MAAL,CAAY8E,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;gBACD,CAAA;YACF,CAAA;YAED,IAAI,CAAC,IAAKuB,CAAAA,oBAAV,EAAgC;gBAC9B,mCAAA;gBACA,IAAA,CAAK9F,UAAL,EAAA,CAAA;YACD,CAAA;YACD,IAAK8F,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;QACD,CA5BD,CAjGgB,CAAA,wBAAA;QAgIhB,IAAKzE,CAAAA,OAAL,OAAe0E,+NAAAA,AAAa,EAAC;YAC3BC,EAAE,EAAEV,OAAO,CAACF,OADe;YAE3Ba,KAAK,EAAEzB,eAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,eAAe,CAAEyB,KAAjB,CAAuBC,IAAvB,CAA4B1B,eAA5B,CAFoB;YAG3B2B,SAAS,GAAGzF,IAAD,IAAU;gBAAA,IAAA,sBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;gBACnB,IAAI,OAAOA,IAAP,KAAgB,WAApB,EAAiC;oBAC/B,IAAIwD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;wBACE,IAAA,CAAK3E,MAAL,CAAY8E,KAAZ,CAAA,wIAAA,GAC2I,IAAA,CAAK3E,SADhJ,CAAA,CAAA;oBAGD,CAAA;oBACD+F,OAAO,CAAC,IAAIS,KAAJ,CAAa,IAAKxG,CAAAA,SAAlB,GAAA,qBAAD,CAAP,CAAA;oBACA,OAAA;gBACD,CAAA;gBAED,IAAA,CAAKY,OAAL,CAAaE,IAAb,CAAA,CAXmB,CAAA,wBAAA;gBAcnB,CAAKlB,sBAAAA,GAAAA,CAAAA,mBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWN,MAAX,EAAkBiH,SAAlB,KAA8BzF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAAAA,IAA9B,EAAoC,IAApC,CAAA,CAAA;gBACA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKlB,KAAL,CAAWN,MAAX,EAAkB2G,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACEnF,IADF,EAEE,IAAKZ,CAAAA,KAAL,CAAWyE,KAFb,EAGE,IAHF,CAAA,CAAA;gBAMA,IAAI,CAAC,IAAKuB,CAAAA,oBAAV,EAAgC;oBAC9B,mCAAA;oBACA,IAAA,CAAK9F,UAAL,EAAA,CAAA;gBACD,CAAA;gBACD,IAAK8F,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;aA5ByB;YA8B3BH,OA9B2B;YA+B3BU,MAAM,EAAE,CAACC,YAAD,EAAe/B,KAAf,KAAyB;gBAC/B,IAAA,CAAK3D,QAAL,CAAc;oBAAEC,IAAI,EAAE,QAAR;oBAAkByF,YAAlB;oBAAgC/B,KAAAA;iBAA9C,CAAA,CAAA;aAhCyB;YAkC3BgC,OAAO,EAAE,MAAM;gBACb,IAAA,CAAK3F,QAAL,CAAc;oBAAEC,IAAI,EAAE,OAAA;iBAAtB,CAAA,CAAA;aAnCyB;YAqC3B2F,UAAU,EAAE,MAAM;gBAChB,IAAA,CAAK5F,QAAL,CAAc;oBAAEC,IAAI,EAAE,UAAA;iBAAtB,CAAA,CAAA;aAtCyB;YAwC3B4F,KAAK,EAAEnB,OAAO,CAAChG,OAAR,CAAgBmH,KAxCI;YAyC3BC,UAAU,EAAEpB,OAAO,CAAChG,OAAR,CAAgBoH,UAzCD;YA0C3BC,WAAW,EAAErB,OAAO,CAAChG,OAAR,CAAgBqH,WAAAA;QA1CF,CAAD,CAA5B,CAAA;QA6CA,IAAA,CAAKvF,OAAL,GAAe,IAAKC,CAAAA,OAAL,CAAaD,OAA5B,CAAA;QAEA,OAAO,IAAA,CAAKA,OAAZ,CAAA;IACD,CAAA;IAEOR,QAAQ,CAACgG,MAAD,EAAsC;QACpD,MAAMC,OAAO,GACX/G,KADc,IAEgB;YAAA,IAAA,YAAA,EAAA,qBAAA,CAAA;YAC9B,OAAQ8G,MAAM,CAAC/F,IAAf;gBACE,KAAK,QAAL;oBACE,OAAO;wBACL,GAAGf,KADE;wBAELgH,iBAAiB,EAAEF,MAAM,CAACN,YAFrB;wBAGLS,kBAAkB,EAAEH,MAAM,CAACrC,KAAAA;qBAH7B,CAAA;gBAKF,KAAK,OAAL;oBACE,OAAO;wBACL,GAAGzE,KADE;wBAELQ,WAAW,EAAE,QAAA;qBAFf,CAAA;gBAIF,KAAK,UAAL;oBACE,OAAO;wBACL,GAAGR,KADE;wBAELQ,WAAW,EAAE,UAAA;qBAFf,CAAA;gBAIF,KAAK,OAAL;oBACE,OAAO;wBACL,GAAGR,KADE;wBAELgH,iBAAiB,EAAE,CAFd;wBAGLC,kBAAkB,EAAE,IAHf;wBAILrB,SAAS,EAAEkB,CAAAA,YAAAA,GAAAA,MAAM,CAAC3G,IAAT,KAAA,OAAA,eAAiB,IAJrB;wBAKLK,WAAW,qNAAE0G,WAAAA,AAAQ,EAAC,IAAK1H,CAAAA,OAAL,CAAaqH,WAAd,CAAR,GACT,UADS,GAET,QAPC;wBAQL,GAAI,CAAC7G,KAAK,CAACgB,aAAP,IAAwB;4BAC1ByD,KAAK,EAAE,IADmB;4BAE1B0C,MAAM,EAAE,SAAA;yBAFV,CAAA;qBARF,CAAA;gBAaF,KAAK,SAAL;oBACE,OAAO;wBACL,GAAGnH,KADE;wBAELY,IAAI,EAAEkG,MAAM,CAAClG,IAFR;wBAGLwG,eAAe,EAAEpH,KAAK,CAACoH,eAAN,GAAwB,CAHpC;wBAILpG,aAAa,EAAA,CAAA,qBAAA,GAAE8F,MAAM,CAAC9F,aAAT,KAAA,OAAA,wBAA0BqG,IAAI,CAACC,GAAL,EAJlC;wBAKL7C,KAAK,EAAE,IALF;wBAMLlC,aAAa,EAAE,KANV;wBAOL4E,MAAM,EAAE,SAPH;wBAQL,GAAI,CAACL,MAAM,CAAC5F,MAAR,IAAkB;4BACpBV,WAAW,EAAE,MADO;4BAEpBwG,iBAAiB,EAAE,CAFC;4BAGpBC,kBAAkB,EAAE,IAAA;yBAHtB,CAAA;qBARF,CAAA;gBAcF,KAAK,OAAL;oBACE,MAAMxC,KAAK,GAAGqC,MAAM,CAACrC,KAArB,CAAA;oBAEA,uNAAIqB,mBAAAA,AAAgB,EAACrB,KAAD,CAAhB,IAA2BA,KAAK,CAACZ,MAAjC,IAA2C,IAAK8B,CAAAA,WAApD,EAAiE;wBAC/D,OAAO;4BAAE,GAAG,IAAA,CAAKA,WAAV;4BAAuBnF,WAAW,EAAE,MAAA;yBAA3C,CAAA;oBACD,CAAA;oBAED,OAAO;wBACL,GAAGR,KADE;wBAELyE,KAAK,EAAEA,KAFF;wBAGL8C,gBAAgB,EAAEvH,KAAK,CAACuH,gBAAN,GAAyB,CAHtC;wBAILC,cAAc,EAAEH,IAAI,CAACC,GAAL,EAJX;wBAKLN,iBAAiB,EAAEhH,KAAK,CAACgH,iBAAN,GAA0B,CALxC;wBAMLC,kBAAkB,EAAExC,KANf;wBAOLjE,WAAW,EAAE,MAPR;wBAQL2G,MAAM,EAAE,OAAA;qBARV,CAAA;gBAUF,KAAK,YAAL;oBACE,OAAO;wBACL,GAAGnH,KADE;wBAELuC,aAAa,EAAE,IAAA;qBAFjB,CAAA;gBAIF,KAAK,UAAL;oBACE,OAAO;wBACL,GAAGvC,KADE;wBAEL,GAAG8G,MAAM,CAAC9G,KAAAA;qBAFZ,CAAA;YArEJ,CAAA;SAHF,CAAA;QA+EA,IAAA,CAAKA,KAAL,GAAa+G,OAAO,CAAC,IAAA,CAAK/G,KAAN,CAApB,CAAA;6NAEAyH,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAKjI,SAAL,CAAekI,OAAf,CAAwBzF,QAAD,IAAc;gBACnCA,QAAQ,CAAC0F,aAAT,CAAuBd,MAAvB,CAAA,CAAA;aADF,CAAA,CAAA;YAIA,IAAKpH,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;gBAAEC,KAAK,EAAE,IAAT;gBAAe3C,IAAI,EAAE,SAArB;gBAAgC+F,MAAAA;aAAlD,CAAA,CAAA;SALF,CAAA,CAAA;IAOD,CAAA;AAnciB,CAAA;AAscpB,SAAS7G,eAAT,CAMET,OANF,EAO6B;IAC3B,MAAMoB,IAAI,GACR,OAAOpB,OAAO,CAACqI,WAAf,KAA+B,UAA/B,GACKrI,OAAO,CAACqI,WAAT,EADJ,GAEIrI,OAAO,CAACqI,WAHd,CAAA;IAKA,MAAMC,OAAO,GAAG,OAAOlH,IAAP,KAAgB,WAAhC,CAAA;IAEA,MAAMmH,oBAAoB,GAAGD,OAAO,GAChC,OAAOtI,OAAO,CAACuI,oBAAf,KAAwC,UAAxC,GACGvI,OAAO,CAACuI,oBAAT,EADF,GAEEvI,OAAO,CAACuI,oBAHsB,GAIhC,CAJJ,CAAA;IAMA,OAAO;QACLnH,IADK;QAELwG,eAAe,EAAE,CAFZ;QAGLpG,aAAa,EAAE8G,OAAO,GAAGC,oBAAH,IAAGA,IAAAA,GAAAA,oBAAH,GAA2BV,IAAI,CAACC,GAAL,EAA3B,GAAwC,CAHzD;QAIL7C,KAAK,EAAE,IAJF;QAKL8C,gBAAgB,EAAE,CALb;QAMLC,cAAc,EAAE,CANX;QAOLR,iBAAiB,EAAE,CAPd;QAQLC,kBAAkB,EAAE,IARf;QASLrB,SAAS,EAAE,IATN;QAULrD,aAAa,EAAE,KAVV;QAWL4E,MAAM,EAAEW,OAAO,GAAG,SAAH,GAAe,SAXzB;QAYLtH,WAAW,EAAE,MAAA;KAZf,CAAA;AAcD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "file": "queryCache.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/queryCache.ts"], "sourcesContent": ["import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n"], "names": ["Query<PERSON>ache", "Subscribable", "constructor", "config", "queries", "queriesMap", "build", "client", "options", "state", "query<PERSON><PERSON>", "queryHash", "hashQueryKeyByOptions", "query", "get", "Query", "cache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "push", "notify", "type", "remove", "queryInMap", "destroy", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "exact", "matchQuery", "findAll", "Object", "keys", "length", "event", "listeners", "listener", "onFocus", "onOnline"], "mappings": ";;;;;;;;;;;AA4EA,QAAA;AAEO,MAAMA,UAAN,6NAAyBC,eAAzB,CAA0D;IAM/DC,WAAW,CAACC,MAAD,CAA4B;QACrC,KAAA,EAAA,CAAA;QACA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,CAAA,CAAxB,CAAA;QACA,IAAKC,CAAAA,OAAL,GAAe,EAAf,CAAA;QACA,IAAKC,CAAAA,UAAL,GAAkB,CAAA,CAAlB,CAAA;IACD,CAAA;IAEDC,KAAK,CACHC,MADG,EAEHC,OAFG,EAGHC,KAHG,EAI4C;QAAA,IAAA,kBAAA,CAAA;QAC/C,MAAMC,QAAQ,GAAGF,OAAO,CAACE,QAAzB,CAAA;QACA,MAAMC,SAAS,GACbH,CAAAA,kBAAAA,GAAAA,OAAO,CAACG,SADK,KACQC,IAAAA,GAAAA,kBAAAA,oNAAAA,wBAAAA,AAAqB,EAACF,QAAD,EAAWF,OAAX,CAD5C,CAAA;QAEA,IAAIK,KAAK,GAAG,IAAA,CAAKC,GAAL,CAAiDH,SAAjD,CAAZ,CAAA;QAEA,IAAI,CAACE,KAAL,EAAY;YACVA,KAAK,GAAG,iNAAIE,QAAJ,CAAU;gBAChBC,KAAK,EAAE,IADS;gBAEhBC,MAAM,EAAEV,MAAM,CAACW,SAAP,EAFQ;gBAGhBR,QAHgB;gBAIhBC,SAJgB;gBAKhBH,OAAO,EAAED,MAAM,CAACY,mBAAP,CAA2BX,OAA3B,CALO;gBAMhBC,KANgB;gBAOhBW,cAAc,EAAEb,MAAM,CAACc,gBAAP,CAAwBX,QAAxB,CAAA;YAPA,CAAV,CAAR,CAAA;YASA,IAAKY,CAAAA,GAAL,CAAST,KAAT,CAAA,CAAA;QACD,CAAA;QAED,OAAOA,KAAP,CAAA;IACD,CAAA;IAEDS,GAAG,CAACT,KAAD,EAAyC;QAC1C,IAAI,CAAC,IAAA,CAAKR,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAL,EAAuC;YACrC,IAAA,CAAKN,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAA,GAAmCE,KAAnC,CAAA;YACA,IAAA,CAAKT,OAAL,CAAamB,IAAb,CAAkBV,KAAlB,CAAA,CAAA;YACA,IAAA,CAAKW,MAAL,CAAY;gBACVC,IAAI,EAAE,OADI;gBAEVZ,KAAAA;aAFF,CAAA,CAAA;QAID,CAAA;IACF,CAAA;IAEDa,MAAM,CAACb,KAAD,EAAyC;QAC7C,MAAMc,UAAU,GAAG,IAAKtB,CAAAA,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAnB,CAAA;QAEA,IAAIgB,UAAJ,EAAgB;YACdd,KAAK,CAACe,OAAN,EAAA,CAAA;YAEA,IAAA,CAAKxB,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAayB,MAAb,EAAqBC,CAAD,GAAOA,CAAC,KAAKjB,KAAjC,CAAf,CAAA;YAEA,IAAIc,UAAU,KAAKd,KAAnB,EAA0B;gBACxB,OAAO,IAAA,CAAKR,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAP,CAAA;YACD,CAAA;YAED,IAAA,CAAKa,MAAL,CAAY;gBAAEC,IAAI,EAAE,SAAR;gBAAmBZ,KAAAA;aAA/B,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDkB,KAAK,GAAS;6NACZC,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,EAAsBrB,KAAD,IAAW;gBAC9B,IAAKa,CAAAA,MAAL,CAAYb,KAAZ,CAAA,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAEDC,GAAG,CAMDH,SANC,EAO0D;QAC3D,OAAO,IAAKN,CAAAA,UAAL,CAAgBM,SAAhB,CAAP,CAAA;IACD,CAAA;IAEDwB,MAAM,GAAY;QAChB,OAAO,IAAA,CAAK/B,OAAZ,CAAA;IACD,CAAA;IAYD;;GAEF,GACEgC,IAAI,CACFC,IADE,EAEFC,IAFE,EAG8C;QAChD,MAAM,CAACC,OAAD,CAAYC,GAAAA,mOAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;QAEA,IAAI,OAAOC,OAAO,CAACE,KAAf,KAAyB,WAA7B,EAA0C;YACxCF,OAAO,CAACE,KAAR,GAAgB,IAAhB,CAAA;QACD,CAAA;QAED,OAAO,IAAKrC,CAAAA,OAAL,CAAagC,IAAb,EAAmBvB,KAAD,oNAAW6B,aAAAA,AAAU,EAACH,OAAD,EAAU1B,KAAV,CAAvC,CAAP,CAAA;IACD,CAAA;IAiBD;;GAEF,GACE8B,OAAO,CACLN,IADK,EAELC,IAFK,EAGI;QACT,MAAM,CAACC,OAAD,CAAYC,GAAAA,mOAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;QACA,OAAOM,MAAM,CAACC,IAAP,CAAYN,OAAZ,CAAqBO,CAAAA,MAArB,GAA8B,CAA9B,GACH,IAAA,CAAK1C,OAAL,CAAayB,MAAb,CAAqBhB,KAAD,IAAW6B,8NAAAA,AAAU,EAACH,OAAD,EAAU1B,KAAV,CAAzC,CADG,GAEH,IAAA,CAAKT,OAFT,CAAA;IAGD,CAAA;IAEDoB,MAAM,CAACuB,KAAD,EAA+B;6NACnCf,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAKe,SAAL,CAAed,OAAf,CAAuB,CAAC,EAAEe,QAAAA,EAAH,KAAkB;gBACvCA,QAAQ,CAACF,KAAD,CAAR,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAEDG,OAAO,GAAS;6NACdlB,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,EAAsBrB,KAAD,IAAW;gBAC9BA,KAAK,CAACqC,OAAN,EAAA,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAEDC,QAAQ,GAAS;QACfnB,qOAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,EAAsBrB,KAAD,IAAW;gBAC9BA,KAAK,CAACsC,QAAN,EAAA,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;AArK8D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "file": "mutationCache.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n"], "names": ["MutationCache", "Subscribable", "constructor", "config", "mutations", "mutationId", "build", "client", "options", "state", "mutation", "Mutation", "mutationCache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultMutationOptions", "defaultOptions", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "undefined", "add", "push", "notify", "type", "remove", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "filters", "exact", "matchMutation", "findAll", "event", "listeners", "listener", "resumePausedMutations", "resuming", "Promise", "resolve", "then", "pausedMutations", "isPaused", "reduce", "promise", "continue", "catch", "noop"], "mappings": ";;;;;;;;;;;AAiFA,QAAA;AAEO,MAAMA,aAAN,6NAA4BC,eAA5B,CAAgE;IAOrEC,WAAW,CAACC,MAAD,CAA+B;QACxC,KAAA,EAAA,CAAA;QACA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,CAAA,CAAxB,CAAA;QACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;QACA,IAAKC,CAAAA,UAAL,GAAkB,CAAlB,CAAA;IACD,CAAA;IAEDC,KAAK,CACHC,MADG,EAEHC,OAFG,EAGHC,KAHG,EAI4C;QAC/C,MAAMC,QAAQ,GAAG,oNAAIC,WAAJ,CAAa;YAC5BC,aAAa,EAAE,IADa;YAE5BC,MAAM,EAAEN,MAAM,CAACO,SAAP,EAFoB;YAG5BT,UAAU,EAAE,EAAE,IAAA,CAAKA,UAHS;YAI5BG,OAAO,EAAED,MAAM,CAACQ,sBAAP,CAA8BP,OAA9B,CAJmB;YAK5BC,KAL4B;YAM5BO,cAAc,EAAER,OAAO,CAACS,WAAR,GACZV,MAAM,CAACW,mBAAP,CAA2BV,OAAO,CAACS,WAAnC,CADY,GAEZE,SAAAA;QARwB,CAAb,CAAjB,CAAA;QAWA,IAAKC,CAAAA,GAAL,CAASV,QAAT,CAAA,CAAA;QAEA,OAAOA,QAAP,CAAA;IACD,CAAA;IAEDU,GAAG,CAACV,QAAD,EAA+C;QAChD,IAAA,CAAKN,SAAL,CAAeiB,IAAf,CAAoBX,QAApB,CAAA,CAAA;QACA,IAAA,CAAKY,MAAL,CAAY;YAAEC,IAAI,EAAE,OAAR;YAAiBb,QAAAA;SAA7B,CAAA,CAAA;IACD,CAAA;IAEDc,MAAM,CAACd,QAAD,EAA+C;QACnD,IAAA,CAAKN,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeqB,MAAf,EAAuBC,CAAD,GAAOA,CAAC,KAAKhB,QAAnC,CAAjB,CAAA;QACA,IAAA,CAAKY,MAAL,CAAY;YAAEC,IAAI,EAAE,SAAR;YAAmBb,QAAAA;SAA/B,CAAA,CAAA;IACD,CAAA;IAEDiB,KAAK,GAAS;6NACZC,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAKzB,SAAL,CAAe0B,OAAf,CAAwBpB,QAAD,IAAc;gBACnC,IAAKc,CAAAA,MAAL,CAAYd,QAAZ,CAAA,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAEDqB,MAAM,GAAe;QACnB,OAAO,IAAA,CAAK3B,SAAZ,CAAA;IACD,CAAA;IAED4B,IAAI,CACFC,OADE,EAEyD;QAC3D,IAAI,OAAOA,OAAO,CAACC,KAAf,KAAyB,WAA7B,EAA0C;YACxCD,OAAO,CAACC,KAAR,GAAgB,IAAhB,CAAA;QACD,CAAA;QAED,OAAO,IAAK9B,CAAAA,SAAL,CAAe4B,IAAf,EAAqBtB,QAAD,oNAAcyB,gBAAAA,AAAa,EAACF,OAAD,EAAUvB,QAAV,CAA/C,CAAP,CAAA;IACD,CAAA;IAED0B,OAAO,CAACH,OAAD,EAAuC;QAC5C,OAAO,IAAK7B,CAAAA,SAAL,CAAeqB,MAAf,EAAuBf,QAAD,oNAAcyB,gBAAAA,AAAa,EAACF,OAAD,EAAUvB,QAAV,CAAjD,CAAP,CAAA;IACD,CAAA;IAEDY,MAAM,CAACe,KAAD,EAAkC;4NACtCT,iBAAa,CAACC,KAAd,CAAoB,MAAM;YACxB,IAAA,CAAKS,SAAL,CAAeR,OAAf,CAAuB,CAAC,EAAES,QAAAA,EAAH,KAAkB;gBACvCA,QAAQ,CAACF,KAAD,CAAR,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAEDG,qBAAqB,GAAqB;QAAA,IAAA,cAAA,CAAA;QACxC,IAAA,CAAKC,QAAL,GAAgB,CAAC,CAAA,cAAA,GAAA,IAAA,CAAKA,QAAN,KAAA,IAAA,GAAA,cAAA,GAAkBC,OAAO,CAACC,OAAR,EAAlB,EACbC,IADa,CACR,MAAM;YACV,MAAMC,eAAe,GAAG,IAAKzC,CAAAA,SAAL,CAAeqB,MAAf,EAAuBC,CAAD,GAAOA,CAAC,CAACjB,KAAF,CAAQqC,QAArC,CAAxB,CAAA;YACA,4NAAOlB,gBAAa,CAACC,KAAd,CAAoB,IACzBgB,eAAe,CAACE,MAAhB,CACE,CAACC,OAAD,EAAUtC,QAAV,GACEsC,OAAO,CAACJ,IAAR,CAAa,IAAMlC,QAAQ,CAACuC,QAAT,EAAA,CAAoBC,KAApB,8MAA0BC,OAA1B,CAAnB,CAFJ,EAGET,OAAO,CAACC,OAAR,EAHF,CADK,CAAP,CAAA;SAHY,CAAA,CAWbC,IAXa,CAWR,MAAM;YACV,IAAKH,CAAAA,QAAL,GAAgBtB,SAAhB,CAAA;QACD,CAba,CAAhB,CAAA;QAeA,OAAO,IAAA,CAAKsB,QAAZ,CAAA;IACD,CAAA;AAhGoE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "file": "queryClient.mjs", "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/node_modules/%40refinedev/core/node_modules/%40tanstack/query-core/src/queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n"], "names": ["QueryClient", "constructor", "config", "queryCache", "Query<PERSON>ache", "mutationCache", "MutationCache", "logger", "defaultLogger", "defaultOptions", "queryDefaults", "mutationDefaults", "mountCount", "process", "env", "NODE_ENV", "error", "mount", "unsubscribeFocus", "focusManager", "subscribe", "isFocused", "resumePausedMutations", "onFocus", "unsubscribeOnline", "onlineManager", "isOnline", "onOnline", "unmount", "undefined", "isFetching", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "fetchStatus", "findAll", "length", "isMutating", "fetching", "getQueryData", "query<PERSON><PERSON>", "find", "state", "data", "ensureQueryData", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cachedData", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "updater", "options", "query", "prevData", "functionalUpdate", "defaultedOptions", "defaultQueryOptions", "build", "setData", "manual", "setQueriesData", "notify<PERSON><PERSON>ger", "batch", "getQueryState", "removeQueries", "for<PERSON>ach", "remove", "resetQueries", "refetchFilters", "type", "reset", "refetchQueries", "cancelQueries", "cancelOptions", "revert", "promises", "cancel", "all", "then", "noop", "catch", "invalidateQueries", "invalidate", "refetchType", "filter", "isDisabled", "fetch", "cancelRefetch", "meta", "refetchPage", "promise", "throwOnError", "retry", "isStaleByTime", "staleTime", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "behavior", "infiniteQueryBehavior", "prefetchInfiniteQuery", "getMutationCache", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "x", "hashQuery<PERSON>ey", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "partialMatchKey", "matchingDefaults", "JSON", "stringify", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_defaulted", "queries", "queryHash", "hashQueryKeyByOptions", "refetchOnReconnect", "networkMode", "useErrorBoundary", "suspense", "defaultMutationOptions", "mutations", "clear"], "mappings": ";;;;;;;;;;;;;;;;;;;AAwDA,QAAA;AAEO,MAAMA,WAAN,CAAkB;IAWvBC,WAAW,CAACC,MAAyB,GAAG,CAAA,CAA7B,CAAiC;QAC1C,IAAKC,CAAAA,UAAL,GAAkBD,MAAM,CAACC,UAAP,IAAqB,sNAAIC,aAAJ,EAAvC,CAAA;QACA,IAAKC,CAAAA,aAAL,GAAqBH,MAAM,CAACG,aAAP,IAAwB,yNAAIC,gBAAJ,EAA7C,CAAA;QACA,IAAA,CAAKC,MAAL,GAAcL,MAAM,CAACK,MAAP,kNAAiBC,gBAA/B,CAAA;QACA,IAAA,CAAKC,cAAL,GAAsBP,MAAM,CAACO,cAAP,IAAyB,CAAA,CAA/C,CAAA;QACA,IAAKC,CAAAA,aAAL,GAAqB,EAArB,CAAA;QACA,IAAKC,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;QACA,IAAKC,CAAAA,UAAL,GAAkB,CAAlB,CAAA;QAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,gCAAyB,YAAzB,IAAyCb,MAAM,CAACK,MAApD,EAA4D;YAC1D,IAAKA,CAAAA,MAAL,CAAYS,KAAZ,CAAA,4FAAA,CAAA,CAAA;QAGD,CAAA;IACF,CAAA;IAEDC,KAAK,GAAS;QACZ,IAAA,CAAKL,UAAL,EAAA,CAAA;QACA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;QAE3B,IAAA,CAAKM,gBAAL,uNAAwBC,eAAY,CAACC,SAAb,CAAuB,MAAM;YACnD,wNAAID,eAAY,CAACE,SAAb,EAAJ,EAA8B;gBAC5B,IAAA,CAAKC,qBAAL,EAAA,CAAA;gBACA,IAAKnB,CAAAA,UAAL,CAAgBoB,OAAhB,EAAA,CAAA;YACD,CAAA;QACF,CALuB,CAAxB,CAAA;QAMA,IAAA,CAAKC,iBAAL,wNAAyBC,gBAAa,CAACL,SAAd,CAAwB,MAAM;YACrD,yNAAIK,gBAAa,CAACC,QAAd,EAAJ,EAA8B;gBAC5B,IAAA,CAAKJ,qBAAL,EAAA,CAAA;gBACA,IAAKnB,CAAAA,UAAL,CAAgBwB,QAAhB,EAAA,CAAA;YACD,CAAA;QACF,CALwB,CAAzB,CAAA;IAMD,CAAA;IAEDC,OAAO,GAAS;QAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;QACd,IAAA,CAAKhB,UAAL,EAAA,CAAA;QACA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;QAE3B,CAAA,qBAAA,GAAA,IAAA,CAAKM,gBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAKA,CAAAA,gBAAL,GAAwBW,SAAxB,CAAA;QAEA,CAAA,qBAAA,GAAA,IAAA,CAAKL,iBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAKA,CAAAA,iBAAL,GAAyBK,SAAzB,CAAA;IACD,CAAA;IAUD;;GAEF,GACEC,UAAU,CAACC,IAAD,EAAiCC,IAAjC,EAA8D;QACtE,MAAM,CAACC,OAAD,CAAYC,oNAAAA,kBAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;QACAC,OAAO,CAACE,WAAR,GAAsB,UAAtB,CAAA;QACA,OAAO,IAAA,CAAKhC,UAAL,CAAgBiC,OAAhB,CAAwBH,OAAxB,EAAiCI,MAAxC,CAAA;IACD,CAAA;IAEDC,UAAU,CAACL,OAAD,EAAoC;QAC5C,OAAO,IAAA,CAAK5B,aAAL,CAAmB+B,OAAnB,CAA2B;YAAE,GAAGH,OAAL;YAAcM,QAAQ,EAAE,IAAA;QAAxB,CAA3B,EAA2DF,MAAlE,CAAA;IACD,CAAA;IAYD;;GAEF,GACEG,YAAY,CACVC,QADU,EAEVR,OAFU,EAGgB;QAAA,IAAA,qBAAA,CAAA;QAC1B,OAAA,CAAA,qBAAA,GAAO,IAAK9B,CAAAA,UAAL,CAAgBuC,IAAhB,CAAmCD,QAAnC,EAA6CR,OAA7C,CAAP,KAAA,IAAA,GAAA,KAAA,CAAA,GAAO,qBAAuDU,CAAAA,KAAvD,CAA6DC,IAApE,CAAA;IACD,CAAA;IA4CD;;GAEF,GACEC,eAAe,CAMbd,IANa,EAYbC,IAZa,EAebc,IAfa,EAgBG;QAChB,MAAMC,aAAa,GAAGC,kOAAAA,AAAc,EAACjB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;QACA,MAAMG,UAAU,GAAG,IAAKT,CAAAA,YAAL,CAAyBO,aAAa,CAACN,QAAvC,CAAnB,CAAA;QAEA,OAAOQ,UAAU,GACbC,OAAO,CAACC,OAAR,CAAgBF,UAAhB,CADa,GAEb,IAAA,CAAKG,UAAL,CAAgBL,aAAhB,CAFJ,CAAA;IAGD,CAAA;IAWD;;GAEF,GACEM,cAAc,CACZC,iBADY,EAE4B;QACxC,OAAO,IAAA,CAAKC,aAAL,EACJnB,CAAAA,OADI,CACIkB,iBADJ,CAAA,CAEJE,GAFI,CAEA,CAAC,EAAEf,QAAF,EAAYE,KAAAA,EAAb,KAAyB;YAC5B,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAnB,CAAA;YACA,OAAO;gBAACH,QAAD;gBAAWG,IAAX;aAAP,CAAA;QACD,CALI,CAAP,CAAA;IAMD,CAAA;IAEDa,YAAY,CACVhB,QADU,EAEViB,OAFU,EAGVC,OAHU,EAIgB;QAC1B,MAAMC,KAAK,GAAG,IAAKzD,CAAAA,UAAL,CAAgBuC,IAAhB,CAAmCD,QAAnC,CAAd,CAAA;QACA,MAAMoB,QAAQ,GAAGD,KAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,KAAK,CAAEjB,KAAP,CAAaC,IAA9B,CAAA;QACA,MAAMA,IAAI,oNAAGkB,mBAAAA,AAAgB,EAACJ,OAAD,EAAUG,QAAV,CAA7B,CAAA;QAEA,IAAI,OAAOjB,IAAP,KAAgB,WAApB,EAAiC;YAC/B,OAAOf,SAAP,CAAA;QACD,CAAA;QAED,MAAMkB,aAAa,oNAAGC,iBAAAA,AAAc,EAACP,QAAD,CAApC,CAAA;QACA,MAAMsB,gBAAgB,GAAG,IAAA,CAAKC,mBAAL,CAAyBjB,aAAzB,CAAzB,CAAA;QACA,OAAO,IAAK5C,CAAAA,UAAL,CACJ8D,KADI,CACE,IADF,EACQF,gBADR,CAAA,CAEJG,OAFI,CAEItB,IAFJ,EAEU;YAAE,GAAGe,OAAL;YAAcQ,MAAM,EAAE,IAAA;QAAtB,CAFV,CAAP,CAAA;IAGD,CAAA;IAeD;;GAEF,GACEC,cAAc,CACZd,iBADY,EAEZI,OAFY,EAGZC,OAHY,EAI4B;QACxC,4NAAOU,gBAAa,CAACC,KAAd,CAAoB,IACzB,IAAKf,CAAAA,aAAL,EACGnB,CAAAA,OADH,CACWkB,iBADX,CAEGE,CAAAA,GAFH,CAEO,CAAC,EAAEf,QAAAA,EAAH,GAAkB;oBACrBA,QADqB;oBAErB,IAAA,CAAKgB,YAAL,CAAgChB,QAAhC,EAA0CiB,OAA1C,EAAmDC,OAAnD,CAFqB;iBAFzB,CADK,CAAP,CAAA;IAQD,CAAA;IAEDY,aAAa,CACX9B,QADW,EAEX;;GAEJ,GACIR,OALW,EAMmC;QAAA,IAAA,sBAAA,CAAA;QAC9C,OAAO,CAAA,sBAAA,GAAA,IAAA,CAAK9B,UAAL,CAAgBuC,IAAhB,CAA2CD,QAA3C,EAAqDR,OAArD,CAAP,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA+DU,KAAtE,CAAA;IACD,CAAA;IAUD;;GAEF,GACE6B,aAAa,CACXzC,IADW,EAEXC,IAFW,EAGL;QACN,MAAM,CAACC,OAAD,CAAYC,IAAAA,kOAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;QACA,MAAM7B,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;6NACAkE,gBAAa,CAACC,KAAd,CAAoB,MAAM;YACxBnE,UAAU,CAACiC,OAAX,CAAmBH,OAAnB,EAA4BwC,OAA5B,EAAqCb,KAAD,IAAW;gBAC7CzD,UAAU,CAACuE,MAAX,CAAkBd,KAAlB,CAAA,CAAA;aADF,CAAA,CAAA;SADF,CAAA,CAAA;IAKD,CAAA;IAcD;;GAEF,GACEe,YAAY,CACV5C,IADU,EAEVC,IAFU,EAGVc,IAHU,EAIK;QACf,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,oNAAAA,kBAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;QACA,MAAM3C,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;QAEA,MAAMyE,cAAmC,GAAG;YAC1CC,IAAI,EAAE,QADoC;YAE1C,GAAG5C,OAAAA;SAFL,CAAA;QAKA,4NAAOoC,gBAAa,CAACC,KAAd,CAAoB,MAAM;YAC/BnE,UAAU,CAACiC,OAAX,CAAmBH,OAAnB,EAA4BwC,OAA5B,EAAqCb,KAAD,IAAW;gBAC7CA,KAAK,CAACkB,KAAN,EAAA,CAAA;aADF,CAAA,CAAA;YAGA,OAAO,IAAA,CAAKC,cAAL,CAAoBH,cAApB,EAAoCjB,OAApC,CAAP,CAAA;QACD,CALM,CAAP,CAAA;IAMD,CAAA;IAWD;;GAEF,GACEqB,aAAa,CACXjD,IADW,EAEXC,IAFW,EAGXc,IAHW,EAII;QACf,MAAM,CAACb,OAAD,EAAUgD,aAAa,GAAG,CAAA,CAA1B,CAAA,OAAgC/C,+NAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAArD,CAAA;QAEA,IAAI,OAAOmC,aAAa,CAACC,MAArB,KAAgC,WAApC,EAAiD;YAC/CD,aAAa,CAACC,MAAd,GAAuB,IAAvB,CAAA;QACD,CAAA;QAED,MAAMC,QAAQ,GAAGd,qOAAa,CAACC,KAAd,CAAoB,IACnC,IAAA,CAAKnE,UAAL,CACGiC,OADH,CACWH,OADX,CAEGuB,CAAAA,GAFH,EAEQI,KAAD,GAAWA,KAAK,CAACwB,MAAN,CAAaH,aAAb,CAFlB,CADe,CAAjB,CAAA;QAMA,OAAO/B,OAAO,CAACmC,GAAR,CAAYF,QAAZ,CAAA,CAAsBG,IAAtB,8MAA2BC,OAA3B,CAAA,CAAiCC,KAAjC,8MAAuCD,OAAvC,CAAP,CAAA;IACD,CAAA;IAcD;;GAEF,GACEE,iBAAiB,CACf1D,IADe,EAEfC,IAFe,EAGfc,IAHe,EAIA;QACf,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,oNAAAA,kBAAAA,AAAe,EAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;QAEA,4NAAOuB,gBAAa,CAACC,KAAd,CAAoB,MAAM;YAAA,IAAA,IAAA,EAAA,oBAAA,CAAA;YAC/B,IAAKnE,CAAAA,UAAL,CAAgBiC,OAAhB,CAAwBH,OAAxB,CAAiCwC,CAAAA,OAAjC,EAA0Cb,KAAD,IAAW;gBAClDA,KAAK,CAAC8B,UAAN,EAAA,CAAA;aADF,CAAA,CAAA;YAIA,IAAIzD,OAAO,CAAC0D,WAAR,KAAwB,MAA5B,EAAoC;gBAClC,OAAOzC,OAAO,CAACC,OAAR,EAAP,CAAA;YACD,CAAA;YACD,MAAMyB,cAAmC,GAAG;gBAC1C,GAAG3C,OADuC;gBAE1C4C,IAAI,EAAA,CAAA,IAAA,GAAA,CAAA,oBAAA,GAAE5C,OAAO,CAAC0D,WAAV,KAAA,OAAA,uBAAyB1D,OAAO,CAAC4C,IAAjC,KAAyC,IAAA,GAAA,IAAA,GAAA,QAAA;aAF/C,CAAA;YAIA,OAAO,IAAA,CAAKE,cAAL,CAAoBH,cAApB,EAAoCjB,OAApC,CAAP,CAAA;QACD,CAbM,CAAP,CAAA;IAcD,CAAA;IAcD;;GAEF,GACEoB,cAAc,CACZhD,IADY,EAEZC,IAFY,EAGZc,IAHY,EAIG;QACf,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,oNAAAA,kBAAe,AAAfA,EAAgBH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;QAEA,MAAMqC,QAAQ,wNAAGd,gBAAa,CAACC,KAAd,CAAoB,IACnC,IAAKnE,CAAAA,UAAL,CACGiC,OADH,CACWH,OADX,EAEG2D,MAFH,EAEWhC,KAAD,GAAW,CAACA,KAAK,CAACiC,UAAN,EAFtB,CAAA,CAGGrC,GAHH,EAGQI,KAAD,IAAA;gBAAA,IAAA,qBAAA,CAAA;gBAAA,OACHA,KAAK,CAACkC,KAAN,CAAYjE,SAAZ,EAAuB;oBACrB,GAAG8B,OADkB;oBAErBoC,aAAa,EAAA,CAAA,wBAAEpC,OAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,OAAO,CAAEoC,aAAX,KAAA,OAAA,wBAA4B,IAFpB;oBAGrBC,IAAI,EAAE;wBAAEC,WAAW,EAAEhE,OAAO,CAACgE,WAAAA;oBAAvB,CAAA;gBAHe,CAAvB,CADG,CAAA;YAAA,CAHP,CADe,CAAjB,CAAA;QAaA,IAAIC,OAAO,GAAGhD,OAAO,CAACmC,GAAR,CAAYF,QAAZ,CAAsBG,CAAAA,IAAtB,CAA2BC,oNAA3B,CAAd,CAAA;QAEA,IAAI,CAAA,CAAC5B,OAAD,IAAA,IAAA,IAACA,OAAO,CAAEwC,YAAV,CAAJ,EAA4B;YAC1BD,OAAO,GAAGA,OAAO,CAACV,KAAR,8MAAcD,OAAd,CAAV,CAAA;QACD,CAAA;QAED,OAAOW,OAAP,CAAA;IACD,CAAA;IAyCD;;GAEF,GACE9C,UAAU,CAMRrB,IANQ,EAORC,IAPQ,EAaRc,IAbQ,EAiBQ;QAChB,MAAMC,aAAa,oNAAGC,iBAAc,AAAdA,EAAejB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;QACA,MAAMiB,gBAAgB,GAAG,IAAKC,CAAAA,mBAAL,CAAyBjB,aAAzB,CAAzB,CAFgB,CAAA,0DAAA;QAKhB,IAAI,OAAOgB,gBAAgB,CAACqC,KAAxB,KAAkC,WAAtC,EAAmD;YACjDrC,gBAAgB,CAACqC,KAAjB,GAAyB,KAAzB,CAAA;QACD,CAAA;QAED,MAAMxC,KAAK,GAAG,IAAA,CAAKzD,UAAL,CAAgB8D,KAAhB,CAAsB,IAAtB,EAA4BF,gBAA5B,CAAd,CAAA;QAEA,OAAOH,KAAK,CAACyC,aAAN,CAAoBtC,gBAAgB,CAACuC,SAArC,CAAA,GACH1C,KAAK,CAACkC,KAAN,CAAY/B,gBAAZ,CADG,GAEHb,OAAO,CAACC,OAAR,CAAgBS,KAAK,CAACjB,KAAN,CAAYC,IAA5B,CAFJ,CAAA;IAGD,CAAA;IAyCD;;GAEF,GACE2D,aAAa,CAMXxE,IANW,EAOXC,IAPW,EAaXc,IAbW,EAiBI;QACf,OAAO,IAAA,CAAKM,UAAL,CAAgBrB,IAAhB,EAA6BC,IAA7B,EAA0Cc,IAA1C,CACJwC,CAAAA,IADI,8MACCC,OADD,CAAA,CAEJC,KAFI,8MAEED,OAFF,CAAP,CAAA;IAGD,CAAA;IAyCD;;GAEF,GACEiB,kBAAkB,CAMhBzE,IANgB,EAShBC,IATgB,EAehBc,IAfgB,EAmBc;QAC9B,MAAMC,aAAa,oNAAGC,iBAAAA,AAAc,EAACjB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;QACAC,aAAa,CAAC0D,QAAd,IAAyBC,wPAAqB,AAArBA,EAAzB,CAAA;QAKA,OAAO,IAAKtD,CAAAA,UAAL,CAAgBL,aAAhB,CAAP,CAAA;IACD,CAAA;IAyCD;;GAEF,GACE4D,qBAAqB,CAMnB5E,IANmB,EASnBC,IATmB,EAenBc,IAfmB,EAmBJ;QACf,OAAO,IAAA,CAAK0D,kBAAL,CAAwBzE,IAAxB,EAAqCC,IAArC,EAAkDc,IAAlD,CACJwC,CAAAA,IADI,8MACCC,OADD,CAAA,CAEJC,KAFI,8MAEED,OAFF,CAAP,CAAA;IAGD,CAAA;IAEDjE,qBAAqB,GAAqB;QACxC,OAAO,IAAKjB,CAAAA,aAAL,CAAmBiB,qBAAnB,EAAP,CAAA;IACD,CAAA;IAEDiC,aAAa,GAAe;QAC1B,OAAO,IAAA,CAAKpD,UAAZ,CAAA;IACD,CAAA;IAEDyG,gBAAgB,GAAkB;QAChC,OAAO,IAAA,CAAKvG,aAAZ,CAAA;IACD,CAAA;IAEDwG,SAAS,GAAW;QAClB,OAAO,IAAA,CAAKtG,MAAZ,CAAA;IACD,CAAA;IAEDuG,iBAAiB,GAAmB;QAClC,OAAO,IAAA,CAAKrG,cAAZ,CAAA;IACD,CAAA;IAEDsG,iBAAiB,CAACpD,OAAD,EAAgC;QAC/C,IAAKlD,CAAAA,cAAL,GAAsBkD,OAAtB,CAAA;IACD,CAAA;IAEDqD,gBAAgB,CACdvE,QADc,EAEdkB,OAFc,EAGR;QACN,MAAMsD,MAAM,GAAG,IAAKvG,CAAAA,aAAL,CAAmBgC,IAAnB,EACZwE,CAAD,oNAAOC,eAAAA,AAAY,EAAC1E,QAAD,CAAZ,SAA2B0E,4NAAAA,AAAY,EAACD,CAAC,CAACzE,QAAH,CADjC,CAAf,CAAA;QAGA,IAAIwE,MAAJ,EAAY;YACVA,MAAM,CAACxG,cAAP,GAAwBkD,OAAxB,CAAA;QACD,CAFD,MAEO;YACL,IAAKjD,CAAAA,aAAL,CAAmB0G,IAAnB,CAAwB;gBAAE3E,QAAF;gBAAYhC,cAAc,EAAEkD,OAAAA;aAApD,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAED0D,gBAAgB,CACd5E,QADc,EAE6C;QAC3D,IAAI,CAACA,QAAL,EAAe;YACb,OAAOZ,SAAP,CAAA;QACD,CAH0D,CAAA,kCAAA;QAM3D,MAAMyF,qBAAqB,GAAG,IAAA,CAAK5G,aAAL,CAAmBgC,IAAnB,EAAyBwE,CAAD,oNACpDK,kBAAAA,AAAe,EAAC9E,QAAD,EAAWyE,CAAC,CAACzE,QAAb,CADa,CAA9B,CAN2D,CAAA,0CAAA;QAW3D,IAAI5B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;YACE,mDAAA;YACA,MAAMyG,gBAAgB,GAAG,IAAA,CAAK9G,aAAL,CAAmBkF,MAAnB,EAA2BsB,CAAD,oNACjDK,kBAAAA,AAAe,EAAC9E,QAAD,EAAWyE,CAAC,CAACzE,QAAb,CADQ,CAAzB,CAFyC,CAAA,kGAAA;YAMzC,IAAI+E,gBAAgB,CAACnF,MAAjB,GAA0B,CAA9B,EAAiC;gBAC/B,IAAK9B,CAAAA,MAAL,CAAYS,KAAZ,CAAA,uDAAA,GAC0DyG,IAAI,CAACC,SAAL,CACtDjF,QADsD,CAD1D,GAAA,gNAAA,CAAA,CAAA;YAKD,CAAA;QACF,CAAA;QAED,OAAO6E,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAE7G,cAA9B,CAAA;IACD,CAAA;IAEDkH,mBAAmB,CACjBC,WADiB,EAEjBjE,OAFiB,EAGX;QACN,MAAMsD,MAAM,GAAG,IAAKtG,CAAAA,gBAAL,CAAsB+B,IAAtB,EACZwE,CAAD,oNAAOC,eAAAA,AAAY,EAACS,WAAD,CAAZ,sNAA8BT,eAAAA,AAAY,EAACD,CAAC,CAACU,WAAH,CADpC,CAAf,CAAA;QAGA,IAAIX,MAAJ,EAAY;YACVA,MAAM,CAACxG,cAAP,GAAwBkD,OAAxB,CAAA;QACD,CAFD,MAEO;YACL,IAAKhD,CAAAA,gBAAL,CAAsByG,IAAtB,CAA2B;gBAAEQ,WAAF;gBAAenH,cAAc,EAAEkD,OAAAA;aAA1D,CAAA,CAAA;QACD,CAAA;IACF,CAAA;IAEDkE,mBAAmB,CACjBD,WADiB,EAEwC;QACzD,IAAI,CAACA,WAAL,EAAkB;YAChB,OAAO/F,SAAP,CAAA;QACD,CAHwD,CAAA,kCAAA;QAMzD,MAAMyF,qBAAqB,GAAG,IAAA,CAAK3G,gBAAL,CAAsB+B,IAAtB,CAA4BwE,CAAD,IACvDK,mOAAe,AAAfA,EAAgBK,WAAD,EAAcV,CAAC,CAACU,WAAhB,CADa,CAA9B,CANyD,CAAA,0CAAA;QAWzD,IAAI/G,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,WAAc,CAA3C;YACE,mDAAA;YACA,MAAMyG,gBAAgB,GAAG,IAAA,CAAK7G,gBAAL,CAAsBiF,MAAtB,EAA8BsB,CAAD,oNACpDK,kBAAAA,AAAe,EAACK,WAAD,EAAcV,CAAC,CAACU,WAAhB,CADQ,CAAzB,CAFyC,CAAA,kGAAA;YAMzC,IAAIJ,gBAAgB,CAACnF,MAAjB,GAA0B,CAA9B,EAAiC;gBAC/B,IAAK9B,CAAAA,MAAL,CAAYS,KAAZ,CAAA,0DAAA,GAC6DyG,IAAI,CAACC,SAAL,CACzDE,WADyD,CAD7D,GAAA,yNAAA,CAAA,CAAA;YAKD,CAAA;QACF,CAAA;QAED,OAAON,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAE7G,cAA9B,CAAA;IACD,CAAA;IAEDuD,mBAAmB,CAOjBL,OAPiB,EAsBjB;QACA,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEmE,UAAb,EAAyB;YACvB,OAAOnE,OAAP,CAAA;QAOD,CAAA;QAED,MAAMI,gBAAgB,GAAG;YACvB,GAAG,IAAKtD,CAAAA,cAAL,CAAoBsH,OADA;YAEvB,GAAG,IAAA,CAAKV,gBAAL,CAAsB1D,OAAtB,IAAA,OAAA,KAAA,IAAsBA,OAAO,CAAElB,QAA/B,CAFoB;YAGvB,GAAGkB,OAHoB;YAIvBmE,UAAU,EAAE,IAAA;SAJd,CAAA;QAOA,IAAI,CAAC/D,gBAAgB,CAACiE,SAAlB,IAA+BjE,gBAAgB,CAACtB,QAApD,EAA8D;YAC5DsB,gBAAgB,CAACiE,SAAjB,oNAA6BC,wBAAAA,AAAqB,EAChDlE,gBAAgB,CAACtB,QAD+B,EAEhDsB,gBAFgD,CAAlD,CAAA;QAID,CAvBD,CAAA,2BAAA;QA0BA,IAAI,OAAOA,gBAAgB,CAACmE,kBAAxB,KAA+C,WAAnD,EAAgE;YAC9DnE,gBAAgB,CAACmE,kBAAjB,GACEnE,gBAAgB,CAACoE,WAAjB,KAAiC,QADnC,CAAA;QAED,CAAA;QACD,IAAI,OAAOpE,gBAAgB,CAACqE,gBAAxB,KAA6C,WAAjD,EAA8D;YAC5DrE,gBAAgB,CAACqE,gBAAjB,GAAoC,CAAC,CAACrE,gBAAgB,CAACsE,QAAvD,CAAA;QACD,CAAA;QAED,OAAOtE,gBAAP,CAAA;IAOD,CAAA;IAEDuE,sBAAsB,CACpB3E,OADoB,EAEjB;QACH,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEmE,UAAb,EAAyB;YACvB,OAAOnE,OAAP,CAAA;QACD,CAAA;QACD,OAAO;YACL,GAAG,IAAKlD,CAAAA,cAAL,CAAoB8H,SADlB;YAEL,GAAG,IAAA,CAAKV,mBAAL,CAAyBlE,OAAzB,IAAA,OAAA,KAAA,IAAyBA,OAAO,CAAEiE,WAAlC,CAFE;YAGL,GAAGjE,OAHE;YAILmE,UAAU,EAAE,IAAA;SAJd,CAAA;IAMD,CAAA;IAEDU,KAAK,GAAS;QACZ,IAAKrI,CAAAA,UAAL,CAAgBqI,KAAhB,EAAA,CAAA;QACA,IAAKnI,CAAAA,aAAL,CAAmBmI,KAAnB,EAAA,CAAA;IACD,CAAA;AAn4BsB", "ignoreList": [0], "debugId": null}}]}