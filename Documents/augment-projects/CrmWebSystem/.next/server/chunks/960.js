"use strict";exports.id=960,exports.ids=[960],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(43210);n(60687);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}},363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(43210),i=0;function o(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},7044:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},9510:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(43210),i=n(11273),o=n(98599),a=n(8730),s=n(60687);function l(e){let t=e+"CollectionProvider",[n,l]=(0,i.A)(t),[u,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,i=r.useRef(null),o=r.useRef(new Map).current;return(0,s.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:n})};d.displayName=t;let h=e+"CollectionSlot",f=(0,a.TL)(h),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=c(h,n),a=(0,o.s)(t,i.collectionRef);return(0,s.jsx)(f,{ref:a,children:r})});p.displayName=h;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,a.TL)(m),y=r.forwardRef((e,t)=>{let{scope:n,children:i,...a}=e,l=r.useRef(null),u=(0,o.s)(t,l),d=c(m,n);return r.useEffect(()=>(d.itemMap.set(l,{ref:l,...a}),()=>void d.itemMap.delete(l))),(0,s.jsx)(g,{...{[v]:""},ref:u,children:i})});return y.displayName=m,[{Provider:d,Slot:p,ItemSlot:y},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},l]}var u=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},11096:(e,t,n)=>{n.d(t,{H4:()=>S,_V:()=>T,bL:()=>E});var r=n(43210),i=n(11273),o=n(13495),a=n(66156),s=n(14163),l=n(57379);function u(){return()=>{}}var c=n(60687),d="Avatar",[h,f]=(0,i.A)(d),[p,m]=h(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...i}=e,[o,a]=r.useState("idle");return(0,c.jsx)(p,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:(0,c.jsx)(s.sG.span,{...i,ref:t})})});v.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:i,onLoadingStatusChange:d=()=>{},...h}=e,f=m(g,n),p=function(e,{referrerPolicy:t,crossOrigin:n}){let i=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),o=r.useRef(null),s=i?(o.current||(o.current=new window.Image),o.current):null,[c,d]=r.useState(()=>x(s,e));return(0,a.N)(()=>{d(x(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!s)return;let r=e("loaded"),i=e("error");return s.addEventListener("load",r),s.addEventListener("error",i),t&&(s.referrerPolicy=t),"string"==typeof n&&(s.crossOrigin=n),()=>{s.removeEventListener("load",r),s.removeEventListener("error",i)}},[s,n,t]),c}(i,h),v=(0,o.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&v(p)},[p,v]),"loaded"===p?(0,c.jsx)(s.sG.img,{...h,ref:t,src:i}):null});y.displayName=g;var w="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:i,...o}=e,a=m(w,n),[l,u]=r.useState(void 0===i);return r.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>u(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(s.sG.span,{...o,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=w;var E=v,T=y,S=b},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>o});var r=n(43210),i=n(60687);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,a=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),s=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[s]||a,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[s]||a,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12157:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(43210);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>s});var r=n(43210),i=n(51215),o=n(8730),a=n(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},15124:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},16189:(e,t,n)=>{var r=n(65773);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},18171:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(74479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},18853:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(43210),i=n(66156);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},21279:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},25028:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(43210),i=n(51215),o=n(14163),a=n(66156),s=n(60687),l=r.forwardRef((e,t)=>{let{container:n,...l}=e,[u,c]=r.useState(!1);(0,a.N)(()=>c(!0),[]);let d=n||u&&globalThis?.document?.body;return d?i.createPortal((0,s.jsx)(o.sG.div,{...l,ref:t}),d):null});l.displayName="Portal"},26001:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function a(e,t,n,r){if("function"==typeof t){let[i,a]=o(r);t=t(void 0!==n?n:e.custom,i,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,a]=o(r);t=t(void 0!==n?n:e.custom,i,a)}return t}function s(e,t,n){let r=e.getProps();return a(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oC});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(s)}let c={schedule:(e,t=!1,o=!1)=>{let s=o&&i?n:r;return t&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(s=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:v,postRender:g}=a,y=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),v.process(i),g.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},w=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:d.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,i=!1)=>(n||w(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)a[d[t]].cancel(e)},state:i,steps:a}}let{schedule:p,cancel:m,state:v,steps:g}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],w=new Set(y),b=new Set(["width","height","top","left","right","bottom",...y]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function E(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class T{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>E(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){r=void 0}let P={now:()=>(void 0===r&&P.set(v.isProcessing||c.useManualTiming?v.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(S)}},A=e=>!isNaN(parseFloat(e)),C={current:void 0};class R{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=P.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=P.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=A(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new T);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return C.current&&C.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=P.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(e,t){return new R(e,t)}let k=e=>Array.isArray(e),D=e=>!!(e&&e.getVelocity);function j(e,t){let n=e.getValue("willChange");if(D(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let L=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),N="data-"+L("framerAppearId"),V=(e,t)=>n=>t(e(n)),O=(...e)=>e.reduce(V),F=(e,t,n)=>n>t?t:n<e?e:n,I=e=>1e3*e,B=e=>e/1e3,_={layout:0,mainThread:0,waapi:0},U=()=>{},$=()=>{},W=e=>t=>"string"==typeof t&&t.startsWith(e),z=W("--"),H=W("var(--"),Y=e=>!!H(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},K={...G,transform:e=>F(0,1,e)},q={...G,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,a,s]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},en=e=>F(0,255,e),er={...G,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(K.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},ea=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),es=ea("deg"),el=ea("%"),eu=ea("px"),ec=ea("vh"),ed=ea("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(K.transform(r))+")"},ep={test:e=>ei.test(e)||eo.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ev="number",eg="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ew(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=t.replace(ey,e=>(ep.test(e)?(r.color.push(o),i.push(eg),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(ev),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:a,indexes:r,types:i}}function eb(e){return ew(e).values}function ex(e){let{split:t,types:n}=ew(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===ev?i+=Z(e[o]):t===eg?i+=ep.transform(e[o]):i+=e[o]}return i}}let eE=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,eT={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:eb,createTransformer:ex,getAnimatableNone:function(e){let t=eb(e);return ex(e)(t.map(eE))}};function eS(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eP(e,t){return n=>n>0?t:e}let eA=(e,t,n)=>e+(t-e)*n,eC=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eR=[eo,ei,ef],eM=e=>eR.find(t=>t.test(e));function ek(e){let t=eM(e);if(U(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,a=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=eS(s,r,e+1/3),o=eS(s,r,e),a=eS(s,r,e-1/3)}else i=o=a=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:r}}(n)),n}let eD=(e,t)=>{let n=ek(e),r=ek(t);if(!n||!r)return eP(e,t);let i={...n};return e=>(i.red=eC(n.red,r.red,e),i.green=eC(n.green,r.green,e),i.blue=eC(n.blue,r.blue,e),i.alpha=eA(n.alpha,r.alpha,e),ei.transform(i))},ej=new Set(["none","hidden"]);function eL(e,t){return n=>eA(e,t,n)}function eN(e){return"number"==typeof e?eL:"string"==typeof e?Y(e)?eP:ep.test(e)?eD:eF:Array.isArray(e)?eV:"object"==typeof e?ep.test(e)?eD:eO:eP}function eV(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eN(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eO(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eN(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eF=(e,t)=>{let n=eT.createTransformer(t),r=ew(e),i=ew(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?ej.has(e)&&!i.values.length||ej.has(t)&&!r.values.length?function(e,t){return ej.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):O(eV(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][r[o]],s=e.values[a]??0;n[i]=s,r[o]++}return n}(r,i),i.values),n):(U(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eP(e,t))};function eI(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eA(e,t,n):eN(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>v.isProcessing?v.timestamp:P.now()}},e_=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eU(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function e$(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ez(e,t){return e*Math.sqrt(1-t*t)}let eH=["duration","bounce"],eY=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eG(e=eW.visualDuration,t=eW.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,a=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eY)&&eX(e,eH))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*F(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eW.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eW.duration,bounce:t=eW.bounce,velocity:n=eW.velocity,mass:r=eW.mass}){let i,o;U(e<=I(eW.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=F(eW.minDamping,eW.maxDamping,a),e=F(eW.minDuration,eW.maxDuration,B(e)),a<1?(i=t=>{let r=t*a,i=r*e;return .001-(r-n)/ez(t,a)*Math.exp(-i)},o=t=>{let r=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=Math.exp(-r),l=ez(Math.pow(t,2),a);return(r*n+n-o)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let s=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=I(e),isNaN(s))return{stiffness:eW.stiffness,damping:eW.damping,duration:e};{let t=Math.pow(s,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eW.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-B(r.velocity||0)}),m=f||0,v=c/(2*Math.sqrt(u*d)),g=s-a,y=B(Math.sqrt(u/d)),w=5>Math.abs(g);if(i||(i=w?eW.restSpeed.granular:eW.restSpeed.default),o||(o=w?eW.restDelta.granular:eW.restDelta.default),v<1){let e=ez(y,v);n=t=>s-Math.exp(-v*y*t)*((m+v*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===v)n=e=>s-Math.exp(-y*e)*(g+(m+y*g)*e);else{let e=y*Math.sqrt(v*v-1);n=t=>{let n=Math.exp(-v*y*t),r=Math.min(e*t,300);return s-n*((m+v*y*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let b={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;v<1&&(r=0===e?I(m):e$(n,e,t));let a=Math.abs(s-t)<=o;l.done=Math.abs(r)<=i&&a}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(eU(b),2e4),t=e_(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eK({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,v=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l,g=n*t,y=f+g,w=void 0===a?y:a(y);w!==y&&(g=w-f);let b=e=>-g*Math.exp(-e/r),x=e=>w+b(e),E=e=>{let t=b(e),n=x(e);p.done=Math.abs(t)<=u,p.value=p.done?w:n},T=e=>{m(p.value)&&(d=e,h=eG({keyframes:[p.value,v(p.value)],velocity:e$(x,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,E(e),T(e)),void 0!==d&&e>=d)?h.next(e-d):(t||E(e),p)}}}eG.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eU(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:B(i)}}(e,100,eG);return e.ease=t.ease,e.duration=I(t.duration),e.type="keyframes",e};let eq=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,a,s=0;do(o=eq(a=t+(n-t)/2,r,i)-e)>0?n=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,n);return e=>0===e||1===e?e:eq(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e3=eZ(.33,1.53,.69,.99),e4=e5(e3),e6=e2(e4),e9=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e8=e5(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e8,backIn:e4,backInOut:e6,backOut:e3,anticipate:e9},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){$(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?($(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ta({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),a={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if($(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,n){let r=[],i=n||c.mix||eI,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=O(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=s.length,d=n=>{if(a&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return s[r](i)};return n?t=>d(F(e[0],e[o-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eA(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=s(t),a.done=t>=e,a)}}let ts=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ts),a=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return a&&void 0!==r?r:o[a]}let tu={decay:eK,inertia:eK,tween:ta,keyframes:ta,spring:eG};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tf extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==P.now()&&this.tick(P.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},_.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ta,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:a}=e,s=t||ta;s!==ta&&"number"!=typeof a[0]&&(this.mixKeyframes=O(th,eI(a[0],a[1])),a=[0,100]);let l=s({...e,keyframes:a});"mirror"===i&&(this.mirroredGenerator=s({...e,keyframes:[...a].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=eU(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?v<0:v>r;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,w=n;if(c){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/a)):"mirror"===d&&(w=o)),y=F(0,1,n)*a}let b=g?{done:!1,value:u[0]}:w.next(y);i&&(b.value=i(b.value));let{done:x}=b;g||null===s||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return E&&f!==eK&&(b.value=tl(u,this.options,m,this.speed)),p&&p(b.value),E&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=I(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(P.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(P.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,_.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>tg(tp(Math.atan2(e[1],e[0]))),tv={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tg=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tw=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tw,scale:e=>(ty(e)+tw(e))/2,rotateX:e=>tg(tp(Math.atan2(e[6],e[5]))),rotateY:e=>tg(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tx(e){return+!!e.includes("scale")}function tE(e,t){let n,r;if(!e||"none"===e)return tx(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tb,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tv,r=t}if(!r)return tx(t);let o=n[t],a=r[1].split(",").map(tS);return"function"==typeof o?o(a):a[o]}let tT=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tE(n,t)};function tS(e){return parseFloat(e.trim())}let tP=e=>e===G||e===eu,tA=new Set(["x","y","z"]),tC=y.filter(e=>!tA.has(e)),tR={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tE(t,"x"),y:(e,{transform:t})=>tE(t,"y")};tR.translateX=tR.x,tR.translateY=tR.y;let tM=new Set,tk=!1,tD=!1,tj=!1;function tL(){if(tD){let e=Array.from(tM).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tC.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tD=!1,tk=!1,tM.forEach(e=>e.complete(tj)),tM.clear()}function tN(){tM.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tD=!0)})}class tV{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tM.add(this),tk||(tk=!0,p.read(tN),p.resolveKeyframes(tL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tM.delete(this)}cancel(){"scheduled"===this.state&&(tM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tO=e=>e.startsWith("--");function tF(e){let t;return()=>(void 0===t&&(t=e()),t)}let tI=tF(()=>void 0!==window.ScrollTimeline),tB={},t_=function(e,t){let n=tF(e);return()=>tB[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tU=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,t$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tU([0,.65,.55,1]),circOut:tU([.55,0,1,.45]),backIn:tU([.31,.01,.66,-.59]),backOut:tU([.33,1.53,.69,.99])};function tW(e){return"function"==typeof e&&"applyToOptions"in e}class tz extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,$("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tW(e)&&t_()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?t_()?e_(t,n):"ease-out":tt(t)?tU(t):Array.isArray(t)?t.map(t=>e(t,n)||t$.easeOut):t$[t]}(s,i);Array.isArray(d)&&(c.easing=d),h.value&&_.waapi++;let f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return h.value&&p.finished.finally(()=>{_.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tO(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=I(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tI())?(this.animation.timeline=e,u):t(this)}}let tH={anticipate:e9,backInOut:e6,circInOut:te};class tY extends tz{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tH&&(e.ease=tH[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tf({...o,autoplay:!1}),s=I(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eT.test(e)||"0"===e)&&!e.startsWith("url("));var tG,tK,tq=n(18171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=P.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tV;this.keyframeResolver=new h(a,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:d}=n;this.resolvedAt=P.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=tX(i,t),s=tX(o,t);return U(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tW(n))&&r)}(e,i,o,a)&&((c.instantAnimations||!s)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:a}=e;if(!(0,tq.s)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!s&&!r&&"mirror"!==i&&0!==o&&"inertia"!==a}(h)?new tY({...h,element:h.motionValue.owner.current}):new tf(h);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tj=!0,tN(),tL(),tj=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t5:w.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t6=(e,t,n,r={},i,o)=>a=>{let s=l(r,e)||{},u=s.delay||r.delay||0,{elapsed:d=0}=r;d-=I(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-d,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&Object.assign(h,t4(e,h)),h.duration&&(h.duration=I(h.duration)),h.repeatDelay&&(h.repeatDelay=I(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!s.type&&!s.ease,f&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,s);if(void 0!==e)return void p.update(()=>{h.onUpdate(e),h.onComplete()})}return s.isSync?new tf(h):new tJ(h)};function t9(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...u}=t;r&&(o=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let a={delay:n,...l(o||{},t)},s=r.get();if(void 0!==s&&!r.isAnimating&&!Array.isArray(i)&&i===s&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[N];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(a.startTime=e,h=!0)}}j(e,t),r.start(t6(t,r,i,e.shouldReduceMotion&&b.has(t)?{type:!1}:a,e,h));let f=r.animation;f&&c.push(f)}return a&&Promise.all(c).then(()=>{p.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=s(e,t)||{};for(let t in i={...i,...n}){var o;let n=k(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,M(n))}}(e,a)})}),c}function t7(e,t,n={}){let r=s(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t9(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,n=0,r=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(t8).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(t7(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+r,a,s,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),a(n.delay)]);{let[e,t]="beforeChildren"===l?[o,a]:[a,o];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,no=[...nn].reverse(),na=nn.length;function ns(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:ns(!0),whileInView:ns(),whileHover:ns(),whileTap:ns(),whileDrag:ns(),whileFocus:ns(),exit:ns()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t7(e,t,n)));else if("string"==typeof t)r=t7(e,t,n);else{let i="function"==typeof t?s(e,t,n.custom):t;r=Promise.all(t9(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,o=t=>(n,r)=>{let i=s(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function a(a){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},f=1/0;for(let t=0;t<na;t++){var p,m;let s=no[t],v=n[s],g=void 0!==l[s]?l[s]:u[s],y=nt(g),w=s===a?v.isActive:null;!1===w&&(f=t);let b=g===u[s]&&g!==l[s]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),v.protectedKeys={...h},!v.isActive&&null===w||!g&&!v.prevProp||i(g)||"boolean"==typeof g)continue;let x=(p=v.prevProp,"string"==typeof(m=g)?m!==p:!!Array.isArray(m)&&!ne(m,p)),E=x||s===a&&v.isActive&&!b&&y||t>f&&y,T=!1,S=Array.isArray(g)?g:[g],P=S.reduce(o(s),{});!1===w&&(P={});let{prevResolvedValues:A={}}=v,C={...A,...P},R=t=>{E=!0,d.has(t)&&(T=!0,d.delete(t)),v.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in C){let t=P[e],n=A[e];if(h.hasOwnProperty(e))continue;let r=!1;(k(t)&&k(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?R(e):v.protectedKeys[e]=!0:null!=t?R(e):d.add(e)}v.prevProp=g,v.prevResolvedValues=P,v.isActive&&(h={...h,...P}),r&&e.blockInitialAnimation&&(E=!1);let M=!(b&&x)||T;E&&M&&c.push(...S.map(e=>({animation:e,options:{type:s}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=s(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let v=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(c):Promise.resolve()}return{animateChanges:a,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=a(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function nv(e){return{point:{x:e.pageX,y:e.pageY}}}let ng=e=>t=>nm(t)&&e(t,nv(t));function ny(e,t,n,r){return np(e,t,ng(n),r)}function nw({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nb(e){return e.max-e.min}function nx(e,t,n,r=.5){e.origin=r,e.originPoint=eA(t.min,t.max,e.origin),e.scale=nb(n)/nb(t),e.translate=eA(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nE(e,t,n,r){nx(e.x,t.x,n.x,r?r.originX:void 0),nx(e.y,t.y,n.y,r?r.originY:void 0)}function nT(e,t,n){e.min=n.min+t.min,e.max=e.min+nb(t)}function nS(e,t,n){e.min=t.min-n.min,e.max=e.min+nb(t)}function nP(e,t,n){nS(e.x,t.x,n.x),nS(e.y,t.y,n.y)}let nA=()=>({translate:0,scale:1,origin:0,originPoint:0}),nC=()=>({x:nA(),y:nA()}),nR=()=>({min:0,max:0}),nM=()=>({x:nR(),y:nR()});function nk(e){return[e("x"),e("y")]}function nD(e){return void 0===e||1===e}function nj({scale:e,scaleX:t,scaleY:n}){return!nD(e)||!nD(t)||!nD(n)}function nL(e){return nj(e)||nN(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nN(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nV(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nO(e,t=0,n=1,r,i){e.min=nV(e.min,t,n,r,i),e.max=nV(e.max,t,n,r,i)}function nF(e,{x:t,y:n}){nO(e.x,t.translate,t.scale,t.originPoint),nO(e.y,n.translate,n.scale,n.originPoint)}function nI(e,t){e.min=e.min+t,e.max=e.max+t}function nB(e,t,n,r,i=.5){let o=eA(e.min,e.max,i);nO(e,t,n,o,r)}function n_(e,t){nB(e.x,t.x,t.scaleX,t.scale,t.originX),nB(e.y,t.y,t.scaleY,t.scale,t.originY)}function nU(e,t){return nw(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let n$=({current:e})=>e?e.ownerDocument.defaultView:null;function nW(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nz=(e,t)=>Math.abs(e-t);class nH{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nz(e.x,t.x)**2+nz(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=v;this.history.push({...r,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nY(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nG("pointercancel"===e.type?this.lastMoveEventInfo:nY(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nY(nv(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=v;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,nG(o,this.history)),this.removeListeners=O(ny(this.contextWindow,"pointermove",this.handlePointerMove),ny(this.contextWindow,"pointerup",this.handlePointerUp),ny(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nY(e,t){return t?{point:t(e.point)}:e}function nX(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nG({point:e},t){return{point:e,delta:nX(e,nK(t)),offset:nX(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nK(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>I(.1)));)n--;if(!r)return{x:0,y:0};let o=B(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function nK(e){return e[e.length-1]}function nq(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nM(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nH(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nv(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nk(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nb(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),j(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nk(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:n$(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eA(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eA(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nW(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nq(e.x,n,i),y:nq(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nk(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nW(t))return!1;let r=t.current;$(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nU(e,n),{scroll:i}=t;return i&&(nI(r.x,i.offset.x),nI(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),a=(e=i.layout.layoutBox,{x:nZ(e.x,o.x),y:nZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=nw(e))}return a}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(nk(a=>{if(!n2(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return j(this.visualElement,e),n.start(t6(e,n,0,t,this.visualElement,!1))}stopAnimation(){nk(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nk(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nk(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eA(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nW(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nk(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nb(e),i=nb(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),F(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nk(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eA(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=ny(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nW(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nk(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n5 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n3=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n4 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nH(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n$(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n3(e),onStart:n3(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ny(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n6=n(60687);let{schedule:n9}=f(queueMicrotask,!1);var n7=n(43210),n8=n(86044),re=n(12157);let rt=(0,n7.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ro={};class ra extends n7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ro[e]=rl[e],z(e)&&(ro[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||p.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n9.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rs(e){let[t,n]=(0,n8.xQ)(),r=(0,n7.useContext)(re.L);return(0,n6.jsx)(ra,{...e,layoutGroup:r,switchLayoutGroup:(0,n7.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eT.parse(e);if(r.length>5)return e;let i=eT.createTransformer(e),o=+("number"!=typeof r[0]),a=n.x.scale*t.x,s=n.y.scale*t.y;r[0+o]/=a,r[1+o]/=s;let l=eA(a,s,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var ru=n(74479);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rh{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){E(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rf(e){return D(e)?e.get():e}let rp=["TopLeft","TopRight","BottomLeft","BottomRight"],rm=rp.length,rv=e=>"string"==typeof e?parseFloat(e):e,rg=e=>"number"==typeof e||eu.test(e);function ry(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rw=rx(0,.5,e8),rb=rx(.5,.95,u);function rx(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rE(e,t){e.min=t.min,e.max=t.max}function rT(e,t){rE(e.x,t.x),rE(e.y,t.y)}function rS(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rP(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rA(e,t,[n,r,i],o,a){!function(e,t=0,n=1,r=.5,i,o=e,a=e){if(el.test(t)&&(t=parseFloat(t),t=eA(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=eA(o.min,o.max,r);e===o&&(s-=t),e.min=rP(e.min,t,n,s,i),e.max=rP(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,o,a)}let rC=["x","scaleX","originX"],rR=["y","scaleY","originY"];function rM(e,t,n,r){rA(e.x,t,rC,n?n.x:void 0,r?r.x:void 0),rA(e.y,t,rR,n?n.y:void 0,r?r.y:void 0)}function rk(e){return 0===e.translate&&1===e.scale}function rD(e){return rk(e.x)&&rk(e.y)}function rj(e,t){return e.min===t.min&&e.max===t.max}function rL(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rN(e,t){return rL(e.x,t.x)&&rL(e.y,t.y)}function rV(e){return nb(e.x)/nb(e.y)}function rO(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rF{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(E(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rB=["","X","Y","Z"],r_={visibility:"hidden"},rU=0;function r$(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rW({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rU++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rI.nodes=rI.calculatedTargetDeltas=rI.calculatedProjections=0),this.nodes.forEach(rY),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rX),h.addProjectionMetrics&&h.addProjectionMetrics(rI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new T),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=P.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(m(r),e(o-t))};return p.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r6,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=i.getProps(),u=!this.targetLayout||!rN(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:a,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[N];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rK);return}this.isUpdating||this.nodes.forEach(rq),this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(rz),this.nodes.forEach(rH),this.clearAllSnapshots();let e=P.now();v.delta=F(0,1e3/60,e-v.timestamp),v.timestamp=e,v.isProcessing=!0,g.update.process(v),g.preRender.process(v),g.render.process(v),v.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rG),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nb(this.snapshot.measuredBox.x)||nb(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rD(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nL(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nM();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nI(t.x,e.offset.x),nI(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nM();if(rT(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rT(t,e),nI(t.x,i.offset.x),nI(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nM();rT(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&n_(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nL(r.latestValues)&&n_(n,r.latestValues)}return nL(this.latestValues)&&n_(n,this.latestValues),n}removeTransform(e){let t=nM();rT(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nL(n.latestValues))continue;nj(n.latestValues)&&n.updateSnapshot();let r=nM();rT(r,n.measurePageBox()),rM(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nL(this.latestValues)&&rM(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==v.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=v.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nM(),this.relativeTargetOrigin=nM(),nP(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nM(),this.targetWithTransforms=nM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,nT(o.x,a.x,s.x),nT(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rT(this.target,this.layout.layoutBox),nF(this.target,this.targetDelta)):rT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nM(),this.relativeTargetOrigin=nM(),nP(this.relativeTargetOrigin,this.target,e.target),rT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rI.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nj(this.parent.latestValues)||nN(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===v.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rT(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(e,t,n,r=!1){let i,o,a=n.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=n[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&n_(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nF(e,o)),r&&nL(i.latestValues)&&n_(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nM());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rS(this.prevProjectionDelta.x,this.projectionDelta.x),rS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nE(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rO(this.projectionDelta.x,this.prevProjectionDelta.x)&&rO(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),h.value&&rI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nC(),this.projectionDelta=nC(),this.projectionDeltaWithTransform=nC()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},a=nC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=nM(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r4));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(a.x,e.x,r),r5(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,v;nP(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,v=r,r3(f.x,p.x,m.x,v),r3(f.y,p.y,m.y,v),n&&(u=this.relativeTarget,h=n,rj(u.x,h.x)&&rj(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nM()),rT(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eA(0,n.opacity??1,rw(r)),e.opacityExit=eA(t.opacity??1,0,rb(r))):o&&(e.opacity=eA(t.opacity??1,n.opacity??1,r));for(let i=0;i<rm;i++){let o=`border${rp[i]}Radius`,a=ry(t,o),s=ry(n,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||rg(a)===rg(s)?(e[o]=Math.max(eA(rv(a),rv(s),r),0),(el.test(s)||el.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||n.rotate)&&(e.rotate=eA(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,_.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(e,t,n){let r=D(e)?e:M(e);return r.start(t6("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{_.layout--},onComplete:()=>{_.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nM();let t=nb(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nb(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rT(t,n),n_(t,i),nE(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rF),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&r$("z",e,r,this.animationValues);for(let t=0;t<rB.length;t++)r$(`rotate${rB[t]}`,e,r,this.animationValues),r$(`skew${rB[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return r_;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rf(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rf(e?.pointerEvents)||""),this.hasProjected&&!nL(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=n?.z||0;if((i||o||a)&&(r=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),a&&(r+=`skewX(${a}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(r+=`scale(${s}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ro){if(void 0===i[e])continue;let{correct:n,applyTo:o,isCSSVariable:a}=ro[e],s="none"===t.transform?i[e]:n(i[e],r);if(o){let e=o.length;for(let n=0;n<e;n++)t[o[n]]=s}else a?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=r===this?rf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rK),this.root.sharedNodes.clear()}}}function rz(e){e.updateLayout()}function rH(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?nk(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nb(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nk(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],a=nb(n[r]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=nC();nE(a,n,t.layoutBox);let s=nC();o?nE(s,e.applyTransform(r,!0),t.measuredBox):nE(s,n,t.layoutBox);let l=!rD(a),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let a=nM();nP(a,t.layoutBox,i.layoutBox);let s=nM();nP(s,n,o.layoutBox),rN(a,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rY(e){h.value&&rI.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rX(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rG(e){e.clearSnapshot()}function rK(e){e.clearMeasurements()}function rq(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r5(e,t,n){e.translate=eA(t.translate,0,n),e.scale=eA(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r3(e,t,n,r){e.min=eA(t.min,n.min,r),e.max=eA(t.max,n.max,r)}function r4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r6={duration:.45,ease:[.4,0,.1,1]},r9=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r7=r9("applewebkit/")&&!r9("chrome/")?Math.round:u;function r8(e){e.min=r7(e.min),e.max=r7(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rV(t)-rV(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rW({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=rW({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ia(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function is(e){return!("touch"===e.pointerType||nf.x||nf.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,nv(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ia(e,n),a=e=>{if(!is(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{is(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function iv(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let ig=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;iv(n,"down");let e=im(()=>{iv(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>iv(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iy(e){return nm(e)&&!(nf.x||nf.y)}function iw(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,nv(t)))}class ib extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ia(e,n),a=e=>{let r=e.currentTarget;if(!iy(e))return;ip.add(r);let o=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iy(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),(0,tq.s)(e))&&(e.addEventListener("focus",e=>ig(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(iw(this.node,t,"Start"),(e,{success:t})=>iw(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iE=new WeakMap,iT=e=>{let t=ix.get(e.target);t&&t(e)},iS=e=>{e.forEach(iT)},iP={some:0,all:1};class iA extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iP[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;iE.has(n)||iE.set(n,{});let r=iE.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iS,{root:e,...t})),r[i]}(t);return ix.set(e,n),r.observe(e),()=>{ix.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iC=(0,n7.createContext)({strict:!1});var iR=n(32582);let iM=(0,n7.createContext)({});function ik(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iD(e){return!!(ik(e)||e.variants)}function ij(e){return Array.isArray(e)?e.join(" "):e}var iL=n(7044);let iN={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iV={};for(let e in iN)iV[e]={isEnabled:t=>iN[e].some(e=>!!t[e])};let iO=Symbol.for("motionComponentSymbol");var iF=n(21279),iI=n(15124);function iB(e,{layout:t,layoutId:n}){return w.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ro[e]||"opacity"===e)}let i_=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iU={...G,transform:Math.round},i$={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:es,rotateX:es,rotateY:es,rotateZ:es,scale:q,scaleX:q,scaleY:q,scaleZ:q,skew:es,skewX:es,skewY:es,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:K,originX:eh,originY:eh,originZ:eu,zIndex:iU,fillOpacity:K,strokeOpacity:K,numOctaves:iU},iW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iz=y.length;function iH(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let n=t[e];if(w.has(e)){a=!0;continue}if(z(e)){i[e]=n;continue}{let t=i_(n,i$[e]);e.startsWith("origin")?(s=!0,o[e]=t):r[e]=t}}if(!t.transform&&(a||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<iz;o++){let a=y[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||n){let e=i_(s,i$[a]);if(!l){i=!1;let t=iW[a]||a;r+=`${t}(${e}) `}n&&(t[a]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,n){for(let r in t)D(t[r])||iB(r,n)||(e[r]=t[r])}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iK={offset:"strokeDashoffset",array:"strokeDasharray"};function iq(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(iH(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iG:iK;e[o.offset]=eu.transform(-r);let a=eu.transform(t),s=eu.transform(n);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let iZ=()=>({...iY(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i3=n(72789);let i4=e=>(t,n)=>{let r=(0,n7.useContext)(iM),o=(0,n7.useContext)(iF.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},s=r(e,{});for(let e in s)o[e]=rf(s[e]);let{initial:l,animate:u}=e,c=ik(e),d=iD(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=a(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?s():(0,i3.M)(s)};function i6(e,t,n){let{style:r}=e,i={};for(let o in r)(D(r[o])||t.style&&D(t.style[o])||iB(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i9={useVisualState:i4({scrapeMotionValuesFromProps:i6,createRenderState:iY})};function i7(e,t,n){let r=i6(e,t,n);for(let n in e)(D(e[n])||D(t[n]))&&(r[-1!==y.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[G,eu,el,es,ed,ec,{test:e=>"auto"===e,parse:e=>e}],on=e=>ot.find(oe(e)),or=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),oa=new Set(["brightness","contrast","saturate","opacity"]);function os(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),o=+!!oa.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eT,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(os).join(" "):e}},oc={...i$,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:ou,WebkitFilter:ou},od=e=>oc[e];function oh(e,t){let n=od(e);return n!==ou&&(n=eT),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let of=new Set(["auto","none","0"]);class op extends tV{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&Y(r=r.trim())){let i=function e(t,n,r=1){$(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let a=window.getComputedStyle(n).getPropertyValue(i);if(a){let e=a.trim();return or(e)?parseFloat(e):e}return Y(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!b.has(n)||2!==e.length)return;let[r,i]=e,o=on(r),a=on(i);if(o!==a)if(tP(o)&&tP(a))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tR[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||oo(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!of.has(t)&&ew(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oh(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tR[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tR[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let om=[...ot,ep,eT],ov=e=>om.find(oe(e)),og={current:null},oy={current:!1},ow=new WeakMap,ob=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ox{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=P.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=ik(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==s[e]&&D(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ow.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oy.current||function(){if(oy.current=!0,iL.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>og.current=e.matches;e.addListener(t),t()}else og.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||og.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=w.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iV){let t=iV[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nM()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ob.length;t++){let n=ob[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(D(i))e.addValue(r,i);else if(D(o))e.addValue(r,M(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,M(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=M(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(or(n)||oo(n))?n=parseFloat(n):!ov(n)&&eT.test(t)&&(n=oh(e,t)),this.setBaseTarget(e,D(n)?n.get():n)),D(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=a(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||D(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new T),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oE extends ox{constructor(){super(...arguments),this.KeyframeResolver=op}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oT(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}class oS extends oE{constructor(){super(...arguments),this.type="html",this.renderInstance=oT}readValueFromInstance(e,t){if(w.has(t))return this.projection?.isProjecting?tx(t):tT(e,t);{let n=window.getComputedStyle(e),r=(z(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nU(e,t)}build(e,t,n){iH(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}}let oP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oA extends oE{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nM}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(w.has(t)){let e=od(t);return e&&e.default||0}return t=oP.has(t)?t:L(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i7(e,t,n)}build(e,t,n){iq(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in oT(e,t,void 0,r),t.attrs)e.setAttribute(oP.has(n)?n:L(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let oC=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tG={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iA},tap:{Feature:ib},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n4},drag:{Feature:n5,ProjectionNode:io,MeasureLayout:rs},layout:{ProjectionNode:io,MeasureLayout:rs}},tK=(e,t)=>i5(e)?new oA(t):new oS(t,{allowProjection:e!==n7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function o(e,o){var a,s,l;let u,c={...(0,n7.useContext)(iR.Q),...e,layoutId:function({layoutId:e}){let t=(0,n7.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(ik(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n7.useContext)(iM));return(0,n7.useMemo)(()=>({initial:t,animate:n}),[ij(t),ij(n)])}(e),f=r(e,d);if(!d&&iL.B){s=0,l=0,(0,n7.useContext)(iC).strict;let e=function(e){let{drag:t,layout:n}=iV;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n7.useContext)(iM),a=(0,n7.useContext)(iC),s=(0,n7.useContext)(iF.t),l=(0,n7.useContext)(iR.Q).reducedMotion,u=(0,n7.useRef)(null);r=r||a.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n7.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&nW(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n7.useRef)(!1);(0,n7.useInsertionEffect)(()=>{c&&h.current&&c.update(n,s)});let f=n[N],p=(0,n7.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iI.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n9.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n7.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,n6.jsxs)(iM.Provider,{value:h,children:[u&&h.visualElement?(0,n6.jsx)(u,{visualElement:h.visualElement,...c}):null,n(i,e,(a=h.visualElement,(0,n7.useCallback)(e=>{e&&f.onMount&&f.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):nW(o)&&(o.current=e))},[a])),f,d,h.visualElement)]})}e&&function(e){for(let t in e)iV[t]={...iV[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let a=(0,n7.forwardRef)(o);return a[iO]=i,a}({...i5(e)?i8:i9,preloadedFeatures:tG,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let a=(i5(t)?function(e,t,n,r){let i=(0,n7.useMemo)(()=>{let n=iZ();return iq(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n7.useMemo)(()=>{let n=iY();return iH(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),s=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n7.Fragment?{...s,...a,ref:r}:{},{children:u}=n,c=(0,n7.useMemo)(()=>D(u)?u.get():u,[u]);return(0,n7.createElement)(t,{...l,children:c})}}(t),createVisualElement:tK,Component:e})}))},26134:(e,t,n)=>{n.d(t,{UC:()=>et,ZL:()=>J,bL:()=>Z,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>Q});var r=n(43210),i=n(70569),o=n(98599),a=n(11273),s=n(96963),l=n(65551),u=n(31355),c=n(32547),d=n(25028),h=n(46059),f=n(14163),p=n(1359),m=n(42247),v=n(63376),g=n(8730),y=n(60687),w="Dialog",[b,x]=(0,a.A)(w),[E,T]=b(w),S=e=>{let{__scopeDialog:t,children:n,open:i,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[h,f]=(0,l.i)({prop:i,defaultProp:o??!1,onChange:a,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};S.displayName=w;var P="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(P,n),s=(0,o.s)(t,a.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":H(a.open),...r,ref:s,onClick:(0,i.m)(e.onClick,a.onOpenToggle)})});A.displayName=P;var C="DialogPortal",[R,M]=b(C,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:n,children:i,container:o}=e,a=T(C,t);return(0,y.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(i,e=>(0,y.jsx)(h.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:o,children:e})}))})};k.displayName=C;var D="DialogOverlay",j=r.forwardRef((e,t)=>{let n=M(D,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=T(D,e.__scopeDialog);return o.modal?(0,y.jsx)(h.C,{present:r||o.open,children:(0,y.jsx)(N,{...i,ref:t})}):null});j.displayName=D;var L=(0,g.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=T(D,n);return(0,y.jsx)(m.A,{as:L,allowPinchZoom:!0,shards:[i.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":H(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),V="DialogContent",O=r.forwardRef((e,t)=>{let n=M(V,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=T(V,e.__scopeDialog);return(0,y.jsx)(h.C,{present:r||o.open,children:o.modal?(0,y.jsx)(F,{...i,ref:t}):(0,y.jsx)(I,{...i,ref:t})})});O.displayName=V;var F=r.forwardRef((e,t)=>{let n=T(V,e.__scopeDialog),a=r.useRef(null),s=(0,o.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=r.forwardRef((e,t)=>{let n=T(V,e.__scopeDialog),i=r.useRef(!1),o=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(i.current||n.triggerRef.current?.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,d=T(V,n),h=r.useRef(null),f=(0,o.s)(t,h);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":H(d.open),...l,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(K,{titleId:d.titleId}),(0,y.jsx)(q,{contentRef:h,descriptionId:d.descriptionId})]})]})}),_="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=T(_,n);return(0,y.jsx)(f.sG.h2,{id:i.titleId,...r,ref:t})});U.displayName=_;var $="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=T($,n);return(0,y.jsx)(f.sG.p,{id:i.descriptionId,...r,ref:t})}).displayName=$;var W="DialogClose",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=T(W,n);return(0,y.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}z.displayName=W;var Y="DialogTitleWarning",[X,G]=(0,a.q)(Y,{contentName:V,titleName:_,docsSlug:"dialog"}),K=({titleId:e})=>{let t=G(Y),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},q=({contentRef:e,descriptionId:t})=>{let n=G("DialogDescriptionWarning"),i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(i))},[i,e,t]),null},Z=S,Q=A,J=k,ee=j,et=O,en=U,er=z},26312:(e,t,n)=>{n.d(t,{UC:()=>eG,q7:()=>eq,JU:()=>eK,ZL:()=>eX,bL:()=>eH,wv:()=>eZ,l9:()=>eY});var r=n(43210),i=n(70569),o=n(98599),a=n(11273),s=n(65551),l=n(14163),u=n(9510),c=n(43),d=n(31355),h=n(1359),f=n(32547),p=n(96963),m=n(55509),v=n(25028),g=n(46059),y=n(72942),w=n(8730),b=n(13495),x=n(63376),E=n(42247),T=n(60687),S=["Enter"," "],P=["ArrowUp","PageDown","End"],A=["ArrowDown","PageUp","Home",...P],C={ltr:[...S,"ArrowRight"],rtl:[...S,"ArrowLeft"]},R={ltr:["ArrowLeft"],rtl:["ArrowRight"]},M="Menu",[k,D,j]=(0,u.N)(M),[L,N]=(0,a.A)(M,[j,m.Bk,y.RG]),V=(0,m.Bk)(),O=(0,y.RG)(),[F,I]=L(M),[B,_]=L(M),U=e=>{let{__scopeMenu:t,open:n=!1,children:i,dir:o,onOpenChange:a,modal:s=!0}=e,l=V(t),[u,d]=r.useState(null),h=r.useRef(!1),f=(0,b.c)(a),p=(0,c.jH)(o);return r.useEffect(()=>{let e=()=>{h.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>h.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,T.jsx)(m.bL,{...l,children:(0,T.jsx)(F,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:d,children:(0,T.jsx)(B,{scope:t,onClose:r.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:h,dir:p,modal:s,children:i})})})};U.displayName=M;var $=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=V(n);return(0,T.jsx)(m.Mz,{...i,...r,ref:t})});$.displayName="MenuAnchor";var W="MenuPortal",[z,H]=L(W,{forceMount:void 0}),Y=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:i}=e,o=I(W,t);return(0,T.jsx)(z,{scope:t,forceMount:n,children:(0,T.jsx)(g.C,{present:n||o.open,children:(0,T.jsx)(v.Z,{asChild:!0,container:i,children:r})})})};Y.displayName=W;var X="MenuContent",[G,K]=L(X),q=r.forwardRef((e,t)=>{let n=H(X,e.__scopeMenu),{forceMount:r=n.forceMount,...i}=e,o=I(X,e.__scopeMenu),a=_(X,e.__scopeMenu);return(0,T.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,T.jsx)(g.C,{present:r||o.open,children:(0,T.jsx)(k.Slot,{scope:e.__scopeMenu,children:a.modal?(0,T.jsx)(Z,{...i,ref:t}):(0,T.jsx)(Q,{...i,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=I(X,e.__scopeMenu),a=r.useRef(null),s=(0,o.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,T.jsx)(ee,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let n=I(X,e.__scopeMenu);return(0,T.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:S,...C}=e,R=I(X,n),M=_(X,n),k=V(n),j=O(n),L=D(n),[N,F]=r.useState(null),B=r.useRef(null),U=(0,o.s)(t,B,R.onContentChange),$=r.useRef(0),W=r.useRef(""),z=r.useRef(0),H=r.useRef(null),Y=r.useRef("right"),K=r.useRef(0),q=S?E.A:r.Fragment,Z=e=>{let t=W.current+e,n=L().filter(e=>!e.disabled),r=document.activeElement,i=n.find(e=>e.ref.current===r)?.textValue,o=function(e,t,n){var r;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1,a=(r=Math.max(o,0),e.map((t,n)=>e[(r+n)%e.length]));1===i.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}(n.map(e=>e.textValue),t,i),a=n.find(e=>e.textValue===o)?.ref.current;!function e(t){W.current=t,window.clearTimeout($.current),""!==t&&($.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout($.current),[]),(0,h.Oh)();let Q=r.useCallback(e=>Y.current===H.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],s=t[o],l=a.x,u=a.y,c=s.x,d=s.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,H.current?.area),[]);return(0,T.jsx)(G,{scope:n,searchRef:W,onItemEnter:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:r.useCallback(e=>{Q(e)||(B.current?.focus(),F(null))},[Q]),onTriggerLeave:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:z,onPointerGraceIntentChange:r.useCallback(e=>{H.current=e},[]),children:(0,T.jsx)(q,{...S?{as:J,allowPinchZoom:!0}:void 0,children:(0,T.jsx)(f.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,i.m)(l,e=>{e.preventDefault(),B.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,T.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,T.jsx)(y.bL,{asChild:!0,...j,dir:M.dir,orientation:"vertical",loop:a,currentTabStopId:N,onCurrentTabStopIdChange:F,onEntryFocus:(0,i.m)(p,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,T.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eA(R.open),"data-radix-menu-content":"",dir:M.dir,...k,...C,ref:U,style:{outline:"none",...C.style},onKeyDown:(0,i.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Z(e.key));let i=B.current;if(e.target!==i||!A.includes(e.key))return;e.preventDefault();let o=L().filter(e=>!e.disabled).map(e=>e.ref.current);P.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout($.current),W.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eM(e=>{let t=e.target,n=K.current!==e.clientX;e.currentTarget.contains(t)&&n&&(Y.current=e.clientX>K.current?"right":"left",K.current=e.clientX)}))})})})})})})});q.displayName=X;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",ei="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...s}=e,u=r.useRef(null),c=_(er,e.__scopeMenu),d=K(er,e.__scopeMenu),h=(0,o.s)(t,u),f=r.useRef(!1);return(0,T.jsx)(ea,{...s,ref:h,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>a?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),f.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||S.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=er;var ea=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:s,...u}=e,c=K(er,n),d=O(n),h=r.useRef(null),f=(0,o.s)(t,h),[p,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=h.current;e&&g((e.textContent??"").trim())},[u.children]),(0,T.jsx)(k.ItemSlot,{scope:n,disabled:a,textValue:s??v,children:(0,T.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,T.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,eM(e=>{a?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eM(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),es=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,T.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,T.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eC(n)?"mixed":n,...o,ref:t,"data-state":eR(n),onSelect:(0,i.m)(o.onSelect,()=>r?.(!!eC(n)||!n),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ec]=L(el,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...i}=e,o=(0,b.c)(r);return(0,T.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,T.jsx)(et,{...i,ref:t})})});ed.displayName=el;var eh="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,o=ec(eh,e.__scopeMenu),a=n===o.value;return(0,T.jsx)(em,{scope:e.__scopeMenu,checked:a,children:(0,T.jsx)(eo,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eR(a),onSelect:(0,i.m)(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=eh;var ep="MenuItemIndicator",[em,ev]=L(ep,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...i}=e,o=ev(ep,n);return(0,T.jsx)(g.C,{present:r||eC(o.checked)||!0===o.checked,children:(0,T.jsx)(l.sG.span,{...i,ref:t,"data-state":eR(o.checked)})})});eg.displayName=ep;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=V(n);return(0,T.jsx)(m.i3,{...i,...r,ref:t})});ew.displayName="MenuArrow";var[eb,ex]=L("MenuSub"),eE="MenuSubTrigger",eT=r.forwardRef((e,t)=>{let n=I(eE,e.__scopeMenu),a=_(eE,e.__scopeMenu),s=ex(eE,e.__scopeMenu),l=K(eE,e.__scopeMenu),u=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,h={__scopeMenu:e.__scopeMenu},f=r.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return r.useEffect(()=>f,[f]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,T.jsx)($,{asChild:!0,...h,children:(0,T.jsx)(ea,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":eA(n.open),...e,ref:(0,o.t)(t,s.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eM(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eM(e=>{f();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,i="right"===r,o=t[i?"left":"right"],a=t[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:o,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:o,y:t.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;e.disabled||r&&" "===t.key||C[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eT.displayName=eE;var eS="MenuSubContent",eP=r.forwardRef((e,t)=>{let n=H(X,e.__scopeMenu),{forceMount:a=n.forceMount,...s}=e,l=I(X,e.__scopeMenu),u=_(X,e.__scopeMenu),c=ex(eS,e.__scopeMenu),d=r.useRef(null),h=(0,o.s)(t,d);return(0,T.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,T.jsx)(g.C,{present:a||l.open,children:(0,T.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,T.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...s,ref:h,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=R[u.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eA(e){return e?"open":"closed"}function eC(e){return"indeterminate"===e}function eR(e){return eC(e)?"indeterminate":e?"checked":"unchecked"}function eM(e){return t=>"mouse"===t.pointerType?e(t):void 0}eP.displayName=eS;var ek="DropdownMenu",[eD,ej]=(0,a.A)(ek,[N]),eL=N(),[eN,eV]=eD(ek),eO=e=>{let{__scopeDropdownMenu:t,children:n,dir:i,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=eL(t),d=r.useRef(null),[h,f]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:l,caller:ek});return(0,T.jsx)(eN,{scope:t,triggerId:(0,p.B)(),triggerRef:d,contentId:(0,p.B)(),open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,T.jsx)(U,{...c,open:h,onOpenChange:f,dir:i,modal:u,children:n})})};eO.displayName=ek;var eF="DropdownMenuTrigger",eI=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,s=eV(eF,n),u=eL(n);return(0,T.jsx)($,{asChild:!0,...u,children:(0,T.jsx)(l.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,o.t)(t,s.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eI.displayName=eF;var eB=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eL(t);return(0,T.jsx)(Y,{...r,...n})};eB.displayName="DropdownMenuPortal";var e_="DropdownMenuContent",eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=eV(e_,n),s=eL(n),l=r.useRef(!1);return(0,T.jsx)(q,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...o,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=e_,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(et,{...i,...r,ref:t})}).displayName="DropdownMenuGroup";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(en,{...i,...r,ref:t})});e$.displayName="DropdownMenuLabel";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(eo,{...i,...r,ref:t})});eW.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(es,{...i,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(ed,{...i,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(ef,{...i,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(eg,{...i,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(ey,{...i,...r,ref:t})});ez.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(ew,{...i,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(eT,{...i,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,T.jsx)(eP,{...i,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eH=eO,eY=eI,eX=eB,eG=eU,eK=e$,eq=eW,eZ=ez},31355:(e,t,n)=>{n.d(t,{qW:()=>h});var r,i=n(43210),o=n(70569),a=n(14163),s=n(98599),l=n(13495),u=n(60687),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:h,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=i.useContext(d),[x,E]=i.useState(null),T=x?.ownerDocument??globalThis?.document,[,S]=i.useState({}),P=(0,s.s)(t,e=>E(e)),A=Array.from(b.layers),[C]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),R=A.indexOf(C),M=x?A.indexOf(x):-1,k=b.layersWithOutsidePointerEventsDisabled.size>0,D=M>=R,j=function(e,t=globalThis?.document){let n=(0,l.c)(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){p("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));D&&!n&&(m?.(e),g?.(e),e.defaultPrevented||y?.())},T),L=function(e,t=globalThis?.document){let n=(0,l.c)(e),r=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!r.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},T);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===b.layers.size-1&&(h?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},T),i.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),f(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[x,T,n,b]),i.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),f())},[x,b]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...w,ref:P,style:{pointerEvents:k?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function f(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,{discrete:r}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?(0,a.hO)(i,o):i.dispatchEvent(o)}h.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),o=(0,s.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),i=n(98599),o=n(14163),a=n(13495),s=n(60687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),T=r.useRef(null),S=(0,i.s)(t,e=>b(e)),P=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(P.paused||!w)return;let t=e.target;w.contains(t)?T.current=t:p(T.current,{select:!0})},t=function(e){if(P.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||p(T.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,P.paused]),r.useEffect(()=>{if(w){m.add(P);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(l,c);w.addEventListener(l,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(h(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(w))}return()=>{w.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(u,c);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),w.removeEventListener(u,E),m.remove(P)},0)}}},[w,x,E,P]);let A=r.useCallback(e=>{if(!n&&!d||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[i,o]=function(e){let t=h(e);return[f(t,e),f(t.reverse(),e)]}(t);i&&o?e.shiftKey||r!==o?e.shiftKey&&r===i&&(e.preventDefault(),n&&p(o,{select:!0})):(e.preventDefault(),n&&p(i,{select:!0})):r===t&&e.preventDefault()}},[n,d,P.paused]);return(0,s.jsx)(o.sG.div,{tabIndex:-1,...y,ref:S,onKeyDown:A})});function h(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},32582:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},35020:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("folder-kanban",[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z",key:"1fr9dc"}],["path",{d:"M8 10v4",key:"tgpxqk"}],["path",{d:"M12 10v2",key:"hh53o1"}],["path",{d:"M16 10v6",key:"1d6xys"}]])},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>Y});var r,i,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,n(43210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,h=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),m=function(){},v=s.forwardRef(function(e,t){var n,r,i,l,u=s.useRef(null),f=s.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=f[0],g=f[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,T=e.shards,S=e.sideCar,P=e.noRelative,A=e.noIsolation,C=e.inert,R=e.allowPinchZoom,M=e.as,k=e.gapMode,D=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(n=[u,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,l=i.facade,d(function(){var e=h.get(l);if(e){var t=new Set(e),r=new Set(n),i=l.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,i)})}h.set(l,n)},[n]),l),L=o(o({},D),v);return s.createElement(s.Fragment,null,E&&s.createElement(S,{sideCar:p,removeScrollBar:x,shards:T,noRelative:P,noIsolation:A,inert:C,setCallbacks:g,allowPinchZoom:!!R,lockRef:u,gapMode:k}),y?s.cloneElement(s.Children.only(w),o(o({},L),{ref:j})):s.createElement(void 0===M?"div":M,o({},L,{className:b,ref:j}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:l};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,o({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},T=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(i)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=T(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P=b(),A="data-scroll-locked",C=function(e,t,n,r){var i=e.left,o=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},M=function(){s.useEffect(function(){return document.body.setAttribute(A,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},k=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;M();var o=s.useMemo(function(){return S(i)},[i]);return s.createElement(P,{styles:C(o,!t,i,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){D=!1}var L=!!D&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},V=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var i=F(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,i){var o,a=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),s=a*r,l=n.target,u=t.contains(l),c=!1,d=s>0,h=0,f=0;do{if(!l)break;var p=F(e,l),m=p[0],v=p[1]-p[2]-a*m;(m||v)&&O(e,l)&&(h+=v,f+=m);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&s>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-s>f)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},$=0,W=[];let z=(r=function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState($++)[0],o=s.useState(b)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=B(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=V(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=V(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return I(f,t,e,"h"===f?l:u,!0)},[]),u=s.useCallback(function(e){if(W.length&&W[W.length-1]===o){var n="deltaY"in e?_(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){n.current=B(e),r.current=void 0},[]),h=s.useCallback(function(t){c(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return W.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,L),document.addEventListener("touchmove",u,L),document.addEventListener("touchstart",d,L),function(){W=W.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,L),document.removeEventListener("touchmove",u,L),document.removeEventListener("touchstart",d,L)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(k,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),g);var H=s.forwardRef(function(e,t){return s.createElement(v,o({},e,{ref:t,sideCar:z}))});H.classNames=v.classNames;let Y=H},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),i=n(98599),o=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[i,a]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,i=s(t);e?h("MOUNT"):"none"===i||t?.display==="none"?h("UNMOUNT"):n&&r!==i?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=e}},[e,h]),(0,o.N)(()=>{if(i){let e,t=i.ownerDocument.defaultView??window,n=n=>{let r=s(l.current).includes(n.animationName);if(n.target===i&&r&&(h("ANIMATION_END"),!u.current)){let n=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=n)})}},r=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",r),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",r),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,i.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||a.isPresent?r.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},49625:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},52581:(e,t,n)=>{n.d(t,{l$:()=>E,oR:()=>y});var r=n(43210),i=n(51215);let o=e=>{switch(e){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},a=Array(12).fill(0),s=({visible:e,className:t})=>r.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},r.createElement("div",{className:"sonner-spinner"},a.map((e,t)=>r.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),l=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),h=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},r.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),r.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),f=()=>{let[e,t]=r.useState(document.hidden);return r.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},p=1;class m{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:p++,o=this.toasts.find(e=>e.id===i),a=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),o?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:n}),{...t,...e,id:i,dismissible:a,title:n}):t):this.addToast({title:n,...r,dismissible:a,id:i}),i},this.dismiss=e=>(e?(this.dismissedToasts.add(e),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:e,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let n,i;if(!t)return;void 0!==t.loading&&(i=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let o=Promise.resolve(e instanceof Function?e():e),a=void 0!==i,s=o.then(async e=>{if(n=["resolve",e],r.isValidElement(e))a=!1,this.create({id:i,type:"default",message:e});else if(g(e)&&!e.ok){a=!1;let n="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,o="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description,s="object"!=typeof n||r.isValidElement(n)?{message:n}:n;this.create({id:i,type:"error",description:o,...s})}else if(e instanceof Error){a=!1;let n="function"==typeof t.error?await t.error(e):t.error,o="function"==typeof t.description?await t.description(e):t.description,s="object"!=typeof n||r.isValidElement(n)?{message:n}:n;this.create({id:i,type:"error",description:o,...s})}else if(void 0!==t.success){a=!1;let n="function"==typeof t.success?await t.success(e):t.success,o="function"==typeof t.description?await t.description(e):t.description,s="object"!=typeof n||r.isValidElement(n)?{message:n}:n;this.create({id:i,type:"success",description:o,...s})}}).catch(async e=>{if(n=["reject",e],void 0!==t.error){a=!1;let n="function"==typeof t.error?await t.error(e):t.error,o="function"==typeof t.description?await t.description(e):t.description,s="object"!=typeof n||r.isValidElement(n)?{message:n}:n;this.create({id:i,type:"error",description:o,...s})}}).finally(()=>{a&&(this.dismiss(i),i=void 0),null==t.finally||t.finally.call(t)}),l=()=>new Promise((e,t)=>s.then(()=>"reject"===n[0]?t(n[1]):e(n[1])).catch(t));return"string"!=typeof i&&"number"!=typeof i?{unwrap:l}:Object.assign(i,{unwrap:l})},this.custom=(e,t)=>{let n=(null==t?void 0:t.id)||p++;return this.create({jsx:e(n),id:n,...t}),n},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let v=new m,g=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,y=Object.assign((e,t)=>{let n=(null==t?void 0:t.id)||p++;return v.addToast({title:e,...t,id:n}),n},{success:v.success,info:v.info,warning:v.warning,error:v.error,custom:v.custom,message:v.message,promise:v.promise,dismiss:v.dismiss,loading:v.loading},{getHistory:()=>v.toasts,getToasts:()=>v.getActiveToasts()});function w(e){return void 0!==e.label}function b(...e){return e.filter(Boolean).join(" ")}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let x=e=>{var t,n,i,a,l,u,c,d,p,m,v;let{invert:g,toast:y,unstyled:x,interacting:E,setHeights:T,visibleToasts:S,heights:P,index:A,toasts:C,expanded:R,removeToast:M,defaultRichColors:k,closeButton:D,style:j,cancelButtonStyle:L,actionButtonStyle:N,className:V="",descriptionClassName:O="",duration:F,position:I,gap:B,expandByDefault:_,classNames:U,icons:$,closeButtonAriaLabel:W="Close toast"}=e,[z,H]=r.useState(null),[Y,X]=r.useState(null),[G,K]=r.useState(!1),[q,Z]=r.useState(!1),[Q,J]=r.useState(!1),[ee,et]=r.useState(!1),[en,er]=r.useState(!1),[ei,eo]=r.useState(0),[ea,es]=r.useState(0),el=r.useRef(y.duration||F||4e3),eu=r.useRef(null),ec=r.useRef(null),ed=0===A,eh=A+1<=S,ef=y.type,ep=!1!==y.dismissible,em=y.className||"",ev=y.descriptionClassName||"",eg=r.useMemo(()=>P.findIndex(e=>e.toastId===y.id)||0,[P,y.id]),ey=r.useMemo(()=>{var e;return null!=(e=y.closeButton)?e:D},[y.closeButton,D]),ew=r.useMemo(()=>y.duration||F||4e3,[y.duration,F]),eb=r.useRef(0),ex=r.useRef(0),eE=r.useRef(0),eT=r.useRef(null),[eS,eP]=I.split("-"),eA=r.useMemo(()=>P.reduce((e,t,n)=>n>=eg?e:e+t.height,0),[P,eg]),eC=f(),eR=y.invert||g,eM="loading"===ef;ex.current=r.useMemo(()=>eg*B+eA,[eg,eA]),r.useEffect(()=>{el.current=ew},[ew]),r.useEffect(()=>{K(!0)},[]),r.useEffect(()=>{let e=ec.current;if(e){let t=e.getBoundingClientRect().height;return es(t),T(e=>[{toastId:y.id,height:t,position:y.position},...e]),()=>T(e=>e.filter(e=>e.toastId!==y.id))}},[T,y.id]),r.useLayoutEffect(()=>{if(!G)return;let e=ec.current,t=e.style.height;e.style.height="auto";let n=e.getBoundingClientRect().height;e.style.height=t,es(n),T(e=>e.find(e=>e.toastId===y.id)?e.map(e=>e.toastId===y.id?{...e,height:n}:e):[{toastId:y.id,height:n,position:y.position},...e])},[G,y.title,y.description,T,y.id,y.jsx,y.action,y.cancel]);let ek=r.useCallback(()=>{Z(!0),eo(ex.current),T(e=>e.filter(e=>e.toastId!==y.id)),setTimeout(()=>{M(y)},200)},[y,M,T,ex]);r.useEffect(()=>{let e;if((!y.promise||"loading"!==ef)&&y.duration!==1/0&&"loading"!==y.type)return R||E||eC?(()=>{if(eE.current<eb.current){let e=new Date().getTime()-eb.current;el.current=el.current-e}eE.current=new Date().getTime()})():el.current!==1/0&&(eb.current=new Date().getTime(),e=setTimeout(()=>{null==y.onAutoClose||y.onAutoClose.call(y,y),ek()},el.current)),()=>clearTimeout(e)},[R,E,y,ef,eC,ek]),r.useEffect(()=>{y.delete&&(ek(),null==y.onDismiss||y.onDismiss.call(y,y))},[ek,y.delete]);let eD=y.icon||(null==$?void 0:$[ef])||o(ef);return r.createElement("li",{tabIndex:0,ref:ec,className:b(V,em,null==U?void 0:U.toast,null==y||null==(t=y.classNames)?void 0:t.toast,null==U?void 0:U.default,null==U?void 0:U[ef],null==y||null==(n=y.classNames)?void 0:n[ef]),"data-sonner-toast":"","data-rich-colors":null!=(m=y.richColors)?m:k,"data-styled":!(y.jsx||y.unstyled||x),"data-mounted":G,"data-promise":!!y.promise,"data-swiped":en,"data-removed":q,"data-visible":eh,"data-y-position":eS,"data-x-position":eP,"data-index":A,"data-front":ed,"data-swiping":Q,"data-dismissible":ep,"data-type":ef,"data-invert":eR,"data-swipe-out":ee,"data-swipe-direction":Y,"data-expanded":!!(R||_&&G),style:{"--index":A,"--toasts-before":A,"--z-index":C.length-A,"--offset":`${q?ei:ex.current}px`,"--initial-height":_?"auto":`${ea}px`,...j,...y.style},onDragEnd:()=>{J(!1),H(null),eT.current=null},onPointerDown:e=>{!eM&&ep&&(eu.current=new Date,eo(ex.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(J(!0),eT.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,n,r,i;if(ee||!ep)return;eT.current=null;let o=Number((null==(e=ec.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),a=Number((null==(t=ec.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),s=new Date().getTime()-(null==(n=eu.current)?void 0:n.getTime()),l="x"===z?o:a,u=Math.abs(l)/s;if(Math.abs(l)>=45||u>.11){eo(ex.current),null==y.onDismiss||y.onDismiss.call(y,y),"x"===z?X(o>0?"right":"left"):X(a>0?"down":"up"),ek(),et(!0);return}null==(r=ec.current)||r.style.setProperty("--swipe-amount-x","0px"),null==(i=ec.current)||i.style.setProperty("--swipe-amount-y","0px"),er(!1),J(!1),H(null)},onPointerMove:t=>{var n,r,i,o;if(!eT.current||!ep||(null==(n=window.getSelection())?void 0:n.toString().length)>0)return;let a=t.clientY-eT.current.y,s=t.clientX-eT.current.x,l=null!=(o=e.swipeDirections)?o:function(e){let[t,n]=e.split("-"),r=[];return t&&r.push(t),n&&r.push(n),r}(I);!z&&(Math.abs(s)>1||Math.abs(a)>1)&&H(Math.abs(s)>Math.abs(a)?"x":"y");let u={x:0,y:0},c=e=>1/(1.5+Math.abs(e)/20);if("y"===z){if(l.includes("top")||l.includes("bottom"))if(l.includes("top")&&a<0||l.includes("bottom")&&a>0)u.y=a;else{let e=a*c(a);u.y=Math.abs(e)<Math.abs(a)?e:a}}else if("x"===z&&(l.includes("left")||l.includes("right")))if(l.includes("left")&&s<0||l.includes("right")&&s>0)u.x=s;else{let e=s*c(s);u.x=Math.abs(e)<Math.abs(s)?e:s}(Math.abs(u.x)>0||Math.abs(u.y)>0)&&er(!0),null==(r=ec.current)||r.style.setProperty("--swipe-amount-x",`${u.x}px`),null==(i=ec.current)||i.style.setProperty("--swipe-amount-y",`${u.y}px`)}},ey&&!y.jsx&&"loading"!==ef?r.createElement("button",{"aria-label":W,"data-disabled":eM,"data-close-button":!0,onClick:eM||!ep?()=>{}:()=>{ek(),null==y.onDismiss||y.onDismiss.call(y,y)},className:b(null==U?void 0:U.closeButton,null==y||null==(i=y.classNames)?void 0:i.closeButton)},null!=(v=null==$?void 0:$.close)?v:h):null,(ef||y.icon||y.promise)&&null!==y.icon&&((null==$?void 0:$[ef])!==null||y.icon)?r.createElement("div",{"data-icon":"",className:b(null==U?void 0:U.icon,null==y||null==(a=y.classNames)?void 0:a.icon)},y.promise||"loading"===y.type&&!y.icon?y.icon||function(){var e,t;return(null==$?void 0:$.loading)?r.createElement("div",{className:b(null==U?void 0:U.loader,null==y||null==(t=y.classNames)?void 0:t.loader,"sonner-loader"),"data-visible":"loading"===ef},$.loading):r.createElement(s,{className:b(null==U?void 0:U.loader,null==y||null==(e=y.classNames)?void 0:e.loader),visible:"loading"===ef})}():null,"loading"!==y.type?eD:null):null,r.createElement("div",{"data-content":"",className:b(null==U?void 0:U.content,null==y||null==(l=y.classNames)?void 0:l.content)},r.createElement("div",{"data-title":"",className:b(null==U?void 0:U.title,null==y||null==(u=y.classNames)?void 0:u.title)},y.jsx?y.jsx:"function"==typeof y.title?y.title():y.title),y.description?r.createElement("div",{"data-description":"",className:b(O,ev,null==U?void 0:U.description,null==y||null==(c=y.classNames)?void 0:c.description)},"function"==typeof y.description?y.description():y.description):null),r.isValidElement(y.cancel)?y.cancel:y.cancel&&w(y.cancel)?r.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||L,onClick:e=>{w(y.cancel)&&ep&&(null==y.cancel.onClick||y.cancel.onClick.call(y.cancel,e),ek())},className:b(null==U?void 0:U.cancelButton,null==y||null==(d=y.classNames)?void 0:d.cancelButton)},y.cancel.label):null,r.isValidElement(y.action)?y.action:y.action&&w(y.action)?r.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||N,onClick:e=>{w(y.action)&&(null==y.action.onClick||y.action.onClick.call(y.action,e),e.defaultPrevented||ek())},className:b(null==U?void 0:U.actionButton,null==y||null==(p=y.classNames)?void 0:p.actionButton)},y.action.label):null)},E=r.forwardRef(function(e,t){let{invert:n,position:o="bottom-right",hotkey:a=["altKey","KeyT"],expand:s,closeButton:l,className:u,offset:c,mobileOffset:d,theme:h="light",richColors:f,duration:p,style:m,visibleToasts:g=3,toastOptions:y,dir:w="ltr",gap:b=14,icons:E,containerAriaLabel:T="Notifications"}=e,[S,P]=r.useState([]),A=r.useMemo(()=>Array.from(new Set([o].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,o]),[C,R]=r.useState([]),[M,k]=r.useState(!1),[D,j]=r.useState(!1),[L,N]=r.useState("system"!==h?h:"light"),V=r.useRef(null),O=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),F=r.useRef(null),I=r.useRef(!1),B=r.useCallback(e=>{P(t=>{var n;return(null==(n=t.find(t=>t.id===e.id))?void 0:n.delete)||v.dismiss(e.id),t.filter(({id:t})=>t!==e.id)})},[]);return r.useEffect(()=>v.subscribe(e=>{if(e.dismiss)return void requestAnimationFrame(()=>{P(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t))});setTimeout(()=>{i.flushSync(()=>{P(t=>{let n=t.findIndex(t=>t.id===e.id);return -1!==n?[...t.slice(0,n),{...t[n],...e},...t.slice(n+1)]:[e,...t]})})})}),[S]),r.useEffect(()=>{if("system"!==h)return void N(h);"system"===h&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?N("dark"):N("light"))},[h]),r.useEffect(()=>{S.length<=1&&k(!1)},[S]),r.useEffect(()=>{let e=e=>{var t,n;a.every(t=>e[t]||e.code===t)&&(k(!0),null==(n=V.current)||n.focus()),"Escape"===e.code&&(document.activeElement===V.current||(null==(t=V.current)?void 0:t.contains(document.activeElement)))&&k(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a]),r.useEffect(()=>{if(V.current)return()=>{F.current&&(F.current.focus({preventScroll:!0}),F.current=null,I.current=!1)}},[V.current]),r.createElement("section",{ref:t,"aria-label":`${T} ${O}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},A.map((t,i)=>{var o;let[a,h]=t.split("-");return S.length?r.createElement("ol",{key:t,dir:"auto"===w?"ltr":w,tabIndex:-1,ref:V,className:u,"data-sonner-toaster":!0,"data-sonner-theme":L,"data-y-position":a,"data-x-position":h,style:{"--front-toast-height":`${(null==(o=C[0])?void 0:o.height)||0}px`,"--width":"356px","--gap":`${b}px`,...m,...function(e,t){let n={};return[e,t].forEach((e,t)=>{let r=1===t,i=r?"--mobile-offset":"--offset",o=r?"16px":"24px";function a(e){["top","right","bottom","left"].forEach(t=>{n[`${i}-${t}`]="number"==typeof e?`${e}px`:e})}"number"==typeof e||"string"==typeof e?a(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?n[`${i}-${t}`]=o:n[`${i}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]}):a(o)}),n}(c,d)},onBlur:e=>{I.current&&!e.currentTarget.contains(e.relatedTarget)&&(I.current=!1,F.current&&(F.current.focus({preventScroll:!0}),F.current=null))},onFocus:e=>{!(e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible)&&(I.current||(I.current=!0,F.current=e.relatedTarget))},onMouseEnter:()=>k(!0),onMouseMove:()=>k(!0),onMouseLeave:()=>{D||k(!1)},onDragEnd:()=>k(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||j(!0)},onPointerUp:()=>j(!1)},S.filter(e=>!e.position&&0===i||e.position===t).map((i,o)=>{var a,u;return r.createElement(x,{key:i.id,icons:E,index:o,toast:i,defaultRichColors:f,duration:null!=(a=null==y?void 0:y.duration)?a:p,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:n,visibleToasts:g,closeButton:null!=(u=null==y?void 0:y.closeButton)?u:l,interacting:D,position:t,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,closeButtonAriaLabel:null==y?void 0:y.closeButtonAriaLabel,removeToast:B,toasts:S.filter(e=>e.position==i.position),heights:C.filter(e=>e.position==i.position),setHeights:R,expandByDefault:s,gap:b,expanded:M,swipeDirections:e.swipeDirections})})):null}))})},53332:(e,t,n)=>{var r=n(43210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,a=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),i=r[0].inst,c=r[1];return s(function(){i.value=n,i.getSnapshot=t,u(i)&&c({inst:i})},[e,n,t]),a(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},55509:(e,t,n)=>{n.d(t,{Mz:()=>eq,i3:()=>eQ,UC:()=>eZ,bL:()=>eK,Bk:()=>eL});var r=n(43210);let i=["top","right","bottom","left"],o=Math.min,a=Math.max,s=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function h(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(f(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,a=g(t),s=m(g(t)),l=v(s),u=f(t),c="y"===a,d=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,y=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:h};break;case"left":r={x:i.x-o.width,y:h};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[s]-=y*(n&&c?-1:1);break;case"end":r[s]+=y*(n&&c?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,s=o.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=E(u,r,l),h=r,f={},p=0;for(let n=0;n<s.length;n++){let{name:o,fn:m}=s[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:h,strategy:i,middlewareData:f,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,f={...f,[o]:{...f[o],...y}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(h=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:d}=E(u,h,l)),n=-1)}return{x:c,y:d,placement:h,strategy:i,middlewareData:f}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=h(t,e),m=b(p),v=s[f?"floating"===d?"reference":"floating":d],g=x(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:r,y:i,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},T=x(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:w,strategy:l}):y);return{top:(g.top-T.top+m.top)/E.y,bottom:(T.bottom-g.bottom+m.bottom)/E.y,left:(g.left-T.left+m.left)/E.x,right:(T.right-g.right+m.right)/E.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return i.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),a=f(n),s=p(n),l="y"===g(n),u=["left","top"].includes(a)?-1:1,c=o&&l?-1:1,d=h(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof y&&(v="end"===s?-1*y:y),l?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function R(){return"undefined"!=typeof window}function M(e){return j(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(j(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function j(e){return!!R()&&(e instanceof Node||e instanceof k(e).Node)}function L(e){return!!R()&&(e instanceof Element||e instanceof k(e).Element)}function N(e){return!!R()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function V(e){return!!R()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function I(e){let t=B(),n=L(e)?U(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function _(e){return["html","body","#document"].includes(M(e))}function U(e){return k(e).getComputedStyle(e)}function $(e){return L(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===M(e))return e;let t=e.assignedSlot||e.parentNode||V(e)&&e.host||D(e);return V(t)?t.host:t}function z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=W(t);return _(n)?t.ownerDocument?t.ownerDocument.body:t.body:N(n)&&O(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),a=k(i);if(o){let e=H(a);return t.concat(a,a.visualViewport||[],O(i)?i:[],e&&n?z(e):[])}return t.concat(i,z(i,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Y(e){let t=U(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=N(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,l=s(n)!==o||s(r)!==a;return l&&(n=o,r=a),{width:n,height:r,$:l}}function X(e){return L(e)?e:e.contextElement}function G(e){let t=X(e);if(!N(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=Y(t),a=(o?s(n.width):n.width)/r,l=(o?s(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let K=u(0);function q(e){let t=k(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function Z(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),a=X(e),s=u(1);t&&(r?L(r)&&(s=G(r)):s=G(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===k(a))&&i)?q(a):u(0),c=(o.left+l.x)/s.x,d=(o.top+l.y)/s.y,h=o.width/s.x,f=o.height/s.y;if(a){let e=k(a),t=r&&L(r)?k(r):r,n=e,i=H(n);for(;i&&r&&t!==n;){let e=G(i),t=i.getBoundingClientRect(),r=U(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,h*=e.x,f*=e.y,c+=o,d+=a,i=H(n=k(i))}}return x({width:h,height:f,x:c,y:d})}function Q(e,t){let n=$(e).scrollLeft;return t?t.left+n:Z(D(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=k(e),r=D(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;let e=B();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s,y:l}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=$(e),r=e.ownerDocument.body,i=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+Q(e),l=-n.scrollTop;return"rtl"===U(r).direction&&(s+=a(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:l}}(D(e));else if(L(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=N(e)?G(e):u(1),a=e.clientWidth*o.x,s=e.clientHeight*o.y;return{width:a,height:s,x:i*o.x,y:r*o.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===U(e).position}function en(e,t){if(!N(e)||"fixed"===U(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=k(e);if(F(e))return n;if(!N(e)){let t=W(e);for(;t&&!_(t);){if(L(t)&&!et(t))return t;t=W(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(M(r))&&et(r);)r=en(r,t);return r&&_(r)&&et(r)&&!I(r)?n:r||function(e){let t=W(e);for(;N(t)&&!_(t);){if(I(t))return t;if(F(t))break;t=W(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=N(t),i=D(t),o="fixed"===n,a=Z(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=u(0);if(r||!r&&!o)if(("body"!==M(t)||O(i))&&(s=$(t)),r){let e=Z(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=Q(i));o&&!r&&i&&(l.x=Q(i));let c=!i||r||o?u(0):J(i,s);return{x:a.left+s.scrollLeft-l.x-c.x,y:a.top+s.scrollTop-l.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,a=D(r),s=!!t&&F(t.floating);if(r===a||s&&o)return n;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),h=N(r);if((h||!h&&!o)&&(("body"!==M(r)||O(a))&&(l=$(r)),N(r))){let e=Z(r);c=G(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let f=!a||h||o?u(0):J(a,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:n.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,s=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=z(e,[],!1).filter(e=>L(e)&&"body"!==M(e)),i=null,o="fixed"===U(e).position,a=o?W(e):e;for(;L(a)&&!_(a);){let t=U(a),n=I(a);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||O(a)&&!n&&function e(t,n){let r=W(t);return!(r===n||!L(r)||_(r))&&("fixed"===U(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):i=t,a=W(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=s[0],u=s.reduce((e,n)=>{let r=ee(t,n,i);return e.top=a(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=Y(e);return{width:t,height:n}},getScale:G,isElement:L,isRTL:function(e){return"rtl"===U(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let es=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:s,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:f=0}=h(e,t)||{};if(null==d)return{};let y=b(f),w={x:n,y:r},x=m(g(i)),E=v(x),T=await l.getDimensions(d),S="y"===x,P=S?"clientHeight":"clientWidth",A=s.reference[E]+s.reference[x]-w[x]-s.floating[E],C=w[x]-s.reference[x],R=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),M=R?R[P]:0;M&&await (null==l.isElement?void 0:l.isElement(R))||(M=u.floating[P]||s.floating[E]);let k=M/2-T[E]/2-1,D=o(y[S?"top":"left"],k),j=o(y[S?"bottom":"right"],k),L=M-T[E]-j,N=M/2-T[E]/2+(A/2-C/2),V=a(D,o(N,L)),O=!c.arrow&&null!=p(i)&&N!==V&&s.reference[E]/2-(N<D?D:j)-T[E]/2<0,F=O?N<D?N-D:N-L:0;return{[x]:w[x]+F,data:{[x]:V,centerOffset:N-V-F,...O&&{alignmentOffset:F}},reset:O}}}),el=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return T(e,t,{...i,platform:o})};var eu=n(51215),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eh(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ef(e,t){let n=eh(e);return Math.round(t*n)/n}function ep(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?es({element:n.current,padding:r}).fn(t):{}:n?es({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:a,middlewareData:s}=t,l=await C(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:s=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=h(e,t),d={x:n,y:r},p=await S(t,c),v=g(f(i)),y=m(v),w=d[y],b=d[v];if(s){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+p[e],r=w-p[t];w=a(n,o(w,r))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+p[e],r=b-p[t];b=a(n,o(b,r))}let x=u.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:s,[v]:l}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=h(e,t),c={x:n,y:r},d=g(i),p=m(d),v=c[p],y=c[d],w=h(s,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+b.mainAxis,n=o.reference[p]+o.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,E;let e="y"===p?"width":"height",t=["top","left"].includes(f(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[p]:v,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:T,fallbackStrategy:P="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:C=!0,...R}=h(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};let M=f(s),k=g(c),D=f(c)===c,j=await (null==d.isRTL?void 0:d.isRTL(b.floating)),L=T||(D||!C?[w(c)]:function(e){let t=w(e);return[y(e),t,y(t)]}(c)),N="none"!==A;!T&&N&&L.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(c,C,A,j));let V=[c,...L],O=await S(t,R),F=[],I=(null==(r=l.flip)?void 0:r.overflows)||[];if(x&&F.push(O[M]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(g(e)),o=v(i),a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=w(a)),[a,w(a)]}(s,u,j);F.push(O[e[0]],O[e[1]])}if(I=[...I,{placement:s,overflows:F}],!F.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=V[e];if(t&&("alignment"!==E||k===g(t)||I.every(e=>e.overflows[0]>0&&g(e.placement)===k)))return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(o=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(P){case"bestFit":{let e=null==(a=I.filter(e=>{if(N){let t=g(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(s!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,s,{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=h(e,t),y=await S(t,v),w=f(l),b=p(l),x="y"===g(l),{width:E,height:T}=u.floating;"top"===w||"bottom"===w?(i=w,s=b===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(s=w,i="end"===b?"top":"bottom");let P=T-y.top-y.bottom,A=E-y.left-y.right,C=o(T-y[i],P),R=o(E-y[s],A),M=!t.middlewareData.shift,k=C,D=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=P),M&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?D=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):k=T-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:D,availableHeight:k});let j=await c.getDimensions(d.floating);return E!==j.width||T!==j.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=h(e,t);switch(r){case"referenceHidden":{let e=P(await S(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=P(await S(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eT=n(14163),eS=n(60687),eP=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eS.jsx)(eT.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eS.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eP.displayName="Arrow";var eA=n(98599),eC=n(11273),eR=n(13495),eM=n(66156),ek=n(18853),eD="Popper",[ej,eL]=(0,eC.A)(eD),[eN,eV]=ej(eD),eO=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eS.jsx)(eN,{scope:t,anchor:i,onAnchorChange:o,children:n})};eO.displayName=eD;var eF="PopperAnchor",eI=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,a=eV(eF,n),s=r.useRef(null),l=(0,eA.s)(t,s);return r.useEffect(()=>{a.onAnchorChange(i?.current||s.current)}),i?null:(0,eS.jsx)(eT.sG.div,{...o,ref:l})});eI.displayName=eF;var eB="PopperContent",[e_,eU]=ej(eB),e$=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:s=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:h=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eV(eB,n),[x,E]=r.useState(null),T=(0,eA.s)(t,e=>E(e)),[S,P]=r.useState(null),A=(0,ek.X)(S),C=A?.width??0,R=A?.height??0,M="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},k=Array.isArray(f)?f:[f],j=k.length>0,L={padding:M,boundary:k.filter(eY),altBoundary:j},{refs:N,floatingStyles:V,placement:O,isPositioned:F,middlewareData:I}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,h]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);ed(f,i)||p(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==T.current&&(T.current=e,v(e))},[]),b=r.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=a||m,E=s||g,T=r.useRef(null),S=r.useRef(null),P=r.useRef(d),A=null!=u,C=ep(u),R=ep(o),M=ep(c),k=r.useCallback(()=>{if(!T.current||!S.current)return;let e={placement:t,strategy:n,middleware:f};R.current&&(e.platform=R.current),el(T.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};D.current&&!ed(P.current,t)&&(P.current=t,eu.flushSync(()=>{h(t)}))})},[f,t,n,R,M]);ec(()=>{!1===c&&P.current.isPositioned&&(P.current.isPositioned=!1,h(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(x&&(T.current=x),E&&(S.current=E),x&&E){if(C.current)return C.current(x,E,k);k()}},[x,E,k,C,A]);let j=r.useMemo(()=>({reference:T,floating:S,setReference:w,setFloating:b}),[w,b]),L=r.useMemo(()=>({reference:x,floating:E}),[x,E]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=ef(L.floating,d.x),r=ef(L.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eh(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:k,refs:j,elements:L,floatingStyles:N}),[d,k,j,L,N])}({strategy:"fixed",placement:i+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:h=!1}=r,f=X(e),p=s||u?[...f?z(f):[],...z(t)]:[];p.forEach(e=>{s&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=f&&d?function(e,t){let n,r=null,i=D(e);function s(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),s();let h=e.getBoundingClientRect(),{left:f,top:p,width:m,height:v}=h;if(c||t(),!m||!v)return;let g=l(p),y=l(i.clientWidth-(f+m)),w={rootMargin:-g+"px "+-y+"px "+-l(i.clientHeight-(p+v))+"px "+-l(f)+"px",threshold:a(0,o(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(h,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),s}(f,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),f&&!h&&g.observe(f),g.observe(t));let y=h?Z(e):null;return h&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{s&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,h&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:s+R,alignmentAxis:c}),h&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...L}),h&&ew({...L}),eb({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${i}px`),a.setProperty("--radix-popper-anchor-height",`${o}px`)}}),S&&eE({element:S,padding:d}),eX({arrowWidth:C,arrowHeight:R}),v&&ex({strategy:"referenceHidden",...L})]}),[B,_]=eG(O),U=(0,eR.c)(y);(0,eM.N)(()=>{F&&U?.()},[F,U]);let $=I.arrow?.x,W=I.arrow?.y,H=I.arrow?.centerOffset!==0,[Y,G]=r.useState();return(0,eM.N)(()=>{x&&G(window.getComputedStyle(x).zIndex)},[x]),(0,eS.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:F?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eS.jsx)(e_,{scope:n,placedSide:B,onArrowChange:P,arrowX:$,arrowY:W,shouldHideArrow:H,children:(0,eS.jsx)(eT.sG.div,{"data-side":B,"data-align":_,...w,ref:T,style:{...w.style,animation:F?void 0:"none"}})})})});e$.displayName=eB;var eW="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eH=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eU(eW,n),o=ez[i.placedSide];return(0,eS.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eS.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eY(e){return null!==e}eH.displayName=eW;var eX=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,a=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[l,u]=eG(n),c={start:"0%",center:"50%",end:"100%"}[u],d=(i.arrow?.x??0)+a/2,h=(i.arrow?.y??0)+s/2,f="",p="";return"bottom"===l?(f=o?c:`${d}px`,p=`${-s}px`):"top"===l?(f=o?c:`${d}px`,p=`${r.floating.height+s}px`):"right"===l?(f=`${-s}px`,p=o?c:`${h}px`):"left"===l&&(f=`${r.floating.width+s}px`,p=o?c:`${h}px`),{data:{x:f,y:p}}}});function eG(e){let[t,n="center"]=e.split("-");return[t,n]}var eK=eO,eq=eI,eZ=e$,eQ=eH},57379:(e,t,n)=>{e.exports=n(53332)},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62369:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(43210),i=n(14163),o=n(60687),a="horizontal",s=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=a,...u}=e,c=(n=l,s.includes(n))?l:a;return(0,o.jsx)(i.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l},62688:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),a=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:o="",children:a,iconNode:c,...d},h)=>(0,r.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:s("lucide",o),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...o},l)=>(0,r.createElement)(c,{ref:l,iconNode:t,className:s(`lucide-${i(a(e))}`,`lucide-${e}`,n),...o}));return n.displayName=a(e),n}},63376:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,a={},s=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],h=new Set,f=new Set(u),p=function(e){!e||h.has(e)||(h.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||f.has(e)||Array.prototype.forEach.call(e.children,function(e){if(h.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,s=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,s),c.set(e,l),d.push(e),1===s&&a&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),h.clear(),s++,function(){d.forEach(function(e){var t=i.get(e)-1,a=c.get(e)-1;i.set(e,t),c.set(e,a),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),a||e.removeAttribute(n)}),--s||(i=new WeakMap,i=new WeakMap,o=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live], script"))),u(i,o,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>s});var r,i=n(43210),o=n(66156),a=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function s({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,s,l]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),s=i.useRef(t);return a(()=>{s.current=t},[t]),i.useEffect(()=>{o.current!==n&&(s.current?.(n),o.current=n)},[n,o]),[n,r,s]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(43210),i=globalThis?.document?r.useLayoutEffect:()=>{}},67969:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},68123:(e,t,n)=>{n.d(t,{LM:()=>G,OK:()=>K,VM:()=>T,bL:()=>X,lr:()=>N});var r=n(43210),i=n(14163),o=n(46059),a=n(11273),s=n(98599),l=n(13495),u=n(43),c=n(66156),d=n(67969),h=n(70569),f=n(60687),p="ScrollArea",[m,v]=(0,a.A)(p),[g,y]=m(p),w=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:o="hover",dir:a,scrollHideDelay:l=600,...c}=e,[d,h]=r.useState(null),[p,m]=r.useState(null),[v,y]=r.useState(null),[w,b]=r.useState(null),[x,E]=r.useState(null),[T,S]=r.useState(0),[P,A]=r.useState(0),[C,R]=r.useState(!1),[M,k]=r.useState(!1),D=(0,s.s)(t,e=>h(e)),j=(0,u.jH)(a);return(0,f.jsx)(g,{scope:n,type:o,dir:j,scrollHideDelay:l,scrollArea:d,viewport:p,onViewportChange:m,content:v,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:C,onScrollbarXEnabledChange:R,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:M,onScrollbarYEnabledChange:k,onCornerWidthChange:S,onCornerHeightChange:A,children:(0,f.jsx)(i.sG.div,{dir:j,...c,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":T+"px","--radix-scroll-area-corner-height":P+"px",...e.style}})})});w.displayName=p;var b="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:o,nonce:a,...l}=e,u=y(b,n),c=r.useRef(null),d=(0,s.s)(t,c,u.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(i.sG.div,{"data-radix-scroll-area-viewport":"",...l,ref:d,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});x.displayName=b;var E="ScrollAreaScrollbar",T=r.forwardRef((e,t)=>{let{forceMount:n,...i}=e,o=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=o,l="horizontal"===e.orientation;return r.useEffect(()=>(l?a(!0):s(!0),()=>{l?a(!1):s(!1)}),[l,a,s]),"hover"===o.type?(0,f.jsx)(S,{...i,ref:t,forceMount:n}):"scroll"===o.type?(0,f.jsx)(P,{...i,ref:t,forceMount:n}):"auto"===o.type?(0,f.jsx)(A,{...i,ref:t,forceMount:n}):"always"===o.type?(0,f.jsx)(C,{...i,ref:t}):null});T.displayName=E;var S=r.forwardRef((e,t)=>{let{forceMount:n,...i}=e,a=y(E,e.__scopeScrollArea),[s,l]=r.useState(!1);return r.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),l(!0)},r=()=>{t=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(o.C,{present:n||s,children:(0,f.jsx)(A,{"data-state":s?"visible":"hidden",...i,ref:t})})}),P=r.forwardRef((e,t)=>{var n,i;let{forceMount:a,...s}=e,l=y(E,e.__scopeScrollArea),u="horizontal"===e.orientation,c=H(()=>p("SCROLL_END"),100),[d,p]=(n="hidden",i={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>i[e][t]??e,n));return r.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>p("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,l.scrollHideDelay,p]),r.useEffect(()=>{let e=l.viewport,t=u?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(p("SCROLL"),c()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[l.viewport,u,p,c]),(0,f.jsx)(o.C,{present:a||"hidden"!==d,children:(0,f.jsx)(C,{"data-state":"hidden"===d?"hidden":"visible",...s,ref:t,onPointerEnter:(0,h.m)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,h.m)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),A=r.forwardRef((e,t)=>{let n=y(E,e.__scopeScrollArea),{forceMount:i,...a}=e,[s,l]=r.useState(!1),u="horizontal"===e.orientation,c=H(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;l(u?e:t)}},10);return Y(n.viewport,c),Y(n.content,c),(0,f.jsx)(o.C,{present:i||s,children:(0,f.jsx)(C,{"data-state":s?"visible":"hidden",...a,ref:t})})}),C=r.forwardRef((e,t)=>{let{orientation:n="vertical",...i}=e,o=y(E,e.__scopeScrollArea),a=r.useRef(null),s=r.useRef(0),[l,u]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=_(l.viewport,l.content),d={...i,sizes:l,onSizesChange:u,hasThumb:!!(c>0&&c<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function h(e,t){return function(e,t,n,r="ltr"){let i=U(n),o=t||i/2,a=n.scrollbar.paddingStart+o,s=n.scrollbar.size-n.scrollbar.paddingEnd-(i-o),l=n.content-n.viewport;return W([a,s],"ltr"===r?[0,l]:[-1*l,0])(e)}(e,s.current,l,t)}return"horizontal"===n?(0,f.jsx)(R,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){let e=$(o.viewport.scrollLeft,l,o.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=h(e,o.dir))}}):"vertical"===n?(0,f.jsx)(M,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){let e=$(o.viewport.scrollTop,l);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=h(e))}}):null}),R=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:i,...o}=e,a=y(E,e.__scopeScrollArea),[l,u]=r.useState(),c=r.useRef(null),d=(0,s.s)(t,c,a.onScrollbarXChange);return r.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,f.jsx)(j,{"data-orientation":"horizontal",...o,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":U(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&l&&i({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:B(l.paddingLeft),paddingEnd:B(l.paddingRight)}})}})}),M=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:i,...o}=e,a=y(E,e.__scopeScrollArea),[l,u]=r.useState(),c=r.useRef(null),d=(0,s.s)(t,c,a.onScrollbarYChange);return r.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,f.jsx)(j,{"data-orientation":"vertical",...o,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":U(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&l&&i({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:B(l.paddingTop),paddingEnd:B(l.paddingBottom)}})}})}),[k,D]=m(E),j=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:o,hasThumb:a,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:p,onDragScroll:m,onWheelScroll:v,onResize:g,...w}=e,b=y(E,n),[x,T]=r.useState(null),S=(0,s.s)(t,e=>T(e)),P=r.useRef(null),A=r.useRef(""),C=b.viewport,R=o.content-o.viewport,M=(0,l.c)(v),D=(0,l.c)(p),j=H(g,10);function L(e){P.current&&m({x:e.clientX-P.current.left,y:e.clientY-P.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&M(e,R)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[C,x,R,M]),r.useEffect(D,[o,D]),Y(x,j),Y(b.content,j),(0,f.jsx)(k,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,l.c)(u),onThumbPointerUp:(0,l.c)(c),onThumbPositionChange:D,onThumbPointerDown:(0,l.c)(d),children:(0,f.jsx)(i.sG.div,{...w,ref:S,style:{position:"absolute",...w.style},onPointerDown:(0,h.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),P.current=x.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),L(e))}),onPointerMove:(0,h.m)(e.onPointerMove,L),onPointerUp:(0,h.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=A.current,b.viewport&&(b.viewport.style.scrollBehavior=""),P.current=null})})})}),L="ScrollAreaThumb",N=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=D(L,e.__scopeScrollArea);return(0,f.jsx)(o.C,{present:n||i.hasThumb,children:(0,f.jsx)(V,{ref:t,...r})})}),V=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:o,...a}=e,l=y(L,n),u=D(L,n),{onThumbPositionChange:c}=u,d=(0,s.s)(t,e=>u.onThumbChange(e)),p=r.useRef(void 0),m=H(()=>{p.current&&(p.current(),p.current=void 0)},100);return r.useEffect(()=>{let e=l.viewport;if(e){let t=()=>{m(),p.current||(p.current=z(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[l.viewport,m,c]),(0,f.jsx)(i.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:(0,h.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;u.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,h.m)(e.onPointerUp,u.onThumbPointerUp)})});N.displayName=L;var O="ScrollAreaCorner",F=r.forwardRef((e,t)=>{let n=y(O,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,f.jsx)(I,{...e,ref:t}):null});F.displayName=O;var I=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...o}=e,a=y(O,n),[s,l]=r.useState(0),[u,c]=r.useState(0),d=!!(s&&u);return Y(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),c(e)}),Y(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),l(e)}),d?(0,f.jsx)(i.sG.div,{...o,ref:t,style:{width:s,height:u,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function B(e){return e?parseInt(e,10):0}function _(e,t){let n=e/t;return isNaN(n)?0:n}function U(e){let t=_(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function $(e,t,n="ltr"){let r=U(t),i=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-i,a=t.content-t.viewport,s=(0,d.q)(e,"ltr"===n?[0,a]:[-1*a,0]);return W([0,a],[0,o-r])(s)}function W(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var z=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function i(){let o={left:e.scrollLeft,top:e.scrollTop},a=n.left!==o.left,s=n.top!==o.top;(a||s)&&t(),n=o,r=window.requestAnimationFrame(i)}(),()=>window.cancelAnimationFrame(r)};function H(e,t){let n=(0,l.c)(e),i=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(i.current),[]),r.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(n,t)},[n,t])}function Y(e,t){let n=(0,l.c)(t);(0,c.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var X=w,G=x,K=F},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},72789:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},72942:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>k,q7:()=>D});var r=n(43210),i=n(70569),o=n(9510),a=n(98599),s=n(11273),l=n(96963),u=n(14163),c=n(13495),d=n(65551),h=n(43),f=n(60687),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,o.N)(v),[b,x]=(0,s.A)(v,[w]),[E,T]=b(v),S=r.forwardRef((e,t)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(P,{...e,ref:t})})}));S.displayName=v;var P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:s=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:T=!1,...S}=e,P=r.useRef(null),A=(0,a.s)(t,P),C=(0,h.jH)(l),[R,k]=(0,d.i)({prop:g,defaultProp:w??null,onChange:b,caller:v}),[D,j]=r.useState(!1),L=(0,c.c)(x),N=y(n),V=r.useRef(!1),[O,F]=r.useState(0);return r.useEffect(()=>{let e=P.current;if(e)return e.addEventListener(p,L),()=>e.removeEventListener(p,L)},[L]),(0,f.jsx)(E,{scope:n,orientation:o,dir:C,loop:s,currentTabStopId:R,onItemFocus:r.useCallback(e=>k(e),[k]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>F(e=>e-1),[]),children:(0,f.jsx)(u.sG.div,{tabIndex:D||0===O?-1:0,"data-orientation":o,...S,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{V.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!V.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),T)}}V.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>j(!1))})})}),A="RovingFocusGroupItem",C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:a=!1,tabStopId:s,children:c,...d}=e,h=(0,l.B)(),p=s||h,m=T(A,n),v=m.currentTabStopId===p,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=m;return r.useEffect(()=>{if(o)return b(),()=>x()},[o,b,x]),(0,f.jsx)(g.ItemSlot,{scope:n,id:p,focusable:o,active:a,children:(0,f.jsx)(u.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let i=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return R[i]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>M(n))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=E}):c})})});C.displayName=A;var R={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var k=S,D=C},74479:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84027:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86044:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(43210),i=n(21279);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:a,register:s}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,r.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!n&&a?[!1,u]:[!0]}},88920:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(60687),i=n(43210),o=n(12157),a=n(72789),s=n(15124),l=n(21279),u=n(18171),c=n(32582);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t,anchorX:n}){let o=(0,i.useId)(),a=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:u,right:c}=s.current;if(t||!a.current||!e||!r)return;let d="left"===n?`left: ${u}`:`right: ${c}`;a.current.dataset.motionPopId=o;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${d}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,r.jsx)(d,{isPresent:t,childRef:a,sizeRef:s,children:i.cloneElement(e,{ref:a})})}let f=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:s,presenceAffectsLayout:u,mode:c,anchorX:d})=>{let f=(0,a.M)(p),m=(0,i.useId)(),v=!0,g=(0,i.useMemo)(()=>(v=!1,{id:m,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;o&&o()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[n,f,o]);return u&&v&&(g={...g}),(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[n]),i.useEffect(()=>{n||f.size||!o||o()},[n]),"popLayout"===c&&(e=(0,r.jsx)(h,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(l.t.Provider,{value:g,children:e})};function p(){return new Map}var m=n(86044);let v=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:n=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:h="left"})=>{let[p,y]=(0,m.xQ)(d),w=(0,i.useMemo)(()=>g(e),[e]),b=d&&!p?[]:w.map(v),x=(0,i.useRef)(!0),E=(0,i.useRef)(w),T=(0,a.M)(()=>new Map),[S,P]=(0,i.useState)(w),[A,C]=(0,i.useState)(w);(0,s.E)(()=>{x.current=!1,E.current=w;for(let e=0;e<A.length;e++){let t=v(A[e]);b.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[A,b.length,b.join("-")]);let R=[];if(w!==S){let e=[...w];for(let t=0;t<A.length;t++){let n=A[t],r=v(n);b.includes(r)||(e.splice(t,0,n),R.push(n))}return"wait"===c&&R.length&&(e=R),C(g(e)),P(w),null}let{forceRender:M}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:A.map(e=>{let i=v(e),o=(!d||!!p)&&(w===A||b.includes(i));return(0,r.jsx)(f,{isPresent:o,initial:(!x.current||!!n)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:o?void 0:()=>{if(!T.has(i))return;T.set(i,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(M?.(),C(E.current),d&&y?.(),l&&l())},anchorX:h,children:e},i)})})}},96963:(e,t,n)=>{n.d(t,{B:()=>l});var r,i=n(43210),o=n(66156),a=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,n]=i.useState(a());return(0,o.N)(()=>{e||n(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};