exports.id=828,exports.ids=[828],exports.modules={4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},5214:(e,t,s)=>{Promise.resolve().then(s.bind(s,63144))},20373:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ee});var r=s(60687),a=s(29523),n=s(35950),i=s(49625),o=s(35020),l=s(84027),d=s(40083),c=s(82136),u=s(85814),m=s.n(u),h=s(16189);let x=[{title:"Dashboard",href:"/dashboard",icon:i.A},{title:"Projecten",href:"/dashboard/projects",icon:o.A},{title:"Instellingen",href:"/dashboard/settings",icon:l.A}];function f(){let e=(0,h.usePathname)();return(0,r.jsxs)("nav",{className:"flex items-center space-x-4 lg:space-x-6",children:[x.map(t=>(0,r.jsx)(a.$,{variant:e===t.href?"default":"ghost",className:`flex items-center gap-2 transition-all duration-300 ${e===t.href?"glow-accent bg-primary/20 text-primary border-primary/30":"hover:bg-primary/10 hover:text-primary"}`,asChild:!0,children:(0,r.jsxs)(m(),{href:t.href,children:[(0,r.jsx)(t.icon,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden md:inline-block",children:t.title})]})},t.href)),(0,r.jsx)(n.w,{orientation:"vertical",className:"h-6"}),(0,r.jsxs)(a.$,{variant:"ghost",className:"flex items-center gap-2 hover:bg-destructive/10 hover:text-destructive transition-all duration-300",onClick:()=>(0,c.signOut)(),children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden md:inline-block",children:"Uitloggen"})]})]})}var v=s(12941),p=s(43210),b=s(68123),g=s(4780);function j({className:e,children:t,...s}){return(0,r.jsxs)(b.bL,{"data-slot":"scroll-area",className:(0,g.cn)("relative",e),...s,children:[(0,r.jsx)(b.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,r.jsx)(w,{}),(0,r.jsx)(b.OK,{})]})}function w({className:e,orientation:t="vertical",...s}){return(0,r.jsx)(b.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,g.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...s,children:(0,r.jsx)(b.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var N=s(26134),y=s(11860);function P({...e}){return(0,r.jsx)(N.bL,{"data-slot":"sheet",...e})}function k({...e}){return(0,r.jsx)(N.l9,{"data-slot":"sheet-trigger",...e})}function C({...e}){return(0,r.jsx)(N.ZL,{"data-slot":"sheet-portal",...e})}function z({className:e,...t}){return(0,r.jsx)(N.hJ,{"data-slot":"sheet-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function A({className:e,children:t,side:s="right",...a}){return(0,r.jsxs)(C,{children:[(0,r.jsx)(z,{}),(0,r.jsxs)(N.UC,{"data-slot":"sheet-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,(0,r.jsxs)(N.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(y.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function _({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,g.cn)("flex flex-col gap-1.5 p-4",e),...t})}function $({className:e,...t}){return(0,r.jsx)(N.hE,{"data-slot":"sheet-title",className:(0,g.cn)("text-foreground font-semibold",e),...t})}function S({items:e}){let t=(0,h.usePathname)(),[s,n]=p.useState(!1);return(0,r.jsxs)(P,{open:s,onOpenChange:n,children:[(0,r.jsx)(k,{asChild:!0,children:(0,r.jsxs)(a.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden",children:[(0,r.jsx)(v.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"sr-only",children:"Menu"})]})}),(0,r.jsxs)(A,{side:"left",className:"pr-0",children:[(0,r.jsx)(_,{children:(0,r.jsx)($,{children:"Menu"})}),(0,r.jsx)(j,{className:"my-4 h-[calc(100vh-8rem)] pb-10",children:(0,r.jsx)("div",{className:"flex flex-col space-y-3",children:e.map(e=>(0,r.jsxs)(m(),{href:e.href,onClick:()=>n(!1),className:(0,g.cn)("flex items-center space-x-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",t===e.href?"bg-accent text-accent-foreground":"transparent"),children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.title})]},e.href))})})]})]})}var U=s(99270),D=s(89667);function L(){let e=(0,h.useRouter)(),[t,s]=p.useState("");return(0,r.jsxs)("form",{onSubmit:s=>{s.preventDefault(),t.trim()&&e.push(`/dashboard/search?q=${encodeURIComponent(t.trim())}`)},className:"flex w-full max-w-sm items-center space-x-2",children:[(0,r.jsx)(D.p,{type:"search",placeholder:"Zoek projecten...",className:"h-9",value:t,onChange:e=>s(e.target.value)}),(0,r.jsxs)(a.$,{type:"submit",size:"icon",className:"h-9 w-9",children:[(0,r.jsx)(U.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Zoeken"})]})]})}var I=s(26001),E=s(78272),M=s(58869),R=s(32584),W=s(26312);function Z({...e}){return(0,r.jsx)(W.bL,{"data-slot":"dropdown-menu",...e})}function q({...e}){return(0,r.jsx)(W.l9,{"data-slot":"dropdown-menu-trigger",...e})}function H({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(W.ZL,{children:(0,r.jsx)(W.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,g.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function O({className:e,inset:t,variant:s="default",...a}){return(0,r.jsx)(W.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,g.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function T({className:e,inset:t,...s}){return(0,r.jsx)(W.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,g.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...s})}function B({className:e,...t}){return(0,r.jsx)(W.wv,{"data-slot":"dropdown-menu-separator",className:(0,g.cn)("bg-border -mx-1 my-1 h-px",e),...t})}function K(){let{data:e}=(0,c.useSession)();if(!e?.user)return null;let t=e.user.name?.split(" ").map(e=>e[0]).join("").toUpperCase();return(0,r.jsxs)(Z,{children:[(0,r.jsx)(q,{asChild:!0,children:(0,r.jsx)(I.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,r.jsxs)(a.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:[(0,r.jsxs)(R.eu,{className:"h-8 w-8",children:[(0,r.jsx)(R.BK,{src:e.user.image||"",alt:e.user.name||""}),(0,r.jsx)(R.q5,{children:t})]}),(0,r.jsx)(E.A,{className:"ml-2 h-4 w-4"})]})})}),(0,r.jsxs)(H,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(T,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:e.user.name}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e.user.email})]})}),(0,r.jsx)(B,{}),(0,r.jsx)(O,{asChild:!0,children:(0,r.jsx)(I.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,r.jsxs)(a.$,{variant:"ghost",className:"w-full justify-start",onClick:()=>window.location.href="/dashboard/profile",children:[(0,r.jsx)(M.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profiel"})]})})}),(0,r.jsx)(O,{asChild:!0,children:(0,r.jsx)(I.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,r.jsxs)(a.$,{variant:"ghost",className:"w-full justify-start",onClick:()=>window.location.href="/dashboard/settings",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Instellingen"})]})})}),(0,r.jsx)(B,{}),(0,r.jsx)(O,{asChild:!0,children:(0,r.jsx)(I.P.div,{whileHover:{x:4},transition:{duration:.2},children:(0,r.jsxs)(a.$,{variant:"ghost",className:"w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950",onClick:()=>(0,c.signOut)(),children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Uitloggen"})]})})})]})]})}var V=s(21134),J=s(363),F=s(10218);function Q(){let{setTheme:e,theme:t}=(0,F.D)(),[s,n]=p.useState(!1);return(p.useEffect(()=>{n(!0)},[]),s)?(0,r.jsx)(a.$,{variant:"ghost",size:"icon",onClick:()=>e("light"===t?"dark":"light"),className:"relative h-9 w-9 rounded-md","aria-label":"Wissel thema",children:(0,r.jsx)(I.P.div,{initial:!1,animate:{rotate:180*("light"!==t)},transition:{duration:.3},className:"absolute inset-0 flex items-center justify-center",children:"light"===t?(0,r.jsx)(V.A,{className:"h-5 w-5"}):(0,r.jsx)(J.A,{className:"h-5 w-5"})})}):null}var X=s(74654),Y=s(52581);let G=[{title:"Dashboard",href:"/dashboard",icon:i.A},{title:"Projecten",href:"/dashboard/projects",icon:o.A},{title:"Instellingen",href:"/dashboard/settings",icon:l.A}];function ee({children:e}){return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-primary/20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container flex h-14 items-center",children:[(0,r.jsx)(S,{items:G}),(0,r.jsx)(f,{}),(0,r.jsxs)("div",{className:"flex flex-1 items-center justify-between space-x-2 md:justify-end",children:[(0,r.jsx)("div",{className:"w-full flex-1 md:w-auto md:flex-none",children:(0,r.jsx)(L,{})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(Q,{}),(0,r.jsx)(K,{})]})]})]})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:"container py-6",children:(0,r.jsx)(X.g1,{children:(0,r.jsx)(X._A,{children:e})})})}),(0,r.jsx)(Y.l$,{richColors:!0,position:"top-right"})]})}},21741:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>n});var r=s(60687),a=s(82136);function n({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}s(10218)},29519:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","Providers");(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/providers.tsx","ThemeProvider")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...l})}},32584:(e,t,s)=>{"use strict";s.d(t,{BK:()=>o,eu:()=>i,q5:()=>l});var r=s(60687);s(43210);var a=s(11096),n=s(4780);function i({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,r.jsx)(a._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,r.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},35950:(e,t,s)=>{"use strict";s.d(t,{w:()=>i});var r=s(60687);s(43210);var a=s(62369),n=s(4780);function i({className:e,orientation:t="horizontal",decorative:s=!0,...i}){return(0,r.jsx)(a.b,{"data-slot":"separator",decorative:s,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...i})}},38964:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},53718:(e,t,s)=>{Promise.resolve().then(s.bind(s,29519))},61135:()=>{},62932:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},63144:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/dashboard/layout.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73670:(e,t,s)=>{Promise.resolve().then(s.bind(s,20373))},74654:(e,t,s)=>{"use strict";s.d(t,{YE:()=>l,_A:()=>o,g1:()=>d});var r=s(60687),a=s(4780),n=s(26001),i=s(88920);function o({children:e,className:t,delay:s=0,duration:i=.5}){return(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:i,delay:s},className:(0,a.cn)(t),children:e})}function l({children:e,className:t,delay:s=0,duration:i=.5}){return(0,r.jsx)(n.P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{duration:i,delay:s},className:(0,a.cn)(t),children:e})}function d({children:e}){return(0,r.jsx)(i.N,{mode:"wait",children:e})}},77278:(e,t,s)=>{Promise.resolve().then(s.bind(s,21741))},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),a=s(80544),n=s.n(a);s(61135);var i=s(29519);function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:n().variable,children:(0,r.jsx)("body",{className:n().className,children:(0,r.jsx)(i.Providers,{children:e})})})}}};