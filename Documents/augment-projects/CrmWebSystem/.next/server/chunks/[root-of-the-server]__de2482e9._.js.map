{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  });\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma; "], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma';\nimport { compare } from 'bcryptjs';\nimport { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\n\nexport const authOptions: NextAuthOptions = {\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/login',\n  },\n  providers: [\n    CredentialsProvider({\n      name: 'Sign in',\n      credentials: {\n        email: {\n          label: 'Email',\n          type: 'email',\n          placeholder: '<EMAIL>',\n        },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials.password) {\n          return null;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email,\n          },\n        });\n\n        if (!user || !user.password) {\n          return null;\n        }\n\n        const isPasswordValid = await compare(\n          credentials.password,\n          user.password\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: (user as any).role || 'USER',\n        };\n      },\n    }),\n  ],\n  callbacks: {\n    session: ({ session, token }) => {\n      return {\n        ...session,\n        user: {\n          ...session.user,\n          id: token.id,\n          role: token.role,\n        },\n      };\n    },\n    jwt: ({ token, user }) => {\n      if (user) {\n        return {\n          ...token,\n          id: user.id,\n          role: user.role,\n        };\n      }\n      return token;\n    },\n  },\n}; "], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBACL,OAAO;oBACP,MAAM;oBACN,aAAa;gBACf;gBACA,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,YAAY,QAAQ,EAAE;oBAChD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAClC,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,AAAC,KAAa,IAAI,IAAI;gBAC9B;YACF;QACF;KACD;IACD,WAAW;QACT,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;YAC1B,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;gBAClB;YACF;QACF;QACA,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;YACnB,IAAI,MAAM;gBACR,OAAO;oBACL,GAAG,KAAK;oBACR,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;gBACjB;YACF;YACA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/dashboard/stats/route.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth';\nimport { NextResponse } from 'next/server';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\n\nexport async function GET() {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Niet geautoriseerd' },\n        { status: 401 }\n      );\n    }\n\n    const userId = session.user.id;\n\n    // Haal alle statistieken op in parallel\n    const [\n      activeProjects,\n      completedProjects,\n      totalTasks,\n      completedTasks,\n      recentProjects,\n    ] = await Promise.all([\n      // Actieve projecten\n      prisma.project.count({\n        where: {\n          userId,\n          status: 'ACTIVE',\n        },\n      }),\n      \n      // Afgeronde projecten\n      prisma.project.count({\n        where: {\n          userId,\n          status: 'COMPLETED',\n        },\n      }),\n      \n      // Totaal aantal taken\n      prisma.task.count({\n        where: {\n          userId,\n        },\n      }),\n      \n      // Afgeronde taken\n      prisma.task.count({\n        where: {\n          userId,\n          status: 'DONE',\n        },\n      }),\n      \n      // Recente projecten (laatste 5)\n      prisma.project.findMany({\n        where: {\n          userId,\n        },\n        include: {\n          _count: {\n            select: {\n              tasks: true,\n            },\n          },\n        },\n        orderBy: {\n          updatedAt: 'desc',\n        },\n        take: 5,\n      }),\n    ]);\n\n    const stats = {\n      activeProjects,\n      completedProjects,\n      totalTasks,\n      completedTasks,\n      recentProjects,\n    };\n\n    return NextResponse.json(stats);\n  } catch (error) {\n    console.error('Dashboard stats error:', error);\n    return NextResponse.json(\n      { error: 'Fout bij het ophalen van dashboard statistieken' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,QAAQ,IAAI,CAAC,EAAE;QAE9B,wCAAwC;QACxC,MAAM,CACJ,gBACA,mBACA,YACA,gBACA,eACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,oBAAoB;YACpB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACL;oBACA,QAAQ;gBACV;YACF;YAEA,sBAAsB;YACtB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACL;oBACA,QAAQ;gBACV;YACF;YAEA,sBAAsB;YACtB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL;gBACF;YACF;YAEA,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL;oBACA,QAAQ;gBACV;YACF;YAEA,gCAAgC;YAChC,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB,OAAO;oBACL;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,OAAO;wBACT;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA,MAAM;YACR;SACD;QAED,MAAM,QAAQ;YACZ;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkD,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}