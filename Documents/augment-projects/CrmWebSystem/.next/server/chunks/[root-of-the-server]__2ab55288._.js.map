{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  });\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma; "], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport { compare } from \"bcryptjs\";\nimport { NextAuthOptions } from \"next-auth\";\nimport { Adapter } from \"next-auth/adapters\";\nimport NextAuth from \"next-auth/next\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as Adapter,\n  session: {\n    strategy: \"jwt\",\n  },\n  pages: {\n    signIn: \"/auth/login\",\n  },\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Wachtwoord\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error(\"Email en wachtwoord zijn verplicht\");\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email,\n          },\n        });\n\n        if (!user || !user.password) {\n          throw new Error(\"Gebruiker niet gevonden\");\n        }\n\n        const isPasswordValid = await compare(credentials.password, user.password);\n\n        if (!isPasswordValid) {\n          throw new Error(\"Ongeldig wachtwoord\");\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          image: user.image,\n          role: user.role, // Add missing role property\n        };\n      },\n    }),\n  ],\n  callbacks: {\n    async session({ token, session }) {\n      if (token) {\n        session.user.id = token.id;\n        session.user.name = token.name;\n        session.user.email = token.email;\n        session.user.image = token.picture;\n        session.user.role = token.role;\n      }\n\n      return session;\n    },\n    async jwt({ token, user }) {\n      const dbUser = await prisma.user.findFirst({\n        where: {\n          email: token.email,\n        },\n      });\n\n      if (!dbUser) {\n        if (user) {\n          token.id = user?.id;\n        }\n        return token;\n      }\n\n      return {\n        id: dbUser.id,\n        name: dbUser.name,\n        email: dbUser.email,\n        picture: dbUser.image,\n        role: dbUser.role,\n      };\n    },\n  },\n};\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAc,MAAM;gBAAW;YACpD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAAE,YAAY,QAAQ,EAAE,KAAK,QAAQ;gBAEzE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;gBAClC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YAEA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzC,OAAO;oBACL,OAAO,MAAM,KAAK;gBACpB;YACF;YAEA,IAAI,CAAC,QAAQ;gBACX,IAAI,MAAM;oBACR,MAAM,EAAE,GAAG,MAAM;gBACnB;gBACA,OAAO;YACT;YAEA,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,MAAM,OAAO,IAAI;gBACjB,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,KAAK;gBACrB,MAAM,OAAO,IAAI;YACnB;QACF;IACF;AACF;AAEA,MAAM,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}