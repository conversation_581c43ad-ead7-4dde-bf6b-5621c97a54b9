{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  });\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma; "], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/auth.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma';\nimport { compare } from 'bcryptjs';\nimport { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\n\nexport const authOptions: NextAuthOptions = {\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/login',\n  },\n  providers: [\n    CredentialsProvider({\n      name: 'Sign in',\n      credentials: {\n        email: {\n          label: 'Email',\n          type: 'email',\n          placeholder: '<EMAIL>',\n        },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials.password) {\n          return null;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email,\n          },\n        });\n\n        if (!user || !user.password) {\n          return null;\n        }\n\n        const isPasswordValid = await compare(\n          credentials.password,\n          user.password\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: (user as any).role || 'USER',\n        };\n      },\n    }),\n  ],\n  callbacks: {\n    session: ({ session, token }) => {\n      return {\n        ...session,\n        user: {\n          ...session.user,\n          id: token.id,\n          role: token.role,\n        },\n      };\n    },\n    jwt: ({ token, user }) => {\n      if (user) {\n        return {\n          ...token,\n          id: user.id,\n          role: user.role,\n        };\n      }\n      return token;\n    },\n  },\n}; "], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBACL,OAAO;oBACP,MAAM;oBACN,aAAa;gBACf;gBACA,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,YAAY,QAAQ,EAAE;oBAChD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAClC,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,AAAC,KAAa,IAAI,IAAI;gBAC9B;YACF;QACF;KACD;IACD,WAAW;QACT,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;YAC1B,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;gBAClB;YACF;QACF;QACA,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;YACnB,IAAI,MAAM;gBACR,OAAO;oBACL,GAAG,KAAK;oBACR,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;gBACjB;YACF;YACA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/projects/%5Bid%5D/tasks/route.ts"], "sourcesContent": ["import { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { getServerSession } from 'next-auth';\nimport { NextResponse } from 'next/server';\nimport { z } from 'zod';\n\n// Schema voor taak validatie\nconst createTaskSchema = z.object({\n  title: z.string().min(1, 'Titel is verplicht').max(100),\n  description: z.string().max(500).optional(),\n  status: z.enum(['TODO', 'IN_PROGRESS', 'DONE']),\n  dueDate: z.string().optional(),\n});\n\nexport async function POST(\n  req: Request,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user) {\n      return new NextResponse('Unauthorized', { status: 401 });\n    }\n\n    // Controleer of het project bestaat en van <PERSON> geb<PERSON>ike<PERSON> is\n    const project = await prisma.project.findUnique({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n    });\n\n    if (!project) {\n      return new NextResponse('Project niet gevonden', { status: 404 });\n    }\n\n    const json = await req.json();\n    const body = createTaskSchema.parse(json);\n\n    const task = await prisma.task.create({\n      data: {\n        title: body.title,\n        description: body.description,\n        status: body.status,\n        dueDate: body.dueDate && !isNaN(new Date(body.dueDate).getTime()) ? new Date(body.dueDate) : null,\n        projectId: params.id,\n        userId: session.user.id,\n      },\n    });\n\n    return NextResponse.json(task);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return new NextResponse(JSON.stringify(error.issues), { status: 422 });\n    }\n\n    return new NextResponse(\n      JSON.stringify({ error: 'Er is een fout opgetreden' }),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(\n  req: Request,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user) {\n      return new NextResponse('Unauthorized', { status: 401 });\n    }\n\n    // Controleer of het project bestaat en van de gebruiker is\n    const project = await prisma.project.findUnique({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n    });\n\n    if (!project) {\n      return new NextResponse('Project niet gevonden', { status: 404 });\n    }\n\n    const tasks = await prisma.task.findMany({\n      where: {\n        projectId: params.id,\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n    });\n\n    return NextResponse.json(tasks);\n  } catch (error) {\n    return new NextResponse(\n      JSON.stringify({ error: 'Er is een fout opgetreden' }),\n      { status: 500 }\n    );\n  }\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,6BAA6B;AAC7B,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC;IACnD,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACzC,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAe;KAAO;IAC9C,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAEO,eAAe,KACpB,GAAY,EACZ,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,gBAAgB;gBAAE,QAAQ;YAAI;QACxD;QAEA,2DAA2D;QAC3D,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,yBAAyB;gBAAE,QAAQ;YAAI;QACjE;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,OAAO,iBAAiB,KAAK,CAAC;QAEpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO,IAAI,CAAC,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE,OAAO,MAAM,IAAI,KAAK,KAAK,OAAO,IAAI;gBAC7F,WAAW,OAAO,EAAE;gBACpB,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC,MAAM,MAAM,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,OAAO,IAAI,gIAAA,CAAA,eAAY,CACrB,KAAK,SAAS,CAAC;YAAE,OAAO;QAA4B,IACpD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,GAAY,EACZ,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,gBAAgB;gBAAE,QAAQ;YAAI;QACxD;QAEA,2DAA2D;QAC3D,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,yBAAyB;gBAAE,QAAQ;YAAI;QACjE;QAEA,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;gBACL,WAAW,OAAO,EAAE;YACtB;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CACrB,KAAK,SAAS,CAAC;YAAE,OAAO;QAA4B,IACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}