{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/email.ts"], "sourcesContent": ["import nodemailer from 'nodemailer';\n\ninterface EmailOptions {\n  to: string;\n  subject: string;\n  text: string;\n  html: string;\n}\n\nconst transporter = nodemailer.createTransport({\n  host: process.env.SMTP_HOST,\n  port: Number(process.env.SMTP_PORT),\n  secure: process.env.SMTP_SECURE === 'true',\n  auth: {\n    user: process.env.SMTP_USER,\n    pass: process.env.SMTP_PASSWORD,\n  },\n});\n\nexport async function sendEmail({ to, subject, text, html }: EmailOptions) {\n  const mailOptions = {\n    from: process.env.SMTP_FROM,\n    to,\n    subject,\n    text,\n    html,\n  };\n\n  try {\n    await transporter.sendMail(mailOptions);\n  } catch (error) {\n    console.error('Error sending email:', error);\n    throw new Error('Failed to send email');\n  }\n} "], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,cAAc,iJAAA,CAAA,UAAU,CAAC,eAAe,CAAC;IAC7C,MAAM,QAAQ,GAAG,CAAC,SAAS;IAC3B,MAAM,OAAO,QAAQ,GAAG,CAAC,SAAS;IAClC,QAAQ,QAAQ,GAAG,CAAC,WAAW,KAAK;IACpC,MAAM;QACJ,MAAM,QAAQ,GAAG,CAAC,SAAS;QAC3B,MAAM,QAAQ,GAAG,CAAC,aAAa;IACjC;AACF;AAEO,eAAe,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAgB;IACvE,MAAM,cAAc;QAClB,MAAM,QAAQ,GAAG,CAAC,SAAS;QAC3B;QACA;QACA;QACA;IACF;IAEA,IAAI;QACF,MAAM,YAAY,QAAQ,CAAC;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  });\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma; "], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CrmWebSystem/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { sendEmail } from '@/lib/email';\nimport { prisma } from '@/lib/prisma';\nimport { hash } from 'bcryptjs';\nimport { randomBytes } from 'crypto';\nimport { NextResponse } from 'next/server';\nimport { z } from 'zod';\n\nconst registerSchema = z.object({\n  name: z.string().min(2, 'Naam moet minimaal 2 karakters bevatten'),\n  email: z.string().email('Ongeldig email adres'),\n  password: z.string().min(6, 'Wachtwoord moet minimaal 6 karakters bevatten'),\n});\n\nexport async function POST(req: Request) {\n  try {\n    const body = await req.json();\n    const { name, email, password } = registerSchema.parse(body);\n\n    // Check if user already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email },\n    });\n\n    if (existingUser) {\n      return NextResponse.json(\n        { message: '<PERSON><PERSON><PERSON><PERSON><PERSON> met dit email adres bestaat al' },\n        { status: 400 }\n      );\n    }\n\n    // Hash password\n    const hashedPassword = await hash(password, 12);\n\n    // Generate verification token\n    const verificationToken = randomBytes(32).toString('hex');\n    const verificationTokenExpiry = new Date(Date.now() + 24 * 3600000); // 24 hours\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        name,\n        email,\n        password: hashedPassword,\n        verificationToken,\n        verificationTokenExpiry,\n      },\n    });\n\n    // Send verification email\n    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}`;\n    await sendEmail({\n      to: email,\n      subject: 'Verifieer je email adres',\n      text: `Klik op de volgende link om je email adres te verifiëren: ${verificationUrl}`,\n      html: `\n        <p>Welkom bij CRM Web System!</p>\n        <p>Klik op de volgende link om je email adres te verifiëren:</p>\n        <p><a href=\"${verificationUrl}\">${verificationUrl}</a></p>\n        <p>Deze link is 24 uur geldig.</p>\n      `,\n    });\n\n    return NextResponse.json(\n      {\n        message: 'Registratie succesvol. Controleer je email voor verificatie.',\n        user: {\n          name: user.name,\n          email: user.email,\n        },\n      },\n      { status: 201 }\n    );\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { message: error.errors[0].message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { message: 'Er is iets misgegaan bij het registreren' },\n      { status: 500 }\n    );\n  }\n} "], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEA,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,eAAe,KAAK,GAAY;IACrC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,eAAe,KAAK,CAAC;QAEvD,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA2C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,mIAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QAE5C,8BAA8B;QAC9B,MAAM,oBAAoB,CAAA,GAAA,qGAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ,CAAC;QACnD,MAAM,0BAA0B,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,UAAU,WAAW;QAEhF,cAAc;QACd,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;YACF;QACF;QAEA,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,yBAAyB,EAAE,mBAAmB;QAClG,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;YACd,IAAI;YACJ,SAAS;YACT,MAAM,CAAC,0DAA0D,EAAE,iBAAiB;YACpF,MAAM,CAAC;;;oBAGO,EAAE,gBAAgB,EAAE,EAAE,gBAAgB;;MAEpD,CAAC;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;YACnB;QACF,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA2C,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}