---
description: 
globs: reflection-intermediate.mdc
alwaysApply: false
---
# LEVEL 3 REFLECTION: INTERMEDIATE FEATURE REVIEW

> **TL;DR:** This guide structures the reflection process for a completed Level 3 intermediate feature. The focus is on a detailed review of the entire feature development lifecycle, from planning and design through implementation and testing, to extract meaningful lessons and identify improvements for future feature work.

## 🔍 Level 3 Reflection Process

The goal is to create a comprehensive `memory-bank/reflection/reflection-[feature_id].md` document.

```mermaid
graph TD
    StartReflect["Start L3 Reflection"] -->
    ReviewDocs["1. Review All Gathered Documentation"] -->
    AssessOutcome["2. Assess Overall Feature Outcome<br>Did it meet all requirements from tasks.md? Was it successful?"] -->
    AnalyzePlan["3. Analyze Planning Phase Effectiveness<br>Was planning-comprehensive.mdc guidance effective? Was the plan accurate? Scope creep?"] -->
    AnalyzeCreative["4. Analyze Creative Phase(s) Effectiveness<br>Were design decisions sound? Did they translate well to implementation? Issues?"] -->
    AnalyzeImpl["5. Analyze Implementation Phase<br>What went well? Challenges? Bottlenecks? Adherence to design/style guide?"] -->
    AnalyzeTesting["6. Analyze Testing Phase<br>Were tests adequate? Bugs found post-release (if applicable)? Test coverage feel right?"] -->
    IdentifyLessons["7. Identify Key Lessons Learned<br>(Technical, Process, Teamwork, Estimation)"] -->
    ProposeImprovements["8. Propose Actionable Improvements<br>For future L3 feature development"] -->
    DraftReflectionDoc["9. Draft `reflection-[feature_id].md`<br>Using structured template"] -->
    FinalizeReflection["10. Finalize & Save Reflection Document"] -->
    UpdateTasksStatus["11. Update `tasks.md`<br>Mark L3 Reflection Complete"] -->
    ReflectionDone["L3 Reflection Complete<br>Ready for ARCHIVE Mode"]

    style StartReflect fill:#ba68c8,stroke:#9c27b0
    style ReflectionDone fill:#d1c4e9,stroke:#b39ddb
````

## 📝 Structure for `memory-bank/reflection-[feature_id].md`

  * **Feature Name & ID:**
  * **Date of Reflection:**
  * **Brief Feature Summary:** (What was built?)
  * **1. Overall Outcome & Requirements Alignment:**
      * How well did the final feature meet the initial requirements?
      * Were there any deviations from the original scope? If so, why?
      * What is the overall assessment of the feature's success?
  * **2. Planning Phase Review:**
      * How effective was the guidance from `Level3/planning-comprehensive.mdc`?
      * Was the initial plan in `tasks.md` (component breakdown, strategy, risks) accurate and helpful?
      * What could have been planned better? Were estimations (if made) accurate?
  * **3. Creative Phase(s) Review (if applicable):**
      * Were the right aspects flagged for CREATIVE mode?
      * How effective were the design decisions made in `creative-*.md` documents?
      * Did these designs translate well into practical implementation? Any friction points?
      * Was `memory-bank/style-guide.md` clear and sufficient for UI aspects?
  * **4. Implementation Phase Review:**
      * What were the major successes during implementation? (e.g., efficient module development, good use of libraries)
      * What were the biggest challenges or roadblocks? How were they overcome?
      * Were there any unexpected technical difficulties or complexities?
      * How was adherence to the style guide and coding standards?
  * **5. Testing Phase Review:**
      * Was the testing strategy (unit, integration, E2E for the feature) effective?
      * Did testing uncover significant issues early enough?
      * What could improve the testing process for similar features?
  * **6. What Went Well? (Highlight 3-5 key positives across all phases for this feature)**
  * **7. What Could Have Been Done Differently? (Identify 3-5 areas for improvement)**
  * **8. Key Lessons Learned:**
      * **Technical:** New insights about technologies, patterns, or architecture used for this feature.
      * **Process:** Insights about the L3 workflow, communication, task management.
      * **Estimation (if applicable):** Lessons about estimating work for features of this scale.
  * **9. Actionable Improvements for Future L3 Features:** (Specific suggestions)

## 🎯 Focus Areas for L3 Reflection

  * **Feature Scope Management:** Was the scope well-defined and managed?
  * **Integration Complexity:** Challenges or successes in integrating the feature with the existing application.
  * **Design-to-Implementation Fidelity:** How closely did the final product match the designs?
  * **Cross-Component Impact:** Understanding the ripple effects of the feature.
