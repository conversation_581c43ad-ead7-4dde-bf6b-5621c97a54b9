---
description: Visual process map for BUILD mode (Code Implementation)
globs: implementation-mode-map.mdc
alwaysApply: false
---

# BUILD MODE: CODE EXECUTION PROCESS MAP

> **TL;DR:** This visual map guides the BUILD mode process, focusing on efficient code implementation based on the planning and creative phases, with proper command execution and progress tracking.

## 🧭 BUILD MODE PROCESS FLOW

```mermaid
graph TD
    Start["START BUILD MODE"] --> ReadDocs["Read Reference Documents<br>Core/command-execution.md"]
    
    %% Initialization
    ReadDocs --> CheckLevel{"Determine<br>Complexity Level<br>from tasks.md"}
    
    %% Level 1 Implementation
    CheckLevel -->|"Level 1<br>Quick Bug Fix"| L1Process["LEVEL 1 PROCESS<br>Level1/quick-bug-workflow.md"]
    L1Process --> L1Review["Review Bug<br>Report"]
    L1Review --> L1Examine["Examine<br>Relevant Code"]
    L1Examine --> L1Fix["Implement<br>Targeted Fix"]
    L1Fix --> L1Test["Test<br>Fix"]
    L1Test --> L1Update["Update<br>tasks.md"]
    
    %% Level 2 Implementation
    CheckLevel -->|"Level 2<br>Simple Enhancement"| L2Process["LEVEL 2 PROCESS<br>Level2/enhancement-workflow.md"]
    L2Process --> L2Review["Review Build<br>Plan"]
    L2Review --> L2Examine["Examine Relevant<br>Code Areas"]
    L2Examine --> L2Implement["Implement Changes<br>Sequentially"]
    L2Implement --> L2Test["Test<br>Changes"]
    L2Test --> L2Update["Update<br>tasks.md"]
    
    %% Level 3-4 Implementation
    CheckLevel -->|"Level 3-4<br>Feature/System"| L34Process["LEVEL 3-4 PROCESS<br>Level3/feature-workflow.md<br>Level4/system-workflow.md"]
    L34Process --> L34Review["Review Plan &<br>Creative Decisions"]
    L34Review --> L34Phase{"Creative Phase<br>Documents<br>Complete?"}
    
    L34Phase -->|"No"| L34Error["ERROR:<br>Return to CREATIVE Mode"]
    L34Phase -->|"Yes"| L34DirSetup["Create Directory<br>Structure"]
    L34DirSetup --> L34VerifyDirs["VERIFY Directories<br>Created Successfully"]
    L34VerifyDirs --> L34Implementation["Build<br>Phase"]
    
    %% Implementation Phases
    L34Implementation --> L34Phase1["Phase 1<br>Build"]
    L34Phase1 --> L34VerifyFiles["VERIFY Files<br>Created Successfully"]
    L34VerifyFiles --> L34Test1["Test<br>Phase 1"]
    L34Test1 --> L34Document1["Document<br>Phase 1"]
    L34Document1 --> L34Next1{"Next<br>Phase?"}
    L34Next1 -->|"Yes"| L34Implementation
    
    L34Next1 -->|"No"| L34Integration["Integration<br>Testing"]
    L34Integration --> L34Document["Document<br>Integration Points"]
    L34Document --> L34Update["Update<br>tasks.md"]
    
    %% Command Execution
    L1Fix & L2Implement & L34Phase1 --> CommandExec["COMMAND EXECUTION<br>Core/command-execution.md"]
    CommandExec --> DocCommands["Document Commands<br>& Results"]
    
    %% Completion & Transition
    L1Update & L2Update & L34Update --> VerifyComplete["Verify Build<br>Complete"]
    VerifyComplete --> UpdateProgress["Update progress.md<br>with Status"]
    UpdateProgress --> Transition["NEXT MODE:<br>REFLECT MODE"]
```

## 📋 REQUIRED FILE STATE VERIFICATION

Before implementation can begin, verify file state:

```mermaid
graph TD
    Start["File State<br>Verification"] --> CheckTasks{"tasks.md has<br>planning complete?"}
    
    CheckTasks -->|"No"| ErrorPlan["ERROR:<br>Return to PLAN Mode"]
    CheckTasks -->|"Yes"| CheckLevel{"Task<br>Complexity?"}
    
    CheckLevel -->|"Level 1"| L1Ready["Ready for<br>Implementation"]
    
    CheckLevel -->|"Level 2"| L2Ready["Ready for<br>Implementation"]
    
    CheckLevel -->|"Level 3-4"| CheckCreative{"Creative phases<br>required?"}
    
    CheckCreative -->|"No"| L34Ready["Ready for<br>Implementation"]
    CheckCreative -->|"Yes"| VerifyCreative{"Creative phases<br>completed?"}
    
    VerifyCreative -->|"No"| ErrorCreative["ERROR:<br>Return to CREATIVE Mode"]
    VerifyCreative -->|"Yes"| L34Ready
```

## 🔄 FILE SYSTEM VERIFICATION PROCESS

```mermaid
graph TD
    Start["Start File<br>Verification"] --> CheckDir["Check Directory<br>Structure"]
    CheckDir --> DirResult{"Directories<br>Exist?"}
    
    DirResult -->|"No"| ErrorDir["❌ ERROR:<br>Missing Directories"]
    DirResult -->|"Yes"| CheckFiles["Check Each<br>Created File"]
    
    ErrorDir --> FixDir["Fix Directory<br>Structure"]
    FixDir --> CheckDir
    
    CheckFiles --> FileResult{"All Files<br>Exist?"}
    FileResult -->|"No"| ErrorFile["❌ ERROR:<br>Missing/Wrong Path Files"]
    FileResult -->|"Yes"| Complete["✅ Verification<br>Complete"]
    
    ErrorFile --> FixFile["Fix File Paths<br>or Recreate Files"]
    FixFile --> CheckFiles
```

## 📋 DIRECTORY VERIFICATION STEPS

Before beginning any file creation:

```
✓ DIRECTORY VERIFICATION PROCEDURE
1. Create all directories first before any files
2. Use ABSOLUTE paths: /full/path/to/directory
3. Verify each directory after creation:
   ls -la /full/path/to/directory     # Linux/Mac
   dir "C:\full\path\to\directory"    # Windows
4. Document directory structure in progress.md
5. Only proceed to file creation AFTER verifying ALL directories exist
```

## 📋 FILE CREATION VERIFICATION

After creating files:

```
✓ FILE VERIFICATION PROCEDURE
1. Use ABSOLUTE paths for all file operations: /full/path/to/file.ext
2. Verify each file creation was successful:
   ls -la /full/path/to/file.ext     # Linux/Mac
   dir "C:\full\path\to\file.ext"    # Windows 
3. If verification fails:
   a. Check for path resolution issues
   b. Verify directory exists
   c. Try creating with corrected path
   d. Recheck file exists after correction
4. Document all file paths in progress.md
```

## 🔄 COMMAND EXECUTION WORKFLOW

```mermaid
graph TD
    Start["Command<br>Execution"] --> Analyze["Analyze Command<br>Requirements"]
    Analyze --> Complexity{"Command<br>Complexity?"}
    
    Complexity -->|"Simple"| Simple["Execute<br>Single Command"]
    Complexity -->|"Moderate"| Chain["Use Efficient<br>Command Chaining"]
    Complexity -->|"Complex"| Break["Break Into<br>Logical Steps"]
    
    Simple & Chain & Break --> Verify["Verify<br>Results"]
    Verify --> Document["Document<br>Command & Result"]
    Document --> Next["Next<br>Command"]
```

## 📋 LEVEL-SPECIFIC BUILD APPROACHES

```mermaid
graph TD
    subgraph "Level 1: Quick Bug Fix"
        L1A["Targeted Code<br>Examination"]
        L1B["Minimal<br>Change Scope"]
        L1C["Direct<br>Fix"]
        L1D["Verify<br>Fix"]
    end
    
    subgraph "Level 2: Enhancement"
        L2A["Sequential<br>Build"]
        L2B["Contained<br>Changes"]
        L2C["Standard<br>Testing"]
        L2D["Component<br>Documentation"]
    end
    
    subgraph "Level 3-4: Feature/System"
        L3A["Directory<br>Structure First"]
        L3B["Verify Dirs<br>Before Files"]
        L3C["Phased<br>Build"]
        L3D["Verify Files<br>After Creation"]
        L3E["Integration<br>Testing"]
        L3F["Detailed<br>Documentation"]
    end
    
    L1A --> L1B --> L1C --> L1D
    L2A --> L2B --> L2C --> L2D
    L3A --> L3B --> L3C --> L3D --> L3E --> L3F
```

## 📝 BUILD DOCUMENTATION FORMAT

Document builds with:

```
## Build: [Component/Feature]

### Approach
[Brief description of build approach]

### Directory Structure
- [/absolute/path/to/dir1/]: [Purpose]
- [/absolute/path/to/dir2/]: [Purpose]

### Code Changes
- [/absolute/path/to/file1.ext]: [Description of changes]
- [/absolute/path/to/file2.ext]: [Description of changes]

### Verification Steps
- [✓] Directory structure created and verified
- [✓] All files created in correct locations
- [✓] File content verified

### Commands Executed
```
[Command 1]
[Result]
```

```
[Command 2]
[Result]
```

### Testing
- [Test 1]: [Result]
- [Test 2]: [Result]

### Status
- [x] Build complete
- [x] Testing performed
- [x] File verification completed
- [ ] Documentation updated
```

## 📊 TASKS.MD UPDATE FORMAT

During the build process, update tasks.md with progress:

```
## Status
- [x] Initialization complete
- [x] Planning complete
[For Level 3-4:]
- [x] Creative phases complete
- [x] Directory structure created and verified
- [x] [Built component 1]
- [x] [Built component 2]
- [ ] [Remaining component]

## Build Progress
- [Component 1]: Complete
  - Files: [/absolute/path/to/files]
  - [Details about implementation]
- [Component 2]: Complete
  - Files: [/absolute/path/to/files]
  - [Details about implementation]
- [Component 3]: In Progress
  - [Current status]
```

## 📋 PROGRESS.MD UPDATE FORMAT

Update progress.md with:

```
# Build Progress

## Directory Structure
- [/absolute/path/to/dir1/]: Created and verified
- [/absolute/path/to/dir2/]: Created and verified

## [Date]: [Component/Feature] Built
- **Files Created**: 
  - [/absolute/path/to/file1.ext]: Verified
  - [/absolute/path/to/file2.ext]: Verified
- **Key Changes**: 
  - [Change 1]
  - [Change 2]
- **Testing**: [Test results]
- **Next Steps**: [What comes next]
```

## 📊 BUILD VERIFICATION CHECKLIST

```
✓ BUILD VERIFICATION
- Directory structure created correctly? [YES/NO]
- All files created in correct locations? [YES/NO]
- All file paths verified with absolute paths? [YES/NO]
- All planned changes implemented? [YES/NO]
- Testing performed for all changes? [YES/NO]
- Code follows project standards? [YES/NO]
- Edge cases handled appropriately? [YES/NO]
- Build documented with absolute paths? [YES/NO]
- tasks.md updated with progress? [YES/NO]
- progress.md updated with details? [YES/NO]

→ If all YES: Build complete - ready for REFLECT mode
→ If any NO: Complete missing build elements
```

## 🔄 MODE TRANSITION NOTIFICATION

When the build is complete, notify user with:

```
## BUILD COMPLETE

✅ Directory structure verified
✅ All files created in correct locations
✅ All planned changes implemented
✅ Testing performed successfully
✅ tasks.md updated with status
✅ progress.md updated with details

→ NEXT RECOMMENDED MODE: REFLECT MODE
``` 