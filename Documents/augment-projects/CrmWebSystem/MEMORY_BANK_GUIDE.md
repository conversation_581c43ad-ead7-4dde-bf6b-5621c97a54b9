# CRM Web System - Memory Bank Integration Guide

## Overview

This project now includes the Cursor Memory Bank system, a sophisticated workflow management system that provides structured approaches to development tasks through specialized modes.

## Available Modes

### 🚀 PLAN MODE
**Purpose**: Create detailed implementation plans and break down complex tasks

**When to Use**:
- Starting a new feature
- Breaking down complex requirements
- Planning multi-phase implementations
- Architectural decision planning

**How to Activate**:
```
Please load .cursor/custom_modes/plan_instructions.md and help me plan [describe your task]
```

**Example Usage**:
- Planning the task detail pages feature
- Breaking down the search and filtering system
- Planning database optimization strategy

### 🎨 CREATIVE MODE
**Purpose**: Explore design options and make architectural decisions

**When to Use**:
- Designing new UI components
- Exploring algorithm options
- Making architectural decisions
- Solving complex design problems

**How to Activate**:
```
Please load .cursor/custom_modes/creative_instructions.md and help me design [describe your component]
```

**Example Usage**:
- Designing the task detail page layout
- Exploring search algorithm options
- Deciding on real-time notification architecture

### 🔨 IMPLEMENT MODE
**Purpose**: Systematically build planned features

**When to Use**:
- Have a clear implementation plan
- Ready to write code systematically
- Building planned features
- Following established patterns

**How to Activate**:
```
Please load .cursor/custom_modes/implement_instructions.md and help me build [describe your task]
```

**Example Usage**:
- Building the settings page (completed)
- Implementing TypeScript error fixes
- Building the task detail pages

### 🔍 REFLECT MODE
**Purpose**: Review code quality and optimize performance

**When to Use**:
- Code review and optimization
- Performance analysis
- Quality assessment
- Post-implementation review

**How to Activate**:
```
Please load .cursor/custom_modes/reflect_archive_instructions.md and help me review [describe your code]
```

**Example Usage**:
- Reviewing the current codebase for optimization
- Analyzing performance bottlenecks
- Code quality assessment

### 🧠 VAN MODE
**Purpose**: General analysis and problem-solving

**When to Use**:
- General problem analysis
- Understanding complex issues
- Research and investigation
- Brainstorming sessions

**How to Activate**:
```
Please load .cursor/custom_modes/van_instructions.md and help me analyze [describe your problem]
```

## Project-Specific Workflow

### Current Development Flow

1. **Check Current Status**
   - Review `tasks.md` for current priorities
   - Check `implementation-plan.md` for detailed strategies
   - Understand current complexity level (Level 3)

2. **Select Appropriate Mode**
   - **High Priority Tasks** (TypeScript errors): Use IMPLEMENT mode
   - **New Features** (Task details, Search): Use PLAN → CREATIVE → IMPLEMENT
   - **Code Quality**: Use REFLECT mode
   - **General Issues**: Use VAN mode

3. **Follow Mode Guidelines**
   - Each mode has specific steps and documentation requirements
   - Always update `tasks.md` with progress
   - Document decisions in `implementation-plan.md`

### Example Workflows

#### Fixing TypeScript Errors (Current Priority)
```
1. VAN MODE: Analyze the 13 TypeScript errors
2. IMPLEMENT MODE: Systematically fix each error
3. REFLECT MODE: Review fixes and ensure quality
```

#### Adding Task Detail Pages (Future Feature)
```
1. PLAN MODE: Break down requirements and create implementation plan
2. CREATIVE MODE: Design UI/UX and explore routing options
3. IMPLEMENT MODE: Build the feature systematically
4. REFLECT MODE: Review and optimize the implementation
```

## File Structure

```
.cursor/
└── custom_modes/
    ├── plan_instructions.md          # Plan mode instructions
    ├── creative_instructions.md      # Creative mode instructions
    ├── implement_instructions.md     # Implement mode instructions
    ├── reflect_archive_instructions.md # Reflect mode instructions
    └── van_instructions.md           # Van mode instructions

cursor-memory-bank/                   # Original memory bank files (reference)
tasks.md                             # Current task status and priorities
implementation-plan.md               # Detailed implementation strategies
.cursorrules                        # Cursor configuration with memory bank rules
```

## Key Project Files

### `tasks.md`
- Current project status and complexity level
- Active, in-progress, and completed tasks
- Technical debt and issues tracking
- Development guidelines and metrics

### `implementation-plan.md`
- Detailed implementation strategies for each phase
- Components requiring creative phases
- Risk assessment and success metrics
- Testing and deployment considerations

### `.cursorrules`
- Project context and technology stack
- Mode selection guidelines
- Workflow integration instructions
- Project status tracking

## Best Practices

### Mode Selection
1. **Start with VAN mode** if you're unsure about the problem
2. **Use PLAN mode** for any task marked as Level 3+ complexity
3. **Use CREATIVE mode** for components flagged in implementation plans
4. **Use IMPLEMENT mode** when you have clear, actionable plans
5. **Use REFLECT mode** after completing any significant work

### Documentation
- Always read `tasks.md` before starting any work
- Update task status after completing work
- Document design decisions in implementation plans
- Keep complexity levels updated

### Quality Assurance
- Use REFLECT mode for code review
- Verify TypeScript compliance
- Test responsive design
- Check accessibility compliance

## Current Project Status

### Immediate Priorities
1. **TypeScript Error Resolution** (13 errors) - Use IMPLEMENT mode
2. **Performance Optimization** - Use REFLECT mode for analysis
3. **Code Quality Improvements** - Use REFLECT mode

### Upcoming Features
1. **Task Detail Pages** - Use PLAN → CREATIVE → IMPLEMENT flow
2. **Advanced Search** - Use PLAN → CREATIVE → IMPLEMENT flow
3. **Real-time Features** - Use PLAN → CREATIVE → IMPLEMENT flow

### Technology Stack
- Next.js 15, TypeScript, Tailwind CSS
- Prisma ORM, PostgreSQL
- NextAuth.js, Framer Motion
- Custom UI components with hackathon.dev design

## Getting Started

1. **Read the current status**: Check `tasks.md` and `implementation-plan.md`
2. **Identify your task**: Determine what you want to work on
3. **Select the appropriate mode**: Use the guidelines above
4. **Activate the mode**: Load the corresponding instruction file
5. **Follow the mode workflow**: Each mode has specific steps
6. **Update documentation**: Keep `tasks.md` and plans current

## Tips for Success

- **Be specific** when describing tasks to the AI
- **Follow the mode workflows** - they're designed for optimal results
- **Document decisions** - future you will thank you
- **Use complexity levels** - they help determine the right approach
- **Iterate through modes** - complex features often need multiple modes

## Support

If you need help with the Memory Bank system:
1. Check the original documentation in `cursor-memory-bank/`
2. Review the mode instruction files in `.cursor/custom_modes/`
3. Use VAN mode for general analysis and guidance
