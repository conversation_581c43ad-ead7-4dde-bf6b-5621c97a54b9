// Import verwijderd omdat 'jan-ai' niet bestaat

/**
 * Interface voor de parameters van de generateText functie
 */
interface GenerateTextProps {
  /** De prompt die naar het model wordt gestuurd */
  prompt: string;
  /** De naam van het te gebruiken model */
  model: string;
  /** Maximum aantal tokens in de gegenereerde tekst (default: 100) */
  maxTokens?: number;
  /** Temperatuur voor de generatie (default: 0.7) */
  temperature?: number;
}

/**
 * Interface voor het resultaat van de generateText functie
 */
interface GenerateTextResult {
  /** De gegenereerde tekst */
  text: string;
  /** Of de generatie succesvol was */
  success: boolean;
  /** Eventuele foutmelding */
  error?: string;
}

/**
 * Genereert tekst met behulp van Jan.ai
 * @param props - De parameters voor tekstgeneratie
 * @returns Een promise met het generatieresultaat
 */
export async function generateText({
  prompt,
  model,
  maxTokens = 100,
  temperature = 0.7,
}: GenerateTextProps): Promise<GenerateTextResult> {
  if (!process.env.JAN_AI_API_URL) {
    return {
      text: '',
      success: false,
      error: 'JAN_AI_API_URL is niet geconfigureerd in de omgevingsvariabelen',
    };
  }
      error: 'JAN_AI_API_URL is niet geconfigureerd in de omgevingsvariabelen',
    };
  const openai = new openai({
    baseURL: process.env.JAN_AI_API_URL,
    apiKey: 'sk-no-key-required', // Jan.ai heeft geen API sleutel nodig voor lokale modellen
  });

    const completion = await openai.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: maxTokens,
      temperature,
    });

    if (!completion.choices?.[0]?.message?.content) {
      return { text: '', success: false, error: 'Geen tekst gegenereerd.' };
    }

    return { text: completion.choices[0].message.content, success: true };
  } catch (error: any) {
    console.error('Fout bij het genereren van tekst met Jan.ai:', error);
    return {
      text: '',
      success: false,
      error: error.message || 'Onbekende fout bij het genereren van tekst.',
    };
  }
}

// Voorbeeld van gebruik (optioneel, kan verwijderd worden in productie)
// async function runExample() {
//   const response = await generateText({
//     prompt: 'Schrijf een korte marketingtekst over een nieuw CRM systeem.',
//     model: 'llama-3-8b-instruct', // Vervang dit door de naam van jouw gedownloade model in Jan.ai
//     maxTokens: 150,
//   });

//   if (response.success) {
//     console.log('Gegenereerde tekst:', response.text);
//   } else {
//     console.error('Fout:', response.error);
//   }
// }

// runExample();
